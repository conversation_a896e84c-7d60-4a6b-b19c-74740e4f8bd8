-- =============================================
-- 创建遗漏的重要表结构脚本 (第五部分)
-- 功能：创建仓库管理系统中遗漏的重要表结构
-- 用途：补充完整的表结构，包括策略、计费、任务管理等
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 订单选择和波次管理表
-- =============================================

-- OrderSelection 表 (订单选择表)
CREATE TABLE OrderSelection
(OrderSelectionKey        char(10) NOT NULL,
DefaultFlag              char(10) NOT NULL
CONSTRAINT DF_OS_DefaultFlag DEFAULT "0" ,
OrderDateStart           datetime NOT NULL
CONSTRAINT DF_OS_OrderDateStart DEFAULT 'JAN 01 1900' ,
OrderDateEnd             datetime NOT NULL
CONSTRAINT DF_OS_OrderDateEnd DEFAULT 'JAN 01 2100' ,
DeliveryDateStart        datetime NOT NULL
CONSTRAINT DF_OS_DeliveryDateStart DEFAULT 'JAN 01 1900' ,
DeliveryDateEnd          datetime NOT NULL
CONSTRAINT DF_OS_DeliveryDateEnd DEFAULT 'JAN 01 2100' ,
StorerKeyStart           char(15) NOT NULL
CONSTRAINT DF_OS_StorerKeyStart DEFAULT "" ,
StorerKeyEnd             char(15) NOT NULL
CONSTRAINT DF_OS_StorerKeyEnd DEFAULT "ZZZZZZZZZZZZZZZ" ,
OrderKeyStart            char(10) NOT NULL
CONSTRAINT DF_OS_OrderKeyStart DEFAULT "" ,
OrderKeyEnd              char(10) NOT NULL
CONSTRAINT DF_OS_OrderKeyEnd DEFAULT "ZZZZZZZZZZ" ,
ConsigneeKeyStart        char(15) NOT NULL
CONSTRAINT DF_OS_ConsigneeKeyStart DEFAULT "" ,
ConsigneeKeyEnd          char(15) NOT NULL
CONSTRAINT DF_OS_ConsigneeKeyEnd DEFAULT "ZZZZZZZZZZZZZZZ" ,
CarrierKeyStart          char(15) NOT NULL
CONSTRAINT DF_OS_CarrierKeyStart DEFAULT "" ,
CarrierKeyEnd            char(15) NOT NULL
CONSTRAINT DF_OS_CarrierKeyEnd DEFAULT "ZZZZZZZZZZZZZZZ" ,
RouteStart               char(10) NOT NULL
CONSTRAINT DF_OS_RouteStart DEFAULT "" ,
RouteEnd                 char(10) NOT NULL
CONSTRAINT DF_OS_RouteEnd DEFAULT "ZZZZZZZZZZ" ,
StopStart                char(10) NOT NULL
CONSTRAINT DF_OS_StopStart DEFAULT "" ,
StopEnd                  char(10) NOT NULL
CONSTRAINT DF_OS_StopEnd DEFAULT "ZZZZZZZZZZ" ,
DoorStart                char(10) NOT NULL
CONSTRAINT DF_OS_DoorStart DEFAULT "" ,
DoorEnd                  char(10) NOT NULL
CONSTRAINT DF_OS_DoorEnd DEFAULT "ZZZZZZZZZZ" ,
TypeStart                char(10) NOT NULL
CONSTRAINT DF_OS_TypeStart DEFAULT "" ,
TypeEnd                  char(10) NOT NULL
CONSTRAINT DF_OS_TypeEnd DEFAULT "ZZZZZZZZZZ" ,
OrderGroupStart          char(20) NOT NULL
CONSTRAINT DF_OS_OrderGroupStart DEFAULT "" ,
OrderGroupEnd            char(20) NOT NULL
CONSTRAINT DF_OS_OrderGroupEnd DEFAULT "ZZZZZZZZZZZZZZZZZZZZ" ,
IntermodalVehicleStart   char(10) NOT NULL
CONSTRAINT DF_OS_IntermodalVehicleStart DEFAULT "" ,
IntermodalVehicleEnd     char(10) NOT NULL
CONSTRAINT DF_OS_IntermodalVehicleEnd DEFAULT "ZZZZZZZZZZ" ,
StatusStart              char(10) NOT NULL
CONSTRAINT DF_OS_StatusStart DEFAULT "" ,
StatusEnd                char(10) NOT NULL
CONSTRAINT DF_OS_StatusEnd DEFAULT "ZZZZZZZZZZ" ,
PriorityStart            char(10) NOT NULL
CONSTRAINT DF_OS_PriorityStart DEFAULT "" ,
PriorityEnd              char(10) NOT NULL
CONSTRAINT DF_OS_PriorityEnd DEFAULT "ZZZZZZZZZZ" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_OS_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_OS_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_OS_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_OS_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('OrderSelection') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE OrderSelection FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE OrderSelection>>>'
GRANT INSERT ON OrderSelection TO nsql
GRANT UPDATE ON OrderSelection TO nsql
GRANT DELETE ON OrderSelection TO nsql
GRANT SELECT ON OrderSelection TO nsql
END
GO

-- =============================================
-- 事务头表
-- =============================================

-- ITRNHDR 表 (事务头表)
CREATE TABLE ITRNHDR
(HeaderType               char(2)  NOT NULL,
ItrnKey                  char(10) NOT NULL ,
HeaderKey                char(10) NOT NULL ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_ITRNHDR_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_ITRNHDR_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_ITRNHDR_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_ITRNHDR_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('ITRNHDR') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ITRNHDR FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ITRNHDR>>>'
GRANT INSERT ON ITRNHDR TO nsql
GRANT UPDATE ON ITRNHDR TO nsql
GRANT DELETE ON ITRNHDR TO nsql
GRANT SELECT ON ITRNHDR TO nsql
END
GO

-- =============================================
-- 运营处理表
-- =============================================

-- OP_CARTONLINES 表 (装箱行表)
CREATE TABLE OP_CARTONLINES
(
Cartonbatch char(10) NOT NULL,
PickDetailKey char(10) NOT NULL,
PickHeaderKey char(10) NULL,
OrderKey char(10) NULL,
OrderLineNumber char(5) NULL,
Storerkey char(15) NULL,
Sku char(20) NULL,
Lot char(10) NULL,
Loc char(10) NULL,
Id char(18) NULL,
Qty int NULL,
CaseId char(10) NULL,
CartonType char(10) NULL,
CartonGroup char(10) NULL,
AddDate datetime NOT NULL
CONSTRAINT DF_OP_CARTONLINES_AddDate DEFAULT CURRENT_Timestamp ,
AddWho char(18) NOT NULL
CONSTRAINT DF_OP_CARTONLINES_AddWho DEFAULT USER ,
EditDate datetime NOT NULL
CONSTRAINT DF_OP_CARTONLINES_EditDate DEFAULT CURRENT_Timestamp ,
EditWho char(18) NOT NULL
CONSTRAINT DF_OP_CARTONLINES_EditWho DEFAULT USER ,
TrafficCop char(1) NULL,
ArchiveCop char(1) NULL
)
GO

IF OBJECT_ID('OP_CARTONLINES') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE OP_CARTONLINES FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE OP_CARTONLINES>>>'
GRANT INSERT ON OP_CARTONLINES TO nsql
GRANT UPDATE ON OP_CARTONLINES TO nsql
GRANT DELETE ON OP_CARTONLINES TO nsql
GRANT SELECT ON OP_CARTONLINES TO nsql
END
GO

-- =============================================
-- 转移管理表
-- =============================================

-- TRANSFER 表 (转移表)
CREATE TABLE TRANSFER
(TransferKey              char(10)     NOT NULL,
FromStorerKey            char(15)     NOT NULL
CONSTRAINT DF_TRANSFER_FromStorerKey    DEFAULT "",
ToStorerKey              char(15)     NOT NULL
CONSTRAINT DF_TRANSFER_ToStorerKey DEFAULT "",
ExternTransferKey        char(30)     NOT NULL
CONSTRAINT DF_TRANSFER_ExternTransferKey DEFAULT "",
TransferDate             datetime     NOT NULL
CONSTRAINT DF_TRANSFER_TransferDate DEFAULT CURRENT_TIMESTAMP,
Type                     char(10)     NOT NULL
CONSTRAINT DF_TRANSFER_Type DEFAULT "0",
Status                   char(10)     NOT NULL
CONSTRAINT DF_TRANSFER_Status DEFAULT "0"
CONSTRAINT CK_TRANSFER_Status CHECK ( Status LIKE '[0-9]' ),
Notes                    text         NULL,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_TRANSFER_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_TRANSFER_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_TRANSFER_AddWho DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_TRANSFER_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_TRANSFER_EditWho DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('TRANSFER') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TRANSFER FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TRANSFER>>>'
GRANT INSERT ON TRANSFER TO nsql
GRANT UPDATE ON TRANSFER TO nsql
GRANT DELETE ON TRANSFER TO nsql
GRANT SELECT ON TRANSFER TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- TRANSFERDETAIL 表 (转移明细表)
CREATE TABLE TRANSFERDETAIL
(TransferKey              char(10)     NOT NULL,
TransferLineNumber       char(5)      NOT NULL,
FromStorerKey            char(15)     NOT NULL
CONSTRAINT DF_TRFDET_FromStorerKey           DEFAULT "",
FromSku                  char(20)     NOT NULL
CONSTRAINT DF_TRFDET_FromSku                 DEFAULT "",
ToStorerKey              char(15)     NOT NULL
CONSTRAINT DF_TRFDET_ToStorerKey             DEFAULT "",
ToSku                    char(20)     NOT NULL
CONSTRAINT DF_TRFDET_ToSku                   DEFAULT "",
ExternTransferKey        char(30)     NOT NULL
CONSTRAINT DF_TRFDET_ExternTransferKey       DEFAULT "",
ExternLineNo             char(10)     NOT NULL
CONSTRAINT DF_TRFDET_ExternLineNo            DEFAULT "",
Qty                      int          NOT NULL
CONSTRAINT DF_TRFDET_Qty                     DEFAULT 0,
QtyTransferred           int          NOT NULL
CONSTRAINT DF_TRFDET_QtyTransferred          DEFAULT 0,
UOM                      char(10)     NOT NULL
CONSTRAINT DF_TRFDET_UOM                     DEFAULT "",
PackKey                  char(10)     NOT NULL
CONSTRAINT DF_TRFDET_PackKey                 DEFAULT "STD",
Lot                      char(10)     NOT NULL
CONSTRAINT DF_TRFDET_Lot                     DEFAULT "",
ID                       char(18)     NOT NULL
CONSTRAINT DF_TRFDET_ID                      DEFAULT "",
FromLoc                  char(10)     NOT NULL
CONSTRAINT DF_TRFDET_FromLoc                 DEFAULT "UNKNOWN",
ToLoc                    char(10)     NOT NULL
CONSTRAINT DF_TRFDET_ToLoc                   DEFAULT "UNKNOWN",
Status                   char(10)     NOT NULL
CONSTRAINT DF_TRFDET_Status                  DEFAULT "0"
CONSTRAINT CK_TRFDET_Status CHECK ( Status LIKE '[0-9]' ),
Lottable01              char(18)     NOT NULL
CONSTRAINT DF_TRFDET_LOTTABLE01              DEFAULT "",
Lottable02              char(18)     NOT NULL
CONSTRAINT DF_TRFDET_LOTTABLE02              DEFAULT "",
Lottable03              char(18)     NOT NULL
CONSTRAINT DF_TRFDET_LOTTABLE03              DEFAULT "",
Lottable04              datetime     NULL,
Lottable05              datetime     NULL,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_TRFDET_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_TRFDET_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_TRFDET_AddWho DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_TRFDET_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_TRFDET_EditWho DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('TRANSFERDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TRANSFERDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TRANSFERDETAIL>>>'
GRANT INSERT ON TRANSFERDETAIL TO nsql
GRANT UPDATE ON TRANSFERDETAIL TO nsql
GRANT DELETE ON TRANSFERDETAIL TO nsql
GRANT SELECT ON TRANSFERDETAIL TO nsql
END
GO

-- =============================================
-- 错误处理表
-- =============================================

-- HIERROR 表 (主机接口错误表)
CREATE TABLE HIERROR
(HiErrorGroup             char(10) NOT NULL,
ErrorText                varchar(254) NOT NULL,
ErrorType                char(20) NOT NULL ,
SourceKey                char(20) NOT NULL ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_HIERROR_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_HIERROR_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_HIERROR_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_HIERROR_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('HIERROR') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE HIERROR FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE HIERROR>>>'
GRANT INSERT ON HIERROR TO nsql
GRANT UPDATE ON HIERROR TO nsql
GRANT DELETE ON HIERROR TO nsql
GRANT SELECT ON HIERROR TO nsql
END
GO

-- =============================================
-- 标签管理表
-- =============================================

-- LABELLIST 表 (标签列表表)
CREATE TABLE LABELLIST
(
LabelName           char(30)       NOT NULL
CONSTRAINT DF_LABELLIST_LabelName DEFAULT "" ,
LabelDesc           char(60)       NOT NULL
CONSTRAINT DF_LABELLIST_LabelDesc DEFAULT "" ,
LabelType           char(10)       NOT NULL
CONSTRAINT DF_LABELLIST_LabelType DEFAULT "" ,
LabelFormat         char(30)       NOT NULL
CONSTRAINT DF_LABELLIST_LabelFormat DEFAULT "" ,
LabelPrinter        char(30)       NOT NULL
CONSTRAINT DF_LABELLIST_LabelPrinter DEFAULT "" ,
AddDate             datetime       NOT NULL
CONSTRAINT DF_LABELLIST_AddDate DEFAULT CURRENT_Timestamp ,
AddWho              char(18)       NOT NULL
CONSTRAINT DF_LABELLIST_AddWho DEFAULT USER ,
EditDate            datetime       NOT NULL
CONSTRAINT DF_LABELLIST_EditDate DEFAULT CURRENT_Timestamp ,
EditWho             char(18)       NOT NULL
CONSTRAINT DF_LABELLIST_EditWho DEFAULT USER ,
TrafficCop          char(1)        NULL,
ArchiveCop          char(1)        NULL
)
GO

IF OBJECT_ID('LABELLIST') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LABELLIST FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LABELLIST>>>'
GRANT INSERT ON LABELLIST TO nsql
GRANT UPDATE ON LABELLIST TO nsql
GRANT DELETE ON LABELLIST TO nsql
GRANT SELECT ON LABELLIST TO nsql
END
GO

-- 注意：此脚本包含遗漏的重要表结构
-- 更多表结构将在后续部分添加
