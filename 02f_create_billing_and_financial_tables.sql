-- =============================================
-- 创建计费和财务表结构脚本 (第六部分)
-- 功能：创建仓库管理系统的计费和财务相关表结构
-- 用途：计费管理、税务、会计、费率等专业模块
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 会计和财务表
-- =============================================

-- ChartOfAccounts 表 (会计科目表)
CREATE TABLE ChartOfAccounts
(ChartofAccountsKey char(30)  NOT NULL,
Descrip             char(30) NOT NULL
CONSTRAINT DF_ChartOfAccts_Descrip   DEFAULT "",
SupportFlag         char(1) NOT NULL
CONSTRAINT DF_ChartOfAccts_SupportFlag   DEFAULT "A"
CONSTRAINT CK_ChartOfAccts_SupportFlag   CHECK (SupportFLag in ("A", "I", "D")),
AddDate             datetime  NOT NULL
CONSTRAINT DF_ChartOfAccts_AddDate   DEFAULT CURRENT_Timestamp,
AddWho              char(18)  NOT NULL
CONSTRAINT DF_ChartOfAccts_AddWho    DEFAULT USER ,
EditDate            datetime  NOT NULL
CONSTRAINT DF_ChartOfAccts_EditDate  DEFAULT CURRENT_Timestamp,
EditWho             char(18)  NOT NULL
CONSTRAINT DF_ChartOfAccts_EditWho   DEFAULT USER,
TrafficCop          char(1)   NULL
)
GO

IF OBJECT_ID('ChartOfAccounts') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ChartOfAccounts FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ChartOfAccounts>>>'
GRANT INSERT ON ChartOfAccounts TO nsql
GRANT UPDATE ON ChartOfAccounts TO nsql
GRANT DELETE ON ChartOfAccounts TO nsql
GRANT SELECT ON ChartOfAccounts TO nsql
END
GO

-- GLDistribution 表 (总账分配表)
CREATE TABLE GLDistribution
(GLDistributionKey  char(10)  NOT NULL,
SupportFlag         char(1) NOT NULL
CONSTRAINT DF_GLDistribution_SupportFlag   DEFAULT "A"
CONSTRAINT CK_GLDistribution_SupportFlag   CHECK (SupportFLag in ("A", "I", "D")),
Descrip             char(30) NOT NULL
CONSTRAINT DF_GLDistribution_Descrip       DEFAULT "",
AddDate             datetime  NOT NULL
CONSTRAINT DF_GLDistribution_AddDate       DEFAULT CURRENT_Timestamp,
AddWho              char(18)  NOT NULL
CONSTRAINT DF_GLDistribution_AddWho        DEFAULT USER,
EditDate            datetime  NOT NULL
CONSTRAINT DF_GLDistribution_EditDate      DEFAULT CURRENT_Timestamp,
EditWho             char(18)  NOT NULL
CONSTRAINT DF_GLDistribution_EditWho       DEFAULT USER,
TrafficCop          char(1)   NULL
)
GO

IF OBJECT_ID('GLDistribution') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE GLDistribution FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE GLDistribution>>>'
GRANT INSERT ON GLDistribution TO nsql
GRANT UPDATE ON GLDistribution TO nsql
GRANT DELETE ON GLDistribution TO nsql
GRANT SELECT ON GLDistribution TO nsql
END
GO

-- GLDistributionDetail 表 (总账分配明细表)
CREATE TABLE GLDistributionDetail
(GLDistributionKey        char(10)      NOT NULL,
GLDistributionLineNumber char(5)       NOT NULL
CONSTRAINT DF_GLDistDetail_GLDistLineNbr DEFAULT "",
ChartofAccountsKey       char(30)      NOT NULL
CONSTRAINT DF_GLDistDetail_COAKey        DEFAULT "XXXXXXXXXX",
GLDistributionPct        decimal(12,6) NOT NULL
CONSTRAINT DF_GLDistDetail_GLDistPct     DEFAULT 0.0,
Descrip                  char(30)      NOT NULL
CONSTRAINT DF_GLDistDetail_Descrip       DEFAULT "",
AddDate                  datetime      NOT NULL
CONSTRAINT DF_GLDistDetail_AddDate       DEFAULT CURRENT_Timestamp,
AddWho                   char(18)      NOT NULL
CONSTRAINT DF_GLDistDetail_AddWho        DEFAULT USER,
EditDate                 datetime      NOT NULL
CONSTRAINT DF_GLDistDetail_EditDate      DEFAULT CURRENT_Timestamp,
EditWho                  char(18)      NOT NULL
CONSTRAINT DF_GLDistDetail_EditWho       DEFAULT USER,
TrafficCop               char(1)       NULL
)
GO

IF OBJECT_ID('GLDistributionDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE GLDistributionDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE GLDistributionDetail>>>'
GRANT INSERT ON GLDistributionDetail TO nsql
GRANT UPDATE ON GLDistributionDetail TO nsql
GRANT DELETE ON GLDistributionDetail TO nsql
GRANT SELECT ON GLDistributionDetail TO nsql
END
GO

-- =============================================
-- 税务管理表
-- =============================================

-- TaxRate 表 (税率表)
CREATE TABLE TaxRate
( TaxRateKey         char(10) NOT NULL,
TaxAuthority       char(30) NOT NULL
CONSTRAINT DF_TaxRate_TaxAuthority  DEFAULT "",
SupportFlag        char(1) NOT NULL
CONSTRAINT DF_TaxRate_SupportFlag   DEFAULT "A"
CONSTRAINT CK_TaxRate_SupportFlag   CHECK (SupportFLag in ("A", "I", "D")),
Rate               decimal (8,7) NOT NULL
CONSTRAINT DF_TaxRate_Rate DEFAULT  0.0,
ExternTaxRateKey   char(30) not NULL
CONSTRAINT DF_TaxRate_ExternTaxRateKey DEFAULT  "",
AddDate            datetime NOT NULL
CONSTRAINT DF_TaxRate_AddDate DEFAULT CURRENT_Timestamp,
AddWho             char(18) NOT NULL
CONSTRAINT DF_TaxRate_AddWho DEFAULT USER ,
EditDate           datetime NOT NULL
CONSTRAINT DF_TaxRate_EditDate DEFAULT CURRENT_Timestamp,
EditWho            char(18) NOT NULL
CONSTRAINT DF_TaxRate_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('TaxRate') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TaxRate FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TaxRate>>>'
GRANT INSERT ON TaxRate TO nsql
GRANT UPDATE ON TaxRate TO nsql
GRANT DELETE ON TaxRate TO nsql
GRANT SELECT ON TaxRate TO nsql
END
GO

-- TaxGroup 表 (税组表)
CREATE TABLE TaxGroup
( TaxGroupKey         char(10) NOT NULL,
SupportFlag         char(1) NOT NULL
CONSTRAINT DF_TaxGroup_SupportFlag   DEFAULT "A"
CONSTRAINT CK_TaxGroup_SupportFlag   CHECK (SupportFLag in ("A", "I", "D")),
Descrip             char(30) NOT NULL
CONSTRAINT DF_TaxGroup_Descrip  DEFAULT "",
AddDate            datetime NOT NULL
CONSTRAINT DF_TaxGroup_AddDate DEFAULT CURRENT_Timestamp,
AddWho             char(18) NOT NULL
CONSTRAINT DF_TaxGroup_AddWho DEFAULT USER ,
EditDate           datetime NOT NULL
CONSTRAINT DF_TaxGroup_EditDate DEFAULT CURRENT_Timestamp,
EditWho            char(18) NOT NULL
CONSTRAINT DF_TaxGroup_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('TaxGroup') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TaxGroup FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TaxGroup>>>'
GRANT INSERT ON TaxGroup TO nsql
GRANT UPDATE ON TaxGroup TO nsql
GRANT DELETE ON TaxGroup TO nsql
GRANT SELECT ON TaxGroup TO nsql
END
GO

-- TaxGroupDetail 表 (税组明细表)
CREATE TABLE TaxGroupDetail
( TaxGroupKey        char(10) NOT NULL,
TaxRateKey         char(10) NOT NULL,
GLDistributionKey  char(10) NOT NULL
CONSTRAINT DF_TaxGroupDetail_GLDist DEFAULT "XXXXXXXXXX",
AddDate            datetime NOT NULL
CONSTRAINT DF_TaxGroupDetail_AddDate DEFAULT CURRENT_Timestamp,
AddWho             char(18) NOT NULL
CONSTRAINT DF_TaxGroupDetail_AddWho DEFAULT USER ,
EditDate           datetime NOT NULL
CONSTRAINT DF_TaxGroupDetail_EditDate DEFAULT CURRENT_Timestamp,
EditWho            char(18) NOT NULL
CONSTRAINT DF_TaxGroupDetail_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('TaxGroupDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TaxGroupDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TaxGroupDetail>>>'
GRANT INSERT ON TaxGroupDetail TO nsql
GRANT UPDATE ON TaxGroupDetail TO nsql
GRANT DELETE ON TaxGroupDetail TO nsql
GRANT SELECT ON TaxGroupDetail TO nsql
END
GO

-- =============================================
-- 费率管理表
-- =============================================

-- Tariff 表 (费率表)
CREATE TABLE Tariff
( TariffKey              char(10) NOT NULL,
Descrip                char(30) NOT NULL
CONSTRAINT DF_Tariff_Descrip DEFAULT "",
SupportFlag         char(1) NOT NULL
CONSTRAINT DF_Tariff_SupportFlag   DEFAULT "A"
CONSTRAINT CK_Tariff_SupportFlag   CHECK (SupportFLag in ("A", "I", "D")),
TariffType             char(10) NOT NULL
CONSTRAINT DF_Tariff_TariffType DEFAULT "",
CurrencyKey            char(10) NOT NULL
CONSTRAINT DF_Tariff_CurrencyKey DEFAULT "USD",
GLDistributionKey      char(10) NOT NULL
CONSTRAINT DF_Tariff_GLDistributionKey DEFAULT "XXXXXXXXXX",
TaxGroupKey            char(10) NOT NULL
CONSTRAINT DF_Tariff_TaxGroupKey DEFAULT "XXXXXXXXXX",
AddDate            datetime NOT NULL
CONSTRAINT DF_Tariff_AddDate DEFAULT CURRENT_Timestamp,
AddWho             char(18) NOT NULL
CONSTRAINT DF_Tariff_AddWho DEFAULT USER ,
EditDate           datetime NOT NULL
CONSTRAINT DF_Tariff_EditDate DEFAULT CURRENT_Timestamp,
EditWho            char(18) NOT NULL
CONSTRAINT DF_Tariff_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('Tariff') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE Tariff FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE Tariff>>>'
GRANT INSERT ON Tariff TO nsql
GRANT UPDATE ON Tariff TO nsql
GRANT DELETE ON Tariff TO nsql
GRANT SELECT ON Tariff TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- TariffDetail 表 (费率明细表)
CREATE TABLE TariffDetail
( TariffDetailKey         char(10) NOT NULL,
TariffKey               char(10) NOT NULL,
ChargeType              char(10) NOT NULL
CONSTRAINT CK_TariffDetailChargeType CHECK (ChargeType in ('IS', 'RS', 'HI','HO','MI', 'AC','SP', 'DI', 'DO','MR')),
Descrip                 char(30) NOT NULL
CONSTRAINT DF_TariffDetail_Descrip DEFAULT "",
UOM                     char(10) NOT NULL
CONSTRAINT DF_TariffDetail_UOM DEFAULT "",
Rate                    decimal(12,6) NOT NULL
CONSTRAINT DF_TariffDetail_Rate DEFAULT 0.0,
MinimumCharge           decimal(12,6) NOT NULL
CONSTRAINT DF_TariffDetail_MinimumCharge DEFAULT 0.0,
MaximumCharge           decimal(12,6) NOT NULL
CONSTRAINT DF_TariffDetail_MaximumCharge DEFAULT 999999.99,
EffectiveDate           datetime NOT NULL
CONSTRAINT DF_TariffDetail_EffectiveDate DEFAULT CURRENT_Timestamp,
ExpirationDate          datetime NOT NULL
CONSTRAINT DF_TariffDetail_ExpirationDate DEFAULT 'JAN 01 2100',
AddDate                 datetime NOT NULL
CONSTRAINT DF_TariffDetail_AddDate DEFAULT CURRENT_Timestamp,
AddWho                  char(18) NOT NULL
CONSTRAINT DF_TariffDetail_AddWho DEFAULT USER ,
EditDate                datetime NOT NULL
CONSTRAINT DF_TariffDetail_EditDate DEFAULT CURRENT_Timestamp,
EditWho                 char(18) NOT NULL
CONSTRAINT DF_TariffDetail_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('TariffDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TariffDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TariffDetail>>>'
GRANT INSERT ON TariffDetail TO nsql
GRANT UPDATE ON TariffDetail TO nsql
GRANT DELETE ON TariffDetail TO nsql
GRANT SELECT ON TariffDetail TO nsql
END
GO

-- =============================================
-- 容器计费表
-- =============================================

-- ContainerBilling 表 (容器计费表)
CREATE TABLE ContainerBilling
( ContainerBillingKey     char(10)       NOT NULL,
DocType                 char(10)       NOT NULL
CONSTRAINT DF_ContBill_DocType DEFAULT "ASN"
CONSTRAINT CK_ContBill_DocType CHECK (DocType in ('ASN', 'SO', 'CNT')),
ContainerType           char(20)       NOT NULL
CONSTRAINT DF_ContBill_ContainerType DEFAULT "",
StorerKey               char(15)       NOT NULL
CONSTRAINT DF_ContBill_StorerKey DEFAULT "",
TariffKey               char(10)       NOT NULL
CONSTRAINT DF_ContBill_TariffKey DEFAULT "",
Qty                     int            NOT NULL
CONSTRAINT DF_ContBill_Qty DEFAULT 0,
AddDate                 datetime       NOT NULL
CONSTRAINT DF_ContBill_AddDate DEFAULT CURRENT_Timestamp,
AddWho                  char(18)       NOT NULL
CONSTRAINT DF_ContBill_AddWho DEFAULT USER ,
EditDate                datetime       NOT NULL
CONSTRAINT DF_ContBill_EditDate DEFAULT CURRENT_Timestamp,
EditWho                 char(18)       NOT NULL
CONSTRAINT DF_ContBill_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('ContainerBilling') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ContainerBilling FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ContainerBilling>>>'
GRANT INSERT ON ContainerBilling TO nsql
GRANT UPDATE ON ContainerBilling TO nsql
GRANT DELETE ON ContainerBilling TO nsql
GRANT SELECT ON ContainerBilling TO nsql
END
GO

-- =============================================
-- 累计费用表
-- =============================================

-- ACCUMULATEDCHARGES 表 (累计费用表)
CREATE TABLE ACCUMULATEDCHARGES
( AccumulatedChargesKey    char(10) NOT NULL,
Descrip                  char(100) NOT NULL
CONSTRAINT DF_AccChg_Descrip DEFAULT "",
Status                   char(1)  NOT NULL
CONSTRAINT DF_AccChg_Status DEFAULT "0",
StorerKey                char(15) NOT NULL
CONSTRAINT DF_AccChg_StorerKey DEFAULT "",
TariffKey                char(10) NOT NULL
CONSTRAINT DF_AccChg_TariffKey DEFAULT "",
ChargeType               char(10) NOT NULL
CONSTRAINT DF_AccChg_ChargeType DEFAULT "",
ChargeAmount             decimal(12,6) NOT NULL
CONSTRAINT DF_AccChg_ChargeAmount DEFAULT 0.0,
ChargeDate               datetime NOT NULL
CONSTRAINT DF_AccChg_ChargeDate DEFAULT CURRENT_Timestamp,
BillingPeriod            char(10) NOT NULL
CONSTRAINT DF_AccChg_BillingPeriod DEFAULT "",
PrintCount               int      NOT NULL
CONSTRAINT DF_AccChg_PrintCount DEFAULT 0,
AddDate                  datetime NOT NULL
CONSTRAINT DF_AccChg_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_AccChg_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_AccChg_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_AccChg_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('ACCUMULATEDCHARGES') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ACCUMULATEDCHARGES FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ACCUMULATEDCHARGES>>>'
GRANT INSERT ON ACCUMULATEDCHARGES TO nsql
GRANT UPDATE ON ACCUMULATEDCHARGES TO nsql
GRANT DELETE ON ACCUMULATEDCHARGES TO nsql
GRANT SELECT ON ACCUMULATEDCHARGES TO nsql
END
GO

-- LOTNEWBILLTHRUDATE 表 (批次新计费截止日期表)
CREATE TABLE LOTNEWBILLTHRUDATE
( Lot                char(10) NOT NULL
CONSTRAINT DF_LNBTD_LOT DEFAULT "",
BillThruDate    datetime NOT NULL
CONSTRAINT DF_LNBTD_BillThruDate DEFAULT CURRENT_Timestamp,
AddWho  varchar(18) NULL
CONSTRAINT DF_LNBTD_AddWho DEFAULT USER,
AddDate datetime NULL
CONSTRAINT DF_LNBTD_AddDate DEFAULT CURRENT_Timestamp,
EditWho varchar(18) NULL
CONSTRAINT DF_LNBTD_EditWho DEFAULT USER,
EditDate datetime NULL
CONSTRAINT DF_LNBTD_EditDate DEFAULT CURRENT_Timestamp
)
GO

IF OBJECT_ID('LOTNEWBILLTHRUDATE') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LOTNEWBILLTHRUDATE FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LOTNEWBILLTHRUDATE>>>'
GRANT INSERT ON LOTNEWBILLTHRUDATE TO nsql
GRANT UPDATE ON LOTNEWBILLTHRUDATE TO nsql
GRANT DELETE ON LOTNEWBILLTHRUDATE TO nsql
GRANT SELECT ON LOTNEWBILLTHRUDATE TO nsql
END
GO

-- 注意：此脚本包含计费和财务相关表结构
-- 完整的表结构请参考原始 NEPISQL.sql 文件
