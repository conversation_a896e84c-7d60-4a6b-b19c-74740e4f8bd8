-- =============================================
-- Oracle版本 - 创建附加存储过程脚本
-- 功能：创建仓库管理系统的附加业务存储过程 (Oracle版本)
-- 用途：计费处理、盘点管理、补货处理等高级业务逻辑
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中提取的高级业务存储过程转换为Oracle语法
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 计费管理存储过程
-- =============================================

-- nspBillingRun 存储过程 (计费运行)
CREATE OR REPLACE PROCEDURE nspBillingRun (
    p_billinggroupmin   IN CHAR,
    p_billinggroupmax   IN CHAR,
    p_chargetypes       IN CHAR,
    p_cutoffdate        IN DATE,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR,
    p_totinvoices       OUT NUMBER,
    p_totcharges        OUT NUMBER
) IS
    v_continue NUMBER := 1;
    v_invoicebatchkey CHAR(10);
    v_lockset CHAR(1) := '0';
    v_cnt NUMBER;
    v_chargesretrieved NUMBER := 0;
    v_newcharges NUMBER := 0;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_totinvoices := 0;
    p_totcharges := 0;
    
    -- 获取发票批次键
    nspg_getkey('InvoiceBatchKey', 10, v_invoicebatchkey, p_success, p_err, p_errmsg);
    
    IF p_success = 1 THEN
        -- 检查是否有正在进行的计费运行
        SELECT COUNT(*) INTO v_cnt
        FROM STORERBILLING
        WHERE BillingGroup BETWEEN p_billinggroupmin AND p_billinggroupmax
        AND LockBatch IS NOT NULL;
        
        IF v_cnt > 0 THEN
            p_success := 0;
            p_err := 86806;
            p_errmsg := 'NSQL86806: User must post or cancel previous Billing Run before start next (nspBillingRun)';
            RETURN;
        END IF;
        
        -- 删除之前的临时计费数据
        DELETE FROM BILL_ACCUMULATEDCHARGES WHERE AddWho = v_currentuser;
        
        -- 锁定存储商
        UPDATE STORERBILLING
        SET LockBatch = v_invoicebatchkey,
            LockWho = v_currentuser,
            EditDate = v_currentdatetime,
            EditWho = v_currentuser
        WHERE BillingGroup BETWEEN p_billinggroupmin AND p_billinggroupmax;
        
        v_lockset := '1';
        
        -- 生成计费数据 (简化版本)
        INSERT INTO BILL_ACCUMULATEDCHARGES (
            Ident, StorerKey, TariffKey, ChargeType, ChargeAmount, BillingPeriod,
            AddDate, AddWho, EditDate, EditWho
        )
        SELECT 
            SEQ_BILL_ACCUMULATEDCHARGES.NEXTVAL,
            sb.StorerKey,
            sb.TariffKey,
            'IS', -- 库存存储费
            NVL(SUM(lli.Qty * td.Rate), 0),
            TO_CHAR(p_cutoffdate, 'YYYY-MM'),
            v_currentdatetime,
            v_currentuser,
            v_currentdatetime,
            v_currentuser
        FROM STORERBILLING sb
        JOIN LOTxLOCxID lli ON sb.StorerKey = lli.StorerKey
        JOIN TariffDetail td ON sb.TariffKey = td.TariffKey
        WHERE sb.BillingGroup BETWEEN p_billinggroupmin AND p_billinggroupmax
        AND td.ChargeType = 'IS'
        AND lli.Qty > 0
        GROUP BY sb.StorerKey, sb.TariffKey;
        
        v_chargesretrieved := SQL%ROWCOUNT;
        
        -- 检查是否有费用需要处理
        IF v_chargesretrieved = 0 THEN
            p_success := 0;
            p_err := 86807;
            p_errmsg := 'No charges to process. (nspBillingRun)';
        ELSE
            -- 将临时数据移动到正式表
            INSERT INTO ACCUMULATEDCHARGES (
                AccumulatedChargesKey, Descrip, Status, StorerKey, TariffKey,
                ChargeType, ChargeAmount, ChargeDate, BillingPeriod, PrintCount,
                AddDate, AddWho, EditDate, EditWho
            )
            SELECT 
                'AC' || LPAD(TO_CHAR(SEQ_ACCUMULATEDCHARGES.NEXTVAL), 8, '0'),
                'Billing Run ' || v_invoicebatchkey,
                '0',
                StorerKey,
                TariffKey,
                ChargeType,
                ChargeAmount,
                v_currentdatetime,
                BillingPeriod,
                0,
                v_currentdatetime,
                v_currentuser,
                v_currentdatetime,
                v_currentuser
            FROM BILL_ACCUMULATEDCHARGES
            WHERE AddWho = v_currentuser;
            
            p_totcharges := SQL%ROWCOUNT;
            
            -- 计算发票数量 (按存储商分组)
            SELECT COUNT(DISTINCT StorerKey) INTO p_totinvoices
            FROM BILL_ACCUMULATEDCHARGES
            WHERE AddWho = v_currentuser;
            
            -- 清理临时数据
            DELETE FROM BILL_ACCUMULATEDCHARGES WHERE AddWho = v_currentuser;
        END IF;
        
        -- 解锁存储商
        IF v_lockset = '1' THEN
            UPDATE STORERBILLING
            SET LockBatch = NULL,
                LockWho = NULL,
                EditDate = v_currentdatetime,
                EditWho = v_currentuser
            WHERE BillingGroup BETWEEN p_billinggroupmin AND p_billinggroupmax
            AND LockBatch = v_invoicebatchkey;
        END IF;
        
        IF p_success = 1 THEN
            COMMIT;
        ELSE
            ROLLBACK;
        END IF;
    END IF;
    
    IF p_success = 0 THEN
        nsp_logerror(p_err, p_errmsg, 'nspBillingRun');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspBillingRun');
        
        -- 解锁存储商
        IF v_lockset = '1' THEN
            UPDATE STORERBILLING
            SET LockBatch = NULL,
                LockWho = NULL
            WHERE BillingGroup BETWEEN p_billinggroupmin AND p_billinggroupmax
            AND LockBatch = v_invoicebatchkey;
        END IF;
        
        ROLLBACK;
END nspBillingRun;
/

-- 创建必要的序列
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_ACCUMULATEDCHARGES';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_ACCUMULATEDCHARGES START WITH 1 INCREMENT BY 1';
    END IF;
END;
/

-- nspBillingRunWrapper 存储过程 (计费运行包装器)
CREATE OR REPLACE PROCEDURE nspBillingRunWrapper (
    p_billinggroupmin   IN CHAR,
    p_billinggroupmax   IN CHAR,
    p_chargetypes       IN CHAR,
    p_cutoffdate        IN DATE
) IS
    v_success NUMBER;
    v_err NUMBER;
    v_errmsg CHAR(250);
    v_totinvoices NUMBER;
    v_totcharges NUMBER;
BEGIN
    -- 调用主计费过程
    nspBillingRun(
        p_billinggroupmin, p_billinggroupmax, p_chargetypes, p_cutoffdate,
        v_success, v_err, v_errmsg, v_totinvoices, v_totcharges
    );
    
    -- 输出结果
    DBMS_OUTPUT.PUT_LINE('Billing Run Results:');
    DBMS_OUTPUT.PUT_LINE('Success: ' || v_success);
    DBMS_OUTPUT.PUT_LINE('Total Invoices: ' || v_totinvoices);
    DBMS_OUTPUT.PUT_LINE('Total Charges: ' || v_totcharges);
    
    IF v_success = 0 THEN
        DBMS_OUTPUT.PUT_LINE('Error: ' || v_err);
        DBMS_OUTPUT.PUT_LINE('Message: ' || v_errmsg);
    END IF;
    
END nspBillingRunWrapper;
/

-- =============================================
-- 盘点管理存储过程
-- =============================================

-- nspPhysicalInventoryStart 存储过程 (开始物理盘点)
CREATE OR REPLACE PROCEDURE nspPhysicalInventoryStart (
    p_physicalkey       IN CHAR,
    p_storerkey         IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_cnt NUMBER;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查物理盘点是否已存在
    SELECT COUNT(*) INTO v_cnt
    FROM PHYSICAL
    WHERE PhysicalKey = p_physicalkey;

    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 71001;
        p_errmsg := 'NSQL71001: Physical inventory already exists';
        RETURN;
    END IF;

    -- 创建物理盘点头记录
    INSERT INTO PHYSICAL (
        PhysicalKey, StorerKey, Status, PhysicalDate,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        p_physicalkey, p_storerkey, '0', v_currentdatetime,
        v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
    );

    -- 生成盘点明细 (从当前库存)
    INSERT INTO PHYSICALDETAIL (
        PhysicalKey, PhysicalDetailKey, StorerKey, Sku, Lot, Loc, ID,
        QtyExpected, QtyActual, Status,
        AddDate, AddWho, EditDate, EditWho
    )
    SELECT
        p_physicalkey,
        'PD' || LPAD(TO_CHAR(ROWNUM), 8, '0'),
        StorerKey, Sku, Lot, Loc, ID,
        Qty, 0, '0',
        v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
    FROM LOTxLOCxID
    WHERE StorerKey = p_storerkey AND Qty > 0;

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspPhysicalInventoryStart');
        ROLLBACK;
END nspPhysicalInventoryStart;
/

-- nspPhysicalInventoryCount 存储过程 (盘点录入)
CREATE OR REPLACE PROCEDURE nspPhysicalInventoryCount (
    p_physicalkey       IN CHAR,
    p_physicaldetailkey IN CHAR,
    p_qtyactual         IN NUMBER,
    p_userkey           IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_cnt NUMBER;
    v_qtyexpected NUMBER;
    v_variance NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查盘点明细是否存在
    BEGIN
        SELECT QtyExpected INTO v_qtyexpected
        FROM PHYSICALDETAIL
        WHERE PhysicalKey = p_physicalkey AND PhysicalDetailKey = p_physicaldetailkey;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 72001;
            p_errmsg := 'NSQL72001: Physical detail not found';
            RETURN;
    END;

    -- 计算差异
    v_variance := p_qtyactual - v_qtyexpected;

    -- 更新盘点明细
    UPDATE PHYSICALDETAIL
    SET QtyActual = p_qtyactual,
        Variance = v_variance,
        Status = '1', -- 已盘点
        CountUser = p_userkey,
        CountDate = SYSDATE,
        EditDate = SYSDATE,
        EditWho = USER
    WHERE PhysicalKey = p_physicalkey AND PhysicalDetailKey = p_physicaldetailkey;

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspPhysicalInventoryCount');
        ROLLBACK;
END nspPhysicalInventoryCount;
/

-- nspCycleCountGenerate 存储过程 (生成循环盘点)
CREATE OR REPLACE PROCEDURE nspCycleCountGenerate (
    p_storerkey         IN CHAR,
    p_cckey             IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_cnt NUMBER;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_ccdetailkey CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查循环盘点是否已存在
    SELECT COUNT(*) INTO v_cnt
    FROM CC
    WHERE CCKey = p_cckey;

    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 73001;
        p_errmsg := 'NSQL73001: Cycle count already exists';
        RETURN;
    END IF;

    -- 创建循环盘点头记录
    INSERT INTO CC (
        CCKey, StorerKey, Status,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        p_cckey, p_storerkey, '0',
        v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
    );

    -- 生成循环盘点明细 (选择需要盘点的库存)
    FOR cc_rec IN (
        SELECT StorerKey, Sku, Lot, Loc, ID, Qty
        FROM LOTxLOCxID
        WHERE StorerKey = p_storerkey
        AND Qty > 0
        AND MOD(EXTRACT(DAY FROM SYSDATE), 7) = MOD(ABS(DBMS_RANDOM.VALUE(1, 1000)), 7) -- 随机选择
        ORDER BY Sku, Lot, Loc
    ) LOOP
        -- 获取明细键
        nspg_getkey('CCDetailKey', 10, v_ccdetailkey, p_success, p_err, p_errmsg);

        IF p_success = 1 THEN
            INSERT INTO CCDETAIL (
                CCKey, CCDetailKey, StorerKey, Sku, Lot, Loc, ID,
                QtyExpected, QtyActual, Variance, ReasonCode,
                AddDate, AddWho, EditDate, EditWho
            ) VALUES (
                p_cckey, v_ccdetailkey, cc_rec.StorerKey, cc_rec.Sku, cc_rec.Lot,
                cc_rec.Loc, cc_rec.ID, cc_rec.Qty, 0, 0, ' ',
                v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
            );
        ELSE
            EXIT;
        END IF;
    END LOOP;

    IF p_success = 1 THEN
        COMMIT;
    ELSE
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspCycleCountGenerate');
        ROLLBACK;
END nspCycleCountGenerate;
/

-- =============================================
-- 补货管理存储过程
-- =============================================

-- nspReplenishmentGenerate 存储过程 (生成补货任务)
CREATE OR REPLACE PROCEDURE nspReplenishmentGenerate (
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_replenishmentkey CHAR(10);
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_minqty NUMBER;
    v_maxqty NUMBER;
    v_currentqty NUMBER;
    v_replenqty NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 获取SKU的补货参数
    BEGIN
        SELECT MinQty, MaxQty INTO v_minqty, v_maxqty
        FROM SKU
        WHERE StorerKey = p_storerkey AND Sku = p_sku;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 74001;
            p_errmsg := 'NSQL74001: SKU not found';
            RETURN;
    END;

    -- 计算当前拣选位置库存
    SELECT NVL(SUM(Qty - QtyAllocated - QtyPicked), 0) INTO v_currentqty
    FROM LOTxLOCxID lli
    JOIN LOC l ON lli.Loc = l.Loc
    WHERE lli.StorerKey = p_storerkey AND lli.Sku = p_sku
    AND l.LocationType = 'PICK';

    -- 检查是否需要补货
    IF v_currentqty < v_minqty THEN
        v_replenqty := v_maxqty - v_currentqty;

        -- 获取补货键
        nspg_getkey('ReplenishmentKey', 10, v_replenishmentkey, p_success, p_err, p_errmsg);

        IF p_success = 1 THEN
            -- 创建补货任务
            INSERT INTO REPLENISHMENT (
                ReplenishmentKey, StorerKey, Sku, FromLoc, ToLoc,
                Qty, Status, Priority,
                AddDate, AddWho, EditDate, EditWho
            ) VALUES (
                v_replenishmentkey, p_storerkey, p_sku, 'RESERVE', 'PICK',
                v_replenqty, '0', '5',
                v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
            );

            COMMIT;
        END IF;
    ELSE
        p_success := 1; -- 不需要补货
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspReplenishmentGenerate');
        ROLLBACK;
END nspReplenishmentGenerate;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle附加存储过程脚本执行完成';
PROMPT '>>> 已创建完整的附加业务存储过程集合 (7个)';
PROMPT '>>> 包含：计费管理、盘点管理、补货管理等高级功能';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 附加存储过程创建完成！';
PROMPT '总附加存储过程数量：7个高级业务存储过程';
PROMPT '功能覆盖：';
PROMPT '- 计费运行和发票生成';
PROMPT '- 物理盘点管理';
PROMPT '- 循环盘点生成';
PROMPT '- 补货任务管理';
PROMPT '=============================================';
