-- =============================================
-- 创建附加专业化表结构脚本 (第八部分)
-- 功能：创建仓库管理系统的附加专业化表结构
-- 用途：波次管理、循环盘点、设备管理、区域管理等
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 波次管理表
-- =============================================

-- WAVEDETAIL 表 (波次明细表)
CREATE TABLE WAVEDETAIL
(WaveDetailKey            char(10) NOT NULL ,
WaveKey                  char(10) NOT NULL,
OrderKey                 char(10) NOT NULL
CONSTRAINT DF_WAVED_Orderkey DEFAULT "" ,
ProcessFlag              char(10) NOT NULL
CONSTRAINT DF_WAVED_ProcessFlag DEFAULT "0" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_WAVED_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_WAVED_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_WAVED_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_WAVED_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('WAVEDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE WAVEDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE WAVEDETAIL>>>'
GRANT INSERT ON WAVEDETAIL TO nsql
GRANT UPDATE ON WAVEDETAIL TO nsql
GRANT DELETE ON WAVEDETAIL TO nsql
GRANT SELECT ON WAVEDETAIL TO nsql
END
GO

-- =============================================
-- 循环盘点表
-- =============================================

-- CC 表 (循环盘点表)
CREATE TABLE CC
(CCKey                    char(10) NOT NULL,
Storerkey                char(10) NOT NULL
CONSTRAINT DF_CC_StorerKey DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_CC_Sku DEFAULT "" ,
Loc                      char(10) NOT NULL
CONSTRAINT DF_CC_Loc DEFAULT "" ,
Lot                      char(10) NOT NULL
CONSTRAINT DF_CC_Lot DEFAULT "" ,
Id                       char(18) NOT NULL
CONSTRAINT DF_CC_Id DEFAULT "" ,
QtyExpected              int NOT NULL
CONSTRAINT DF_CC_QtyExpected DEFAULT 0 ,
QtyActual                int NOT NULL
CONSTRAINT DF_CC_QtyActual DEFAULT 0 ,
Status                   char(10) NOT NULL
CONSTRAINT DF_CC_Status DEFAULT "0" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_CC_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_CC_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_CC_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_CC_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('CC') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CC FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CC>>>'
GRANT INSERT ON CC TO nsql
GRANT UPDATE ON CC TO nsql
GRANT DELETE ON CC TO nsql
GRANT SELECT ON CC TO nsql
END
GO

-- CCDetail 表 (循环盘点明细表)
CREATE TABLE CCDetail
(CCKey                    char(10) NOT NULL,
CCDetailKey              char(10) NOT NULL,
Storerkey                char(10) NOT NULL
CONSTRAINT DF_CCD_StorerKey DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_CCD_Sku DEFAULT "" ,
Loc                      char(10) NOT NULL
CONSTRAINT DF_CCD_Loc DEFAULT "" ,
Lot                      char(10) NOT NULL
CONSTRAINT DF_CCD_Lot DEFAULT "" ,
Id                       char(18) NOT NULL
CONSTRAINT DF_CCD_Id DEFAULT "" ,
QtyExpected              int NOT NULL
CONSTRAINT DF_CCD_QtyExpected DEFAULT 0 ,
QtyActual                int NOT NULL
CONSTRAINT DF_CCD_QtyActual DEFAULT 0 ,
Variance                 int NOT NULL
CONSTRAINT DF_CCD_Variance DEFAULT 0 ,
ReasonCode               char(10) NOT NULL
CONSTRAINT DF_CCD_ReasonCode DEFAULT "" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_CCD_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_CCD_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_CCD_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_CCD_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('CCDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CCDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CCDetail>>>'
GRANT INSERT ON CCDetail TO nsql
GRANT UPDATE ON CCDetail TO nsql
GRANT DELETE ON CCDetail TO nsql
GRANT SELECT ON CCDetail TO nsql
END
GO

-- =============================================
-- 设备和区域管理表
-- =============================================

-- EquipmentProfile 表 (设备配置表)
CREATE TABLE EquipmentProfile
(EquipmentProfileKey      char(10)     NOT NULL
CONSTRAINT DF_EP_Key         DEFAULT "" ,
Descr                    char(60)     NOT NULL
CONSTRAINT DF_EP_Descr       DEFAULT "" ,
MaximumWeight            float        NOT NULL
CONSTRAINT DF_EP_MaximumWeight DEFAULT 0 ,
MaximumCube              float        NOT NULL
CONSTRAINT DF_EP_MaximumCube DEFAULT 0 ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_EP_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_EP_AddWho      DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_EP_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_EP_EditWho     DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('EquipmentProfile') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE EquipmentProfile FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE EquipmentProfile>>>'
GRANT INSERT ON EquipmentProfile TO nsql
GRANT UPDATE ON EquipmentProfile TO nsql
GRANT DELETE ON EquipmentProfile TO nsql
GRANT SELECT ON EquipmentProfile TO nsql
END
GO

-- PutawayZone 表 (上架区域表)
CREATE TABLE PutawayZone
(PutawayZone              char(10)     NOT NULL
CONSTRAINT DF_PZ_Key         DEFAULT "" ,
Descr                    char(60)     NOT NULL
CONSTRAINT DF_PZ_Descr       DEFAULT "" ,
InLoc                    char(10)     NOT NULL
CONSTRAINT DF_PZ_InLoc       DEFAULT "" ,
OutLoc                   char(10)     NOT NULL
CONSTRAINT DF_PZ_OutLoc      DEFAULT "" ,
MaxPallets               int          NOT NULL
CONSTRAINT DF_PZ_MaxPallets  DEFAULT 0 ,
MaxWeight                float        NOT NULL
CONSTRAINT DF_PZ_MaxWeight   DEFAULT 0 ,
MaxCube                  float        NOT NULL
CONSTRAINT DF_PZ_MaxCube     DEFAULT 0 ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_PZ_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_PZ_AddWho      DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_PZ_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_PZ_EditWho     DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('PutawayZone') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PutawayZone FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PutawayZone>>>'
GRANT INSERT ON PutawayZone TO nsql
GRANT UPDATE ON PutawayZone TO nsql
GRANT DELETE ON PutawayZone TO nsql
GRANT SELECT ON PutawayZone TO nsql
END
GO

-- AreaDetail 表 (区域明细表)
CREATE TABLE AreaDetail
(AreaKey       char(10)                NOT NULL
CONSTRAINT DF_AD_Key         DEFAULT "" ,
PutawayZone   char(10)                NOT NULL
CONSTRAINT DF_AD_PutawayZone DEFAULT "" ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_AD_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_AD_AddWho      DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_AD_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_AD_EditWho     DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('AreaDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE AreaDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE AreaDetail>>>'
GRANT INSERT ON AreaDetail TO nsql
GRANT UPDATE ON AreaDetail TO nsql
GRANT DELETE ON AreaDetail TO nsql
GRANT SELECT ON AreaDetail TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- 注意：此脚本包含附加专业化表结构
-- 更多表结构将在后续部分添加
