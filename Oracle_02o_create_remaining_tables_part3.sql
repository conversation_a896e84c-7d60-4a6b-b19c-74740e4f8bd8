-- =============================================
-- Oracle版本 - 创建剩余表结构脚本 (第十五部分)
-- 功能：创建仓库管理系统中剩余的重要表结构 (Oracle版本)
-- 用途：Drop ID管理、存储商计费、追踪管理、BOL管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- Drop ID管理表
-- =============================================

-- Dropid 表 (Drop ID表)
CREATE TABLE Dropid
(Dropid        CHAR(18) NOT NULL
CONSTRAINT DF_DID_Dropid DEFAULT ' ',
Droploc       CHAR(10) NOT NULL
CONSTRAINT DF_DID_Droploc DEFAULT ' ',
AddDate       DATE NOT NULL
CONSTRAINT DF_DID_AddDate DEFAULT SYSDATE,
AddWho        CHAR(18) NOT NULL
CONSTRAINT DF_DID_AddWho DEFAULT USER,
EditDate      DATE NOT NULL
CONSTRAINT DF_DID_EditDate DEFAULT SYSDATE,
EditWho       CHAR(18) NOT NULL
CONSTRAINT DF_DID_EditWho DEFAULT USER,
TrafficCop    CHAR(1) NULL,
ArchiveCop    CHAR(1) NULL
);

-- 添加主键
ALTER TABLE Dropid ADD CONSTRAINT PK_Dropid PRIMARY KEY (Dropid);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Dropid TO nsql;

PROMPT '>>> 已创建表 Dropid (Drop ID表)';

-- DropidDetail 表 (Drop ID明细表)
CREATE TABLE DropidDetail
(Dropid        CHAR(18) NOT NULL
CONSTRAINT DF_DIDD_Dropid DEFAULT ' ',
ChildId       CHAR(18) NOT NULL
CONSTRAINT DF_DIDD_ChildId DEFAULT ' ',
AddDate       DATE NOT NULL
CONSTRAINT DF_DIDD_AddDate DEFAULT SYSDATE,
AddWho        CHAR(18) NOT NULL
CONSTRAINT DF_DIDD_AddWho DEFAULT USER,
EditDate      DATE NOT NULL
CONSTRAINT DF_DIDD_EditDate DEFAULT SYSDATE,
EditWho       CHAR(18) NOT NULL
CONSTRAINT DF_DIDD_EditWho DEFAULT USER,
TrafficCop    CHAR(1) NULL,
ArchiveCop    CHAR(1) NULL
);

-- 添加主键
ALTER TABLE DropidDetail ADD CONSTRAINT PK_DropidDetail PRIMARY KEY (Dropid, ChildId);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON DropidDetail TO nsql;

PROMPT '>>> 已创建表 DropidDetail (Drop ID明细表)';

-- =============================================
-- 存储商计费表
-- =============================================

-- STORERBILLING 表 (存储商计费表)
CREATE TABLE STORERBILLING
(StorerKey                CHAR(15) NOT NULL,
RSMinimumInvoiceCharge   NUMBER(22,6) NOT NULL
CONSTRAINT DF_STORERBILLING_RSMIC DEFAULT 0.0,
BillingGroup             CHAR(15) NOT NULL
CONSTRAINT DF_STORERBILLING_BillingGroup DEFAULT ' ',
TariffKey                CHAR(10) NOT NULL
CONSTRAINT DF_STORERBILLING_TariffKey DEFAULT ' ',
CalendarGroup            CHAR(10) NOT NULL
CONSTRAINT DF_STORERBILLING_CalendarGroup DEFAULT ' ',
TaxGroupKey              CHAR(10) NOT NULL
CONSTRAINT DF_STORERBILLING_TaxGroupKey DEFAULT ' ',
GLDistributionKey        CHAR(10) NOT NULL
CONSTRAINT DF_STORERBILLING_GLDistributionKey DEFAULT ' ',
CurrencyKey              CHAR(10) NOT NULL
CONSTRAINT DF_STORERBILLING_CurrencyKey DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_STORERBILLING_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_STORERBILLING_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_STORERBILLING_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_STORERBILLING_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE STORERBILLING ADD CONSTRAINT PK_STORERBILLING PRIMARY KEY (StorerKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON STORERBILLING TO nsql;

PROMPT '>>> 已创建表 STORERBILLING (存储商计费表)';

-- =============================================
-- 追踪管理表
-- =============================================

-- PTRACEDETAIL 表 (追踪明细表)
CREATE TABLE PTRACEDETAIL
(PTRACETYPE                   CHAR(30) NOT NULL,
PTRACEHEADKey                CHAR(10) NOT NULL,
PA_PutawayStrategyKey        CHAR(10) NOT NULL,
PA_PutawayStrategyLineNumber CHAR(5) NOT NULL,
AddDate                      DATE NOT NULL
CONSTRAINT DF_PTRACEDETAIL_AddDate DEFAULT SYSDATE,
AddWho                       CHAR(18) NOT NULL
CONSTRAINT DF_PTRACEDETAIL_AddWho DEFAULT USER,
EditDate                     DATE NOT NULL
CONSTRAINT DF_PTRACEDETAIL_EditDate DEFAULT SYSDATE,
EditWho                      CHAR(18) NOT NULL
CONSTRAINT DF_PTRACEDETAIL_EditWho DEFAULT USER,
TrafficCop                   CHAR(1) NULL,
ArchiveCop                   CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PTRACEDETAIL ADD CONSTRAINT PK_PTRACEDETAIL 
PRIMARY KEY (PTRACETYPE, PTRACEHEADKey, PA_PutawayStrategyKey, PA_PutawayStrategyLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PTRACEDETAIL TO nsql;

PROMPT '>>> 已创建表 PTRACEDETAIL (追踪明细表)';

-- PTRACEHEAD 表 (追踪头表)
CREATE TABLE PTRACEHEAD
(PTRACETYPE               CHAR(30) NOT NULL,
PTRACEHEADKey            CHAR(10) NOT NULL,
Userid                   CHAR(20) NULL,
AddDate                  DATE NOT NULL
CONSTRAINT DF_PTRACEHEAD_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PTRACEHEAD_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PTRACEHEAD_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PTRACEHEAD_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PTRACEHEAD ADD CONSTRAINT PK_PTRACEHEAD PRIMARY KEY (PTRACETYPE, PTRACEHEADKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PTRACEHEAD TO nsql;

PROMPT '>>> 已创建表 PTRACEHEAD (追踪头表)';

-- =============================================
-- BOL管理表
-- =============================================

-- BOL 表 (提单表)
CREATE TABLE BOL
(BolKey                        CHAR(10) NOT NULL,
Status                        CHAR(10) NOT NULL
CONSTRAINT DF_BOL_Status DEFAULT '0',
CarrierKey                   CHAR(15) NOT NULL
CONSTRAINT DF_BOL_CarrierKey DEFAULT ' ',
TrailerKey                   CHAR(15) NOT NULL
CONSTRAINT DF_BOL_TrailerKey DEFAULT ' ',
LoadDate                     DATE NULL,
DepartureDate                DATE NULL,
AddDate                      DATE NOT NULL
CONSTRAINT DF_BOL_AddDate DEFAULT SYSDATE,
AddWho                       CHAR(18) NOT NULL
CONSTRAINT DF_BOL_AddWho DEFAULT USER,
EditDate                     DATE NOT NULL
CONSTRAINT DF_BOL_EditDate DEFAULT SYSDATE,
EditWho                      CHAR(18) NOT NULL
CONSTRAINT DF_BOL_EditWho DEFAULT USER,
TrafficCop                   CHAR(1) NULL,
ArchiveCop                   CHAR(1) NULL
);

-- 添加主键
ALTER TABLE BOL ADD CONSTRAINT PK_BOL PRIMARY KEY (BolKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BOL TO nsql;

PROMPT '>>> 已创建表 BOL (提单表)';

-- BOLDETAIL 表 (提单明细表)
CREATE TABLE BOLDETAIL
(BolKey         CHAR(10) NOT NULL,
BolLineNumber  CHAR(5) NOT NULL,
OrderKey       CHAR(10) NOT NULL
CONSTRAINT DF_BOLDETAIL_OrderKey DEFAULT ' ',
AddDate        DATE NOT NULL
CONSTRAINT DF_BOLDETAIL_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_BOLDETAIL_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_BOLDETAIL_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_BOLDETAIL_EditWho DEFAULT USER,
TrafficCop     CHAR(1) NULL,
ArchiveCop     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE BOLDETAIL ADD CONSTRAINT PK_BOLDETAIL PRIMARY KEY (BolKey, BolLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BOLDETAIL TO nsql;

PROMPT '>>> 已创建表 BOLDETAIL (提单明细表)';

-- =============================================
-- 批次ID明细表
-- =============================================

-- LotxIdDetail 表 (批次ID明细表)
CREATE TABLE LotxIdDetail
(LotxIdDetailKey     CHAR(10) NOT NULL,
ReceiptKey          CHAR(10) NOT NULL
CONSTRAINT DF_LotIdDetail_RecKey DEFAULT ' ',
ReceiptLineNumber   CHAR(5) NOT NULL
CONSTRAINT DF_LotIdDetail_RecLineNumber DEFAULT ' ',
StorerKey           CHAR(15) NOT NULL
CONSTRAINT DF_LotIdDetail_StorerKey DEFAULT ' ',
Sku                 CHAR(20) NOT NULL
CONSTRAINT DF_LotIdDetail_Sku DEFAULT ' ',
Lot                 CHAR(10) NOT NULL
CONSTRAINT DF_LotIdDetail_Lot DEFAULT ' ',
ID                  CHAR(18) NOT NULL
CONSTRAINT DF_LotIdDetail_ID DEFAULT ' ',
Qty                 NUMBER(10) NOT NULL
CONSTRAINT DF_LotIdDetail_Qty DEFAULT 0,
AddDate             DATE NOT NULL
CONSTRAINT DF_LotIdDetail_AddDate DEFAULT SYSDATE,
AddWho              CHAR(18) NOT NULL
CONSTRAINT DF_LotIdDetail_AddWho DEFAULT USER,
EditDate            DATE NOT NULL
CONSTRAINT DF_LotIdDetail_EditDate DEFAULT SYSDATE,
EditWho             CHAR(18) NOT NULL
CONSTRAINT DF_LotIdDetail_EditWho DEFAULT USER,
TrafficCop          CHAR(1) NULL,
ArchiveCop          CHAR(1) NULL
);

-- 添加主键
ALTER TABLE LotxIdDetail ADD CONSTRAINT PK_LotxIdDetail PRIMARY KEY (LotxIdDetailKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON LotxIdDetail TO nsql;

PROMPT '>>> 已创建表 LotxIdDetail (批次ID明细表)';

-- 提交事务
COMMIT;

PROMPT '>>> 剩余表结构脚本第三部分执行完成';
PROMPT '>>> 已创建Drop ID管理、存储商计费、追踪管理、BOL管理表 (8张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
