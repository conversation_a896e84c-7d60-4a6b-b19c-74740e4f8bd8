-- =============================================
-- Oracle版本 - 创建专业化触发器脚本 (第四部分)
-- 功能：创建仓库管理系统的专业化触发器集合 (Oracle版本)
-- 用途：计费管理、装载管理、ASN管理、ID管理等专业触发器
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中的108个SQL Server触发器转换为Oracle语法 (专业化部分)
-- 包含：计费管理、装载管理、ASN管理、ID管理等触发器
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 计费管理触发器 (从SQL Server转换)
-- =============================================

-- ACCUMULATEDCHARGES表的插入触发器 (从ntrAccumulatedChargesAdd转换)
CREATE OR REPLACE TRIGGER NTR_ACCUMULATEDCHARGES_ADD
BEFORE INSERT ON ACCUMULATEDCHARGES
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    IF :NEW.ChargeAmount IS NULL THEN
        :NEW.ChargeAmount := 0;
    END IF;
    
    -- 生成累计费用键如果为空
    IF :NEW.AccumulatedChargesKey IS NULL OR TRIM(:NEW.AccumulatedChargesKey) = ' ' THEN
        SELECT 'AC' || LPAD(TO_CHAR(SEQ_ACCUMULATEDCHARGES.NEXTVAL), 8, '0') INTO :NEW.AccumulatedChargesKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ACCUMULATEDCHARGES_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ACCUMULATEDCHARGES_ADD (累计费用插入触发器)';

-- ACCUMULATEDCHARGES表的更新触发器 (从ntrAccumulatedChargesUpdate转换)
CREATE OR REPLACE TRIGGER NTR_ACCUMULATEDCHARGES_UPDATE
BEFORE UPDATE ON ACCUMULATEDCHARGES
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
    -- 验证费用金额不能为负数
    IF :NEW.ChargeAmount < 0 THEN
        RAISE_APPLICATION_ERROR(-20013, 'Charge amount cannot be negative');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ACCUMULATEDCHARGES_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ACCUMULATEDCHARGES_UPDATE (累计费用更新触发器)';

-- ACCUMULATEDCHARGES表的删除触发器 (从ntrAccumulatedChargesDelete转换)
CREATE OR REPLACE TRIGGER NTR_ACCUMULATEDCHARGES_DELETE
AFTER DELETE ON ACCUMULATEDCHARGES
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'ACCUMULATEDCHARGES',
        'RECORD_DELETE',
        'Accumulated Charge deleted: ' || :OLD.AccumulatedChargesKey || ' Amount: ' || :OLD.ChargeAmount,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ACCUMULATEDCHARGES_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ACCUMULATEDCHARGES_DELETE (累计费用删除触发器)';

-- =============================================
-- 装载管理触发器 (从SQL Server转换)
-- =============================================

-- LOAD表的插入触发器 (从ntrLoadHeaderAdd转换)
CREATE OR REPLACE TRIGGER NTR_LOAD_ADD
BEFORE INSERT ON LOAD
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成装载键如果为空
    IF :NEW.LoadKey IS NULL OR TRIM(:NEW.LoadKey) = ' ' THEN
        SELECT 'LD' || LPAD(TO_CHAR(SEQ_LOAD.NEXTVAL), 8, '0') INTO :NEW.LoadKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOAD_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOAD_ADD (装载插入触发器)';

-- LOAD表的更新触发器 (从ntrLoadHeaderUpdate转换)
CREATE OR REPLACE TRIGGER NTR_LOAD_UPDATE
BEFORE UPDATE ON LOAD
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
    -- 状态变更日志
    IF :OLD.Status != :NEW.Status THEN
        INSERT INTO ALERT (
            AlertKey, ModuleName, AlertType, AlertMessage, 
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
            'LOAD',
            'STATUS_CHANGE',
            'Load ' || :NEW.LoadKey || ' status changed from ' || :OLD.Status || ' to ' || :NEW.Status,
            SYSDATE, USER, SYSDATE, USER
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOAD_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOAD_UPDATE (装载更新触发器)';

-- LOAD表的删除触发器 (从ntrLoadHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_LOAD_DELETE
BEFORE DELETE ON LOAD
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有装载明细
    SELECT COUNT(*) INTO v_cnt
    FROM LOADDETAIL
    WHERE LoadKey = :OLD.LoadKey;
    
    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20014, 'Cannot delete load with existing load details');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOAD_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOAD_DELETE (装载删除触发器)';

-- LOADDETAIL表的插入触发器 (从ntrLoadDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_LOADDETAIL_ADD
BEFORE INSERT ON LOADDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 生成装载明细键如果为空
    IF :NEW.LoadDetailKey IS NULL OR TRIM(:NEW.LoadDetailKey) = ' ' THEN
        SELECT 'LDD' || LPAD(TO_CHAR(SEQ_LOADDETAIL.NEXTVAL), 7, '0') INTO :NEW.LoadDetailKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOADDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOADDETAIL_ADD (装载明细插入触发器)';

-- LOADDETAIL表的更新触发器 (从ntrLoadDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_LOADDETAIL_UPDATE
BEFORE UPDATE ON LOADDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOADDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOADDETAIL_UPDATE (装载明细更新触发器)';

-- LOADDETAIL表的删除触发器 (从ntrLoadDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_LOADDETAIL_DELETE
AFTER DELETE ON LOADDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'LOADDETAIL',
        'RECORD_DELETE',
        'Load Detail deleted: ' || :OLD.LoadKey || '/' || :OLD.OrderKey,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOADDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOADDETAIL_DELETE (装载明细删除触发器)';

-- =============================================
-- ASN管理触发器 (从SQL Server转换)
-- =============================================

-- ASN表的插入触发器 (从ntrASNHeaderAdd转换)
CREATE OR REPLACE TRIGGER NTR_ASN_ADD
BEFORE INSERT ON ASN
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;

    IF :NEW.Type IS NULL THEN
        :NEW.Type := 'ASN';
    END IF;

    -- 生成ASN键如果为空
    IF :NEW.ASNKey IS NULL OR TRIM(:NEW.ASNKey) = ' ' THEN
        SELECT 'ASN' || LPAD(TO_CHAR(SEQ_ASN.NEXTVAL), 7, '0') INTO :NEW.ASNKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ASN_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ASN_ADD (ASN插入触发器)';

-- ASN表的更新触发器 (从ntrASNHeaderUpdate转换)
CREATE OR REPLACE TRIGGER NTR_ASN_UPDATE
BEFORE UPDATE ON ASN
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

    -- 状态变更日志
    IF :OLD.Status != :NEW.Status THEN
        INSERT INTO ALERT (
            AlertKey, ModuleName, AlertType, AlertMessage,
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
            'ASN',
            'STATUS_CHANGE',
            'ASN ' || :NEW.ASNKey || ' status changed from ' || :OLD.Status || ' to ' || :NEW.Status,
            SYSDATE, USER, SYSDATE, USER
        );
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ASN_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ASN_UPDATE (ASN更新触发器)';

-- ASN表的删除触发器 (从ntrASNHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_ASN_DELETE
BEFORE DELETE ON ASN
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有ASN明细
    SELECT COUNT(*) INTO v_cnt
    FROM ASNDETAIL
    WHERE ASNKey = :OLD.ASNKey;

    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20015, 'Cannot delete ASN with existing ASN details');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ASN_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ASN_DELETE (ASN删除触发器)';

-- ASNDETAIL表的插入触发器 (从ntrASNDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_ASNDETAIL_ADD
BEFORE INSERT ON ASNDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;

    IF :NEW.QtyReceived IS NULL THEN
        :NEW.QtyReceived := 0;
    END IF;

    -- 生成ASN明细键如果为空
    IF :NEW.ASNDetailKey IS NULL OR TRIM(:NEW.ASNDetailKey) = ' ' THEN
        SELECT 'ASND' || LPAD(TO_CHAR(SEQ_ASNDETAIL.NEXTVAL), 6, '0') INTO :NEW.ASNDetailKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ASNDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ASNDETAIL_ADD (ASN明细插入触发器)';

-- ASNDETAIL表的更新触发器 (从ntrASNDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_ASNDETAIL_UPDATE
BEFORE UPDATE ON ASNDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

    -- 根据收货情况更新状态
    IF :OLD.QtyReceived != :NEW.QtyReceived THEN
        :NEW.Status := CASE
            WHEN :NEW.QtyReceived >= :NEW.Qty THEN '9'
            ELSE '0'
        END;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ASNDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ASNDETAIL_UPDATE (ASN明细更新触发器)';

-- ASNDETAIL表的删除触发器 (从ntrASNDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_ASNDETAIL_DELETE
AFTER DELETE ON ASNDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'ASNDETAIL',
        'RECORD_DELETE',
        'ASN Detail deleted: ' || :OLD.ASNKey || '/' || :OLD.ASNLineNumber,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ASNDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ASNDETAIL_DELETE (ASN明细删除触发器)';

-- =============================================
-- ID管理触发器 (从SQL Server转换)
-- =============================================

-- ID表的插入触发器 (从ntrIDAdd转换)
CREATE OR REPLACE TRIGGER NTR_ID_ADD
BEFORE INSERT ON ID
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 验证ID不能为空
    IF :NEW.ID IS NULL OR TRIM(:NEW.ID) = ' ' THEN
        RAISE_APPLICATION_ERROR(-20016, 'ID cannot be empty');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ID_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ID_ADD (ID插入触发器)';

-- ID表的更新触发器 (从ntrIDUpdate转换)
CREATE OR REPLACE TRIGGER NTR_ID_UPDATE
BEFORE UPDATE ON ID
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ID_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ID_UPDATE (ID更新触发器)';

-- ID表的删除触发器 (从ntrIDDelete转换)
CREATE OR REPLACE TRIGGER NTR_ID_DELETE
BEFORE DELETE ON ID
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有库存使用此ID
    SELECT COUNT(*) INTO v_cnt
    FROM LOTxLOCxID
    WHERE ID = :OLD.ID;

    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20017, 'Cannot delete ID with existing inventory');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ID_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ID_DELETE (ID删除触发器)';

-- 提交事务
COMMIT;

PROMPT '>>> Oracle专业化触发器脚本执行完成';
PROMPT '>>> 已创建计费管理、装载管理、ASN管理和ID管理触发器 (18个)';
PROMPT '>>> 所有专业化触发器创建完成！';
