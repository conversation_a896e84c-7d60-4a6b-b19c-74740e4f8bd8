-- =============================================
-- Oracle版本 - 创建计费和财务表结构脚本 (第六部分)
-- 功能：创建仓库管理系统的计费和财务相关表结构 (Oracle版本)
-- 用途：计费管理、税务、会计、费率等专业模块
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 会计和财务表
-- =============================================

-- ChartOfAccounts 表 (会计科目表)
CREATE TABLE ChartOfAccounts
(ChartofAccountsKey CHAR(30) NOT NULL,
Descrip             CHAR(30) NOT NULL
CONSTRAINT DF_ChartOfAccounts_Descrip DEFAULT ' ',
SupportFlag         CHAR(1) NOT NULL
CONSTRAINT DF_ChartOfAccounts_SupportFlag DEFAULT 'A',
AddDate             DATE NOT NULL
CONSTRAINT DF_ChartOfAccounts_AddDate DEFAULT SYSDATE,
AddWho              CHAR(18) NOT NULL
CONSTRAINT DF_ChartOfAccounts_AddWho DEFAULT USER,
EditDate            DATE NOT NULL
CONSTRAINT DF_ChartOfAccounts_EditDate DEFAULT SYSDATE,
EditWho             CHAR(18) NOT NULL
CONSTRAINT DF_ChartOfAccounts_EditWho DEFAULT USER,
TrafficCop          CHAR(1) NULL
);

-- 添加主键
ALTER TABLE ChartOfAccounts ADD CONSTRAINT PK_ChartOfAccounts PRIMARY KEY (ChartofAccountsKey);

-- 添加检查约束
ALTER TABLE ChartOfAccounts ADD CONSTRAINT CK_ChartOfAccounts_SupportFlag 
CHECK (SupportFlag IN ('A', 'I', 'D'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON ChartOfAccounts TO nsql;

PROMPT '>>> 已创建表 ChartOfAccounts (会计科目表)';

-- GLDistribution 表 (总账分配表)
CREATE TABLE GLDistribution
(GLDistributionKey  CHAR(10) NOT NULL,
SupportFlag         CHAR(1) NOT NULL
CONSTRAINT DF_GLDistribution_SupportFlag DEFAULT 'A',
Descrip             CHAR(30) NOT NULL
CONSTRAINT DF_GLDistribution_Descrip DEFAULT ' ',
AddDate             DATE NOT NULL
CONSTRAINT DF_GLDistribution_AddDate DEFAULT SYSDATE,
AddWho              CHAR(18) NOT NULL
CONSTRAINT DF_GLDistribution_AddWho DEFAULT USER,
EditDate            DATE NOT NULL
CONSTRAINT DF_GLDistribution_EditDate DEFAULT SYSDATE,
EditWho             CHAR(18) NOT NULL
CONSTRAINT DF_GLDistribution_EditWho DEFAULT USER,
TrafficCop          CHAR(1) NULL
);

-- 添加主键
ALTER TABLE GLDistribution ADD CONSTRAINT PK_GLDistribution PRIMARY KEY (GLDistributionKey);

-- 添加检查约束
ALTER TABLE GLDistribution ADD CONSTRAINT CK_GLDistribution_SupportFlag 
CHECK (SupportFlag IN ('A', 'I', 'D'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON GLDistribution TO nsql;

PROMPT '>>> 已创建表 GLDistribution (总账分配表)';

-- GLDistributionDetail 表 (总账分配明细表)
CREATE TABLE GLDistributionDetail
(GLDistributionKey        CHAR(10) NOT NULL,
GLDistributionLineNumber CHAR(5) NOT NULL
CONSTRAINT DF_GLDistributionDetail_GLDistLineNumber DEFAULT ' ',
ChartofAccountsKey       CHAR(30) NOT NULL
CONSTRAINT DF_GLDistributionDetail_ChartofAccountsKey DEFAULT 'XXXXXXXXXX',
GLDistributionPct        NUMBER(12,6) NOT NULL
CONSTRAINT DF_GLDistributionDetail_GLDistributionPct DEFAULT 0.0,
Descrip                  CHAR(30) NOT NULL
CONSTRAINT DF_GLDistributionDetail_Descrip DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_GLDistributionDetail_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_GLDistributionDetail_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_GLDistributionDetail_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_GLDistributionDetail_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE GLDistributionDetail ADD CONSTRAINT PK_GLDistributionDetail 
PRIMARY KEY (GLDistributionKey, GLDistributionLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON GLDistributionDetail TO nsql;

PROMPT '>>> 已创建表 GLDistributionDetail (总账分配明细表)';

-- =============================================
-- 税务管理表
-- =============================================

-- TaxRate 表 (税率表)
CREATE TABLE TaxRate
(TaxRateKey         CHAR(10) NOT NULL,
TaxAuthority       CHAR(30) NOT NULL
CONSTRAINT DF_TaxRate_TaxAuthority DEFAULT ' ',
SupportFlag        CHAR(1) NOT NULL
CONSTRAINT DF_TaxRate_SupportFlag DEFAULT 'A',
Rate               NUMBER(8,7) NOT NULL
CONSTRAINT DF_TaxRate_Rate DEFAULT 0.0,
ExternTaxRateKey   CHAR(30) NOT NULL
CONSTRAINT DF_TaxRate_ExternTaxRateKey DEFAULT ' ',
AddDate            DATE NOT NULL
CONSTRAINT DF_TaxRate_AddDate DEFAULT SYSDATE,
AddWho             CHAR(18) NOT NULL
CONSTRAINT DF_TaxRate_AddWho DEFAULT USER,
EditDate           DATE NOT NULL
CONSTRAINT DF_TaxRate_EditDate DEFAULT SYSDATE,
EditWho            CHAR(18) NOT NULL
CONSTRAINT DF_TaxRate_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE TaxRate ADD CONSTRAINT PK_TaxRate PRIMARY KEY (TaxRateKey);

-- 添加检查约束
ALTER TABLE TaxRate ADD CONSTRAINT CK_TaxRate_SupportFlag 
CHECK (SupportFlag IN ('A', 'I', 'D'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaxRate TO nsql;

PROMPT '>>> 已创建表 TaxRate (税率表)';

-- TaxGroup 表 (税组表)
CREATE TABLE TaxGroup
(TaxGroupKey         CHAR(10) NOT NULL,
SupportFlag         CHAR(1) NOT NULL
CONSTRAINT DF_TaxGroup_SupportFlag DEFAULT 'A',
Descrip             CHAR(30) NOT NULL
CONSTRAINT DF_TaxGroup_Descrip DEFAULT ' ',
AddDate            DATE NOT NULL
CONSTRAINT DF_TaxGroup_AddDate DEFAULT SYSDATE,
AddWho             CHAR(18) NOT NULL
CONSTRAINT DF_TaxGroup_AddWho DEFAULT USER,
EditDate           DATE NOT NULL
CONSTRAINT DF_TaxGroup_EditDate DEFAULT SYSDATE,
EditWho            CHAR(18) NOT NULL
CONSTRAINT DF_TaxGroup_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE TaxGroup ADD CONSTRAINT PK_TaxGroup PRIMARY KEY (TaxGroupKey);

-- 添加检查约束
ALTER TABLE TaxGroup ADD CONSTRAINT CK_TaxGroup_SupportFlag 
CHECK (SupportFlag IN ('A', 'I', 'D'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaxGroup TO nsql;

PROMPT '>>> 已创建表 TaxGroup (税组表)';

-- TaxGroupDetail 表 (税组明细表)
CREATE TABLE TaxGroupDetail
(TaxGroupKey        CHAR(10) NOT NULL,
TaxRateKey         CHAR(10) NOT NULL,
GLDistributionKey  CHAR(10) NOT NULL
CONSTRAINT DF_TaxGroupDetail_GLDistributionKey DEFAULT 'XXXXXXXXXX',
AddDate            DATE NOT NULL
CONSTRAINT DF_TaxGroupDetail_AddDate DEFAULT SYSDATE,
AddWho             CHAR(18) NOT NULL
CONSTRAINT DF_TaxGroupDetail_AddWho DEFAULT USER,
EditDate           DATE NOT NULL
CONSTRAINT DF_TaxGroupDetail_EditDate DEFAULT SYSDATE,
EditWho            CHAR(18) NOT NULL
CONSTRAINT DF_TaxGroupDetail_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE TaxGroupDetail ADD CONSTRAINT PK_TaxGroupDetail 
PRIMARY KEY (TaxGroupKey, TaxRateKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaxGroupDetail TO nsql;

PROMPT '>>> 已创建表 TaxGroupDetail (税组明细表)';

-- =============================================
-- 费率管理表
-- =============================================

-- Tariff 表 (费率表)
CREATE TABLE Tariff
(TariffKey              CHAR(10) NOT NULL,
Descrip                CHAR(30) NOT NULL
CONSTRAINT DF_Tariff_Descrip DEFAULT ' ',
SupportFlag         CHAR(1) NOT NULL
CONSTRAINT DF_Tariff_SupportFlag DEFAULT 'A',
TariffType             CHAR(10) NOT NULL
CONSTRAINT DF_Tariff_TariffType DEFAULT ' ',
CurrencyKey            CHAR(10) NOT NULL
CONSTRAINT DF_Tariff_CurrencyKey DEFAULT 'USD',
GLDistributionKey      CHAR(10) NOT NULL
CONSTRAINT DF_Tariff_GLDistributionKey DEFAULT 'XXXXXXXXXX',
TaxGroupKey            CHAR(10) NOT NULL
CONSTRAINT DF_Tariff_TaxGroupKey DEFAULT 'XXXXXXXXXX',
AddDate            DATE NOT NULL
CONSTRAINT DF_Tariff_AddDate DEFAULT SYSDATE,
AddWho             CHAR(18) NOT NULL
CONSTRAINT DF_Tariff_AddWho DEFAULT USER,
EditDate           DATE NOT NULL
CONSTRAINT DF_Tariff_EditDate DEFAULT SYSDATE,
EditWho            CHAR(18) NOT NULL
CONSTRAINT DF_Tariff_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE Tariff ADD CONSTRAINT PK_Tariff PRIMARY KEY (TariffKey);

-- 添加检查约束
ALTER TABLE Tariff ADD CONSTRAINT CK_Tariff_SupportFlag 
CHECK (SupportFlag IN ('A', 'I', 'D'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Tariff TO nsql;

PROMPT '>>> 已创建表 Tariff (费率表)';

-- TariffDetail 表 (费率明细表)
CREATE TABLE TariffDetail
(TariffDetailKey         CHAR(10) NOT NULL,
TariffKey               CHAR(10) NOT NULL,
ChargeType              CHAR(10) NOT NULL,
Descrip                 CHAR(30) NOT NULL
CONSTRAINT DF_TariffDetail_Descrip DEFAULT ' ',
UOM                     CHAR(10) NOT NULL
CONSTRAINT DF_TariffDetail_UOM DEFAULT ' ',
Rate                    NUMBER(12,6) NOT NULL
CONSTRAINT DF_TariffDetail_Rate DEFAULT 0.0,
MinimumCharge           NUMBER(12,6) NOT NULL
CONSTRAINT DF_TariffDetail_MinimumCharge DEFAULT 0.0,
MaximumCharge           NUMBER(12,6) NOT NULL
CONSTRAINT DF_TariffDetail_MaximumCharge DEFAULT 999999.99,
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_TariffDetail_EffectiveDate DEFAULT SYSDATE,
ExpirationDate          DATE NOT NULL
CONSTRAINT DF_TariffDetail_ExpirationDate DEFAULT TO_DATE('2100-01-01', 'YYYY-MM-DD'),
AddDate                 DATE NOT NULL
CONSTRAINT DF_TariffDetail_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TariffDetail_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_TariffDetail_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_TariffDetail_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE TariffDetail ADD CONSTRAINT PK_TariffDetail PRIMARY KEY (TariffDetailKey);

-- 添加检查约束
ALTER TABLE TariffDetail ADD CONSTRAINT CK_TariffDetail_ChargeType
CHECK (ChargeType IN ('IS', 'RS', 'HI','HO','MI', 'AC','SP', 'DI', 'DO','MR'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TariffDetail TO nsql;

PROMPT '>>> 已创建表 TariffDetail (费率明细表)';

-- =============================================
-- 容器计费表
-- =============================================

-- ContainerBilling 表 (容器计费表)
CREATE TABLE ContainerBilling
(ContainerBillingKey     CHAR(10) NOT NULL,
DocType                 CHAR(10) NOT NULL
CONSTRAINT DF_ContainerBilling_DocType DEFAULT 'ASN',
ContainerType           CHAR(20) NOT NULL
CONSTRAINT DF_ContainerBilling_ContainerType DEFAULT ' ',
StorerKey               CHAR(15) NOT NULL
CONSTRAINT DF_ContainerBilling_StorerKey DEFAULT ' ',
TariffKey               CHAR(10) NOT NULL
CONSTRAINT DF_ContainerBilling_TariffKey DEFAULT ' ',
Qty                     NUMBER(10) NOT NULL
CONSTRAINT DF_ContainerBilling_Qty DEFAULT 0,
AddDate                 DATE NOT NULL
CONSTRAINT DF_ContainerBilling_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_ContainerBilling_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_ContainerBilling_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_ContainerBilling_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE ContainerBilling ADD CONSTRAINT PK_ContainerBilling PRIMARY KEY (ContainerBillingKey);

-- 添加检查约束
ALTER TABLE ContainerBilling ADD CONSTRAINT CK_ContainerBilling_DocType
CHECK (DocType IN ('ASN', 'SO', 'CNT'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON ContainerBilling TO nsql;

PROMPT '>>> 已创建表 ContainerBilling (容器计费表)';

-- =============================================
-- 累计费用表
-- =============================================

-- ACCUMULATEDCHARGES 表 (累计费用表)
CREATE TABLE ACCUMULATEDCHARGES
(AccumulatedChargesKey    CHAR(10) NOT NULL,
Descrip                  CHAR(100) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_Descrip DEFAULT ' ',
Status                   CHAR(1) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_Status DEFAULT '0',
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_StorerKey DEFAULT ' ',
TariffKey                CHAR(10) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_TariffKey DEFAULT ' ',
ChargeType               CHAR(10) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_ChargeType DEFAULT ' ',
ChargeAmount             NUMBER(12,6) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_ChargeAmount DEFAULT 0.0,
ChargeDate               DATE NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_ChargeDate DEFAULT SYSDATE,
BillingPeriod            CHAR(10) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_BillingPeriod DEFAULT ' ',
PrintCount               NUMBER(10) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_PrintCount DEFAULT 0,
AddDate                  DATE NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_ACCUMULATEDCHARGES_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE ACCUMULATEDCHARGES ADD CONSTRAINT PK_ACCUMULATEDCHARGES PRIMARY KEY (AccumulatedChargesKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON ACCUMULATEDCHARGES TO nsql;

PROMPT '>>> 已创建表 ACCUMULATEDCHARGES (累计费用表)';

-- LOTNEWBILLTHRUDATE 表 (批次新计费截止日期表)
CREATE TABLE LOTNEWBILLTHRUDATE
(Lot                CHAR(10) NOT NULL
CONSTRAINT DF_LOTNEWBILLTHRUDATE_Lot DEFAULT ' ',
BillThruDate    DATE NOT NULL
CONSTRAINT DF_LOTNEWBILLTHRUDATE_BillThruDate DEFAULT SYSDATE,
AddWho  VARCHAR2(18) NULL
CONSTRAINT DF_LOTNEWBILLTHRUDATE_AddWho DEFAULT USER,
AddDate DATE NULL
CONSTRAINT DF_LOTNEWBILLTHRUDATE_AddDate DEFAULT SYSDATE,
EditWho VARCHAR2(18) NULL
CONSTRAINT DF_LOTNEWBILLTHRUDATE_EditWho DEFAULT USER,
EditDate DATE NULL
CONSTRAINT DF_LOTNEWBILLTHRUDATE_EditDate DEFAULT SYSDATE
);

-- 添加主键
ALTER TABLE LOTNEWBILLTHRUDATE ADD CONSTRAINT PK_LOTNEWBILLTHRUDATE PRIMARY KEY (Lot);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON LOTNEWBILLTHRUDATE TO nsql;

PROMPT '>>> 已创建表 LOTNEWBILLTHRUDATE (批次新计费截止日期表)';

-- 提交事务
COMMIT;

PROMPT '>>> 计费和财务表结构脚本执行完成 (11张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
