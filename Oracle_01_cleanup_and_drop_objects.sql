-- =============================================
-- Oracle版本 - 清理和删除对象脚本
-- 功能：清理现有数据库对象，为重建做准备 (Oracle版本)
-- 用途：数据库重构前的清理工作
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境参数
SET SERVEROUTPUT ON SIZE 1000000;
SET PAGESIZE 0;
SET LINESIZE 1000;
SET TIMING ON;

PROMPT =============================================
PROMPT Oracle版本 - 数据库对象清理脚本
PROMPT 功能：删除现有WMS数据库对象
PROMPT 开始时间：
SELECT TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS START_TIME FROM DUAL;
PROMPT =============================================

-- 创建清理过程
CREATE OR REPLACE PROCEDURE DROP_TABLE_IF_EXISTS(p_table_name VARCHAR2) IS
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count 
    FROM USER_TABLES 
    WHERE TABLE_NAME = UPPER(p_table_name);
    
    IF v_count > 0 THEN
        EXECUTE IMMEDIATE 'DROP TABLE ' || p_table_name || ' CASCADE CONSTRAINTS';
        DBMS_OUTPUT.PUT_LINE('>>> 已删除表: ' || p_table_name);
    ELSE
        DBMS_OUTPUT.PUT_LINE('>>> 表不存在，跳过: ' || p_table_name);
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('>>> 删除表失败: ' || p_table_name || ' - ' || SQLERRM);
END;
/

CREATE OR REPLACE PROCEDURE DROP_SEQUENCE_IF_EXISTS(p_seq_name VARCHAR2) IS
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count 
    FROM USER_SEQUENCES 
    WHERE SEQUENCE_NAME = UPPER(p_seq_name);
    
    IF v_count > 0 THEN
        EXECUTE IMMEDIATE 'DROP SEQUENCE ' || p_seq_name;
        DBMS_OUTPUT.PUT_LINE('>>> 已删除序列: ' || p_seq_name);
    ELSE
        DBMS_OUTPUT.PUT_LINE('>>> 序列不存在，跳过: ' || p_seq_name);
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('>>> 删除序列失败: ' || p_seq_name || ' - ' || SQLERRM);
END;
/

-- 第一步：删除视图
PROMPT 第一步：删除视图...
DECLARE
    CURSOR view_cursor IS
        SELECT VIEW_NAME FROM USER_VIEWS
        WHERE VIEW_NAME LIKE '%WMS%' OR VIEW_NAME LIKE '%NSQL%';
BEGIN
    FOR view_rec IN view_cursor LOOP
        BEGIN
            EXECUTE IMMEDIATE 'DROP VIEW ' || view_rec.VIEW_NAME;
            DBMS_OUTPUT.PUT_LINE('>>> 已删除视图: ' || view_rec.VIEW_NAME);
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('>>> 删除视图失败: ' || view_rec.VIEW_NAME || ' - ' || SQLERRM);
        END;
    END LOOP;
END;
/

-- 第二步：删除存储过程和函数
PROMPT 第二步：删除存储过程和函数...
DECLARE
    CURSOR proc_cursor IS
        SELECT OBJECT_NAME, OBJECT_TYPE FROM USER_OBJECTS
        WHERE OBJECT_TYPE IN ('PROCEDURE', 'FUNCTION', 'PACKAGE')
        AND (OBJECT_NAME LIKE '%WMS%' OR OBJECT_NAME LIKE '%NSQL%');
BEGIN
    FOR proc_rec IN proc_cursor LOOP
        BEGIN
            EXECUTE IMMEDIATE 'DROP ' || proc_rec.OBJECT_TYPE || ' ' || proc_rec.OBJECT_NAME;
            DBMS_OUTPUT.PUT_LINE('>>> 已删除' || proc_rec.OBJECT_TYPE || ': ' || proc_rec.OBJECT_NAME);
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('>>> 删除' || proc_rec.OBJECT_TYPE || '失败: ' || proc_rec.OBJECT_NAME || ' - ' || SQLERRM);
        END;
    END LOOP;
END;
/

-- 第三步：删除触发器
PROMPT 第三步：删除触发器...
DECLARE
    CURSOR trigger_cursor IS
        SELECT TRIGGER_NAME FROM USER_TRIGGERS
        WHERE TRIGGER_NAME LIKE '%WMS%' OR TRIGGER_NAME LIKE '%NSQL%';
BEGIN
    FOR trigger_rec IN trigger_cursor LOOP
        BEGIN
            EXECUTE IMMEDIATE 'DROP TRIGGER ' || trigger_rec.TRIGGER_NAME;
            DBMS_OUTPUT.PUT_LINE('>>> 已删除触发器: ' || trigger_rec.TRIGGER_NAME);
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('>>> 删除触发器失败: ' || trigger_rec.TRIGGER_NAME || ' - ' || SQLERRM);
        END;
    END LOOP;
END;
/

-- 第四步：删除序列
PROMPT 第四步：删除序列...
BEGIN
    DROP_SEQUENCE_IF_EXISTS('SEQ_ITRN');
    DROP_SEQUENCE_IF_EXISTS('SEQ_ORDERKEY');
    DROP_SEQUENCE_IF_EXISTS('SEQ_PICKHEADER');
    DROP_SEQUENCE_IF_EXISTS('SEQ_RECEIPTKEY');
    DROP_SEQUENCE_IF_EXISTS('SEQ_POKEY');
    DROP_SEQUENCE_IF_EXISTS('SEQ_ADJUSTMENTKEY');
    DROP_SEQUENCE_IF_EXISTS('SEQ_TRANSFERKEY');
    DROP_SEQUENCE_IF_EXISTS('SEQ_WAVEKEY');
    DROP_SEQUENCE_IF_EXISTS('SEQ_PALLETKEY');
    DROP_SEQUENCE_IF_EXISTS('SEQ_CONTAINERKEY');
END;
/

-- 第五步：删除核心业务表
PROMPT 第五步：删除核心业务表...
BEGIN
    -- 核心库存表
    DROP_TABLE_IF_EXISTS('ITRN');
    DROP_TABLE_IF_EXISTS('SKU');
    DROP_TABLE_IF_EXISTS('LOT');
    DROP_TABLE_IF_EXISTS('LOTATTRIBUTE');
    DROP_TABLE_IF_EXISTS('LOC');
    DROP_TABLE_IF_EXISTS('LOTxLOCxID');
    DROP_TABLE_IF_EXISTS('ID');
    DROP_TABLE_IF_EXISTS('STORER');
    DROP_TABLE_IF_EXISTS('SKUxLOC');
    
    -- 订单管理表
    DROP_TABLE_IF_EXISTS('ORDERS');
    DROP_TABLE_IF_EXISTS('ORDERDETAIL');
    DROP_TABLE_IF_EXISTS('PICKHEADER');
    DROP_TABLE_IF_EXISTS('PICKDETAIL');
    DROP_TABLE_IF_EXISTS('WAVE');
    DROP_TABLE_IF_EXISTS('WAVEDETAIL');
    DROP_TABLE_IF_EXISTS('CARTONIZATION');
    
    -- 收发货表
    DROP_TABLE_IF_EXISTS('RECEIPT');
    DROP_TABLE_IF_EXISTS('RECEIPTDETAIL');
    DROP_TABLE_IF_EXISTS('PO');
    DROP_TABLE_IF_EXISTS('PODETAIL');
    DROP_TABLE_IF_EXISTS('PALLET');
    DROP_TABLE_IF_EXISTS('PALLETDETAIL');
    DROP_TABLE_IF_EXISTS('CONTAINER');
    DROP_TABLE_IF_EXISTS('CONTAINERDETAIL');
    
    -- 调整和补货表
    DROP_TABLE_IF_EXISTS('ADJUSTMENT');
    DROP_TABLE_IF_EXISTS('ADJUSTMENTDETAIL');
    DROP_TABLE_IF_EXISTS('REPLENISHMENT');
    DROP_TABLE_IF_EXISTS('TRANSFER');
    DROP_TABLE_IF_EXISTS('TRANSFERDETAIL');
    
    -- 系统管理表
    DROP_TABLE_IF_EXISTS('PACK');
    DROP_TABLE_IF_EXISTS('ALERT');
    DROP_TABLE_IF_EXISTS('ERRLOG');
    DROP_TABLE_IF_EXISTS('NSQLCONFIG');
    DROP_TABLE_IF_EXISTS('CODELIST');
    DROP_TABLE_IF_EXISTS('CODELKUP');
    DROP_TABLE_IF_EXISTS('NCOUNTER');
    DROP_TABLE_IF_EXISTS('NCOUNTERITRN');
    DROP_TABLE_IF_EXISTS('NCOUNTERPICK');
    DROP_TABLE_IF_EXISTS('IDSTACK');
END;
/

-- 第六步：删除专业化表
PROMPT 第六步：删除专业化表...
BEGIN
    -- 航空货运表
    DROP_TABLE_IF_EXISTS('MASTERAIRWAYBILL');
    DROP_TABLE_IF_EXISTS('MASTERAIRWAYBILLDETAIL');
    DROP_TABLE_IF_EXISTS('HOUSEAIRWAYBILL');
    DROP_TABLE_IF_EXISTS('HOUSEAIRWAYBILLDETAIL');
    
    -- 海运表
    DROP_TABLE_IF_EXISTS('MBOL');
    DROP_TABLE_IF_EXISTS('MBOLDETAIL');
    DROP_TABLE_IF_EXISTS('BOL');
    DROP_TABLE_IF_EXISTS('BOLDETAIL');
    
    -- 盘点表
    DROP_TABLE_IF_EXISTS('PHYSICAL');
    DROP_TABLE_IF_EXISTS('CC');
    DROP_TABLE_IF_EXISTS('CCDETAIL');
    DROP_TABLE_IF_EXISTS('PHY_A2B_ID');
    DROP_TABLE_IF_EXISTS('PHY_A2B_LOT');
    DROP_TABLE_IF_EXISTS('PHY_A2B_SKU');
    DROP_TABLE_IF_EXISTS('PHY_A2B_TAG');
    DROP_TABLE_IF_EXISTS('PHY_missing_tag_a');
    DROP_TABLE_IF_EXISTS('PHY_missing_tag_b');
    DROP_TABLE_IF_EXISTS('PHY_POST_DETAIL');
    DROP_TABLE_IF_EXISTS('PhysicalParameters');
    
    -- 计费表
    DROP_TABLE_IF_EXISTS('STORERBILLING');
    DROP_TABLE_IF_EXISTS('AccumulatedCharges');
    DROP_TABLE_IF_EXISTS('ACCUMULATEDCHARGES');
    DROP_TABLE_IF_EXISTS('LOTxBILLDATE');
    DROP_TABLE_IF_EXISTS('FxRATE');
    DROP_TABLE_IF_EXISTS('Tariff');
    DROP_TABLE_IF_EXISTS('TariffDetail');
    DROP_TABLE_IF_EXISTS('TaxRate');
    DROP_TABLE_IF_EXISTS('TaxGroup');
    DROP_TABLE_IF_EXISTS('TaxGroupDetail');
    DROP_TABLE_IF_EXISTS('ChartOfAccounts');
    DROP_TABLE_IF_EXISTS('GLDistribution');
    DROP_TABLE_IF_EXISTS('GLDistributionDetail');
    
    -- 策略表
    DROP_TABLE_IF_EXISTS('Strategy');
    DROP_TABLE_IF_EXISTS('PreAllocateStrategy');
    DROP_TABLE_IF_EXISTS('PreAllocateStrategyDetail');
    DROP_TABLE_IF_EXISTS('AllocateStrategy');
    DROP_TABLE_IF_EXISTS('AllocateStrategyDetail');
    DROP_TABLE_IF_EXISTS('PutawayStrategy');
    DROP_TABLE_IF_EXISTS('PutawayStrategyDetail');
    
    -- 任务管理表
    DROP_TABLE_IF_EXISTS('TaskDetail');
    DROP_TABLE_IF_EXISTS('TaskManagerUser');
    DROP_TABLE_IF_EXISTS('TaskManagerUserDetail');
    DROP_TABLE_IF_EXISTS('TTMStrategy');
    DROP_TABLE_IF_EXISTS('TTMStrategyDetail');
    
    -- 报表表
    DROP_TABLE_IF_EXISTS('pbsrpt_reports');
    DROP_TABLE_IF_EXISTS('pbsrpt_category');
    DROP_TABLE_IF_EXISTS('pbsrpt_parms');
    DROP_TABLE_IF_EXISTS('pbsrpt_sets');
    DROP_TABLE_IF_EXISTS('pbsrpt_set_reports');
END;
/

-- 清理临时对象
DROP PROCEDURE DROP_TABLE_IF_EXISTS;
DROP PROCEDURE DROP_SEQUENCE_IF_EXISTS;

-- 提交更改
COMMIT;

PROMPT =============================================
PROMPT Oracle版本 - 数据库对象清理完成！
PROMPT 完成时间：
SELECT TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS END_TIME FROM DUAL;
PROMPT =============================================

-- 重置环境参数
SET TIMING OFF;
SET PAGESIZE 14;
SET LINESIZE 80;
