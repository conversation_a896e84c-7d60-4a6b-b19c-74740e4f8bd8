-- =============================================
-- 创建存储过程脚本
-- 功能：创建仓库管理系统的核心存储过程
-- 用途：业务逻辑处理和数据操作
-- =============================================

-- =============================================
-- 错误日志存储过程
-- =============================================
IF OBJECT_ID('nsp_logerror') IS NOT NULL
DROP PROC nsp_logerror
GO

CREATE PROC nsp_logerror
@n_err        int
,              @c_errmsg     varchar(250)
,              @c_module     varchar(250)
AS
BEGIN
BEGIN TRANSACTION
INSERT         errlog
(
ErrorID
,         Module
,         ErrorText
)
VALUES
(
@n_err
,         @c_module
,         @c_errmsg
)
IF @@ERROR <> 0
BEGIN
ROLLBACK TRANSACTION
RETURN
END
COMMIT TRANSACTION
END
GO

-- =============================================
-- 获取键值存储过程
-- =============================================
IF OBJECT_ID('nspg_getkey') IS NOT NULL
DROP PROC nspg_getkey
GO

CREATE PROC nspg_getkey 
@keyname       char(18)
,              @fieldlength   int
,              @keystring     char(25)       OUTPUT
,              @b_Success     int            OUTPUT
,              @n_err         int            OUTPUT
,              @c_errmsg      char(250)      OUTPUT
,              @b_resultset   int       = 0
,              @n_batch       int       = 1
AS
BEGIN
DECLARE @n_continue int
,              @n_starttcnt int
,              @n_cnt int
,              @n_keycount int
,              @c_keycount char(25)

SELECT @n_continue = 1
SELECT @b_success = 1
SELECT @n_starttcnt = @@TRANCOUNT

IF @n_starttcnt = 0
BEGIN TRANSACTION
ELSE
SAVE TRANSACTION nspg_getkey

-- 获取当前键值
SELECT @n_keycount = keycount
FROM ncounter
WHERE keyname = @keyname

IF @@ROWCOUNT = 0
BEGIN
-- 如果键不存在，创建新键
INSERT ncounter (keyname, keycount)
VALUES (@keyname, @n_batch)
SELECT @n_keycount = @n_batch
END
ELSE
BEGIN
-- 更新键值
UPDATE ncounter
SET keycount = keycount + @n_batch
WHERE keyname = @keyname
SELECT @n_keycount = @n_keycount + @n_batch
END

-- 格式化返回的键字符串
SELECT @c_keycount = CONVERT(char(25), @n_keycount)
SELECT @keystring = RIGHT(REPLICATE('0', @fieldlength) + LTRIM(RTRIM(@c_keycount)), @fieldlength)

IF @n_starttcnt = 0
COMMIT TRANSACTION

IF @b_resultset = 1
BEGIN
SELECT @keystring AS KeyString
END

RETURN

ERROR_HANDLER:
SELECT @b_success = 0
IF @n_starttcnt = 0
ROLLBACK TRANSACTION
ELSE
ROLLBACK TRANSACTION nspg_getkey

END
GO

-- =============================================
-- 记录警报存储过程
-- =============================================
IF OBJECT_ID('nspLogAlert') IS NOT NULL
DROP PROC nspLogAlert
GO

CREATE PROC nspLogAlert
@c_modulename       char(30),                
@c_AlertMessage     char(255),          
@n_Severity         int       = NULL,   
@b_success          int OUTPUT,
@n_err              int OUTPUT,
@c_errmsg           char(250)OUTPUT
AS
BEGIN
DECLARE @n_continue int,      
@n_cnt         int,      
@c_alertkey    char(18),
@n_starttcnt   int

SELECT @n_continue = 1
SELECT @b_success = 1
SELECT @n_starttcnt = @@TRANCOUNT

IF @n_starttcnt = 0
BEGIN TRANSACTION
ELSE
SAVE TRANSACTION nspLogAlert

-- 生成警报键
EXEC nspg_getkey 'ALERT', 10, @c_alertkey OUTPUT, @b_success OUTPUT, @n_err OUTPUT, @c_errmsg OUTPUT

IF @b_success = 0
GOTO ERROR_HANDLER

-- 设置默认严重级别
IF @n_Severity IS NULL
SELECT @n_Severity = 5

-- 插入警报记录
INSERT ALERT
(
AlertKey,
ModuleName,
AlertMessage,
Severity
)
VALUES
(
@c_alertkey,
@c_modulename,
@c_AlertMessage,
@n_Severity
)

SELECT @n_cnt = @@ROWCOUNT
IF @n_cnt = 0 OR @@ERROR <> 0
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'Failed to insert alert record'
GOTO ERROR_HANDLER
END

IF @n_starttcnt = 0
COMMIT TRANSACTION

RETURN

ERROR_HANDLER:
SELECT @b_success = 0
IF @n_starttcnt = 0
ROLLBACK TRANSACTION
ELSE
ROLLBACK TRANSACTION nspLogAlert

END
GO

-- =============================================
-- UOM 转换存储过程
-- =============================================
IF OBJECT_ID('nspUOMCONV') IS NOT NULL
DROP PROC nspUOMCONV
GO

CREATE PROC nspUOMCONV
@n_fromqty          int
,              @c_fromuom          char(10)
,              @c_touom            char(10)
,              @c_packkey          char(10)
,              @n_toqty            int            OUTPUT
,              @b_Success          int            OUTPUT
,              @n_err              int            OUTPUT
,              @c_errmsg           char(250)      OUTPUT
,              @c_uominout         char(2) = "__" OUTPUT
AS
BEGIN
DECLARE @n_continue int
,              @n_starttcnt int
,              @n_cnt int
,              @n_fromconv float
,              @n_toconv float
,              @n_result float

SELECT @n_continue = 1
SELECT @b_success = 1
SELECT @n_starttcnt = @@TRANCOUNT

-- 初始化转换因子
SELECT @n_fromconv = 1
SELECT @n_toconv = 1

-- 获取源UOM的转换因子
IF @c_fromuom = 'CASE'
BEGIN
SELECT @n_fromconv = CaseCnt FROM PACK WHERE PackKey = @c_packkey
END
ELSE IF @c_fromuom = 'INNER'
BEGIN
SELECT @n_fromconv = InnerPack FROM PACK WHERE PackKey = @c_packkey
END
ELSE IF @c_fromuom = 'EACH'
BEGIN
SELECT @n_fromconv = Qty FROM PACK WHERE PackKey = @c_packkey
END
ELSE IF @c_fromuom = 'PALLET'
BEGIN
SELECT @n_fromconv = Pallet FROM PACK WHERE PackKey = @c_packkey
END

-- 获取目标UOM的转换因子
IF @c_touom = 'CASE'
BEGIN
SELECT @n_toconv = CaseCnt FROM PACK WHERE PackKey = @c_packkey
END
ELSE IF @c_touom = 'INNER'
BEGIN
SELECT @n_toconv = InnerPack FROM PACK WHERE PackKey = @c_packkey
END
ELSE IF @c_touom = 'EACH'
BEGIN
SELECT @n_toconv = Qty FROM PACK WHERE PackKey = @c_packkey
END
ELSE IF @c_touom = 'PALLET'
BEGIN
SELECT @n_toconv = Pallet FROM PACK WHERE PackKey = @c_packkey
END

-- 执行转换计算
IF @n_toconv = 0
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'Division by zero in UOM conversion'
GOTO ERROR_HANDLER
END

SELECT @n_result = (@n_fromqty * @n_fromconv) / @n_toconv
SELECT @n_toqty = CONVERT(int, @n_result)

SELECT @c_uominout = @c_fromuom + @c_touom

RETURN

ERROR_HANDLER:
SELECT @b_success = 0
SELECT @n_toqty = 0

END
GO

-- 注意：此脚本包含核心存储过程
-- 更多存储过程请参考原始文件或后续脚本
