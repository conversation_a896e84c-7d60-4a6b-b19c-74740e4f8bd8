-- =============================================
-- 创建盘点和报表表结构脚本 (第四部分)
-- 功能：创建仓库管理系统的盘点和报表相关表结构
-- 用途：物理盘点、报表系统、计费等专业模块
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 盘点相关表 (Physical Inventory Tables)
-- =============================================

-- PHY_A2B_ID 表 (盘点A队B队ID对比表)
CREATE TABLE PHY_A2B_ID (
StorerKey                char(15)  NOT NULL
CONSTRAINT DF_PHY_A2B_ID_StorerKey DEFAULT "" ,
Sku                      char(20)  NOT NULL
CONSTRAINT DF_PHY_A2B_ID_Sku DEFAULT "" ,
Loc                      char(10) NOT NULL
CONSTRAINT DF_PHY_A2B_ID_Loc DEFAULT "" ,
Id                       char(18) NOT NULL
CONSTRAINT DF_PHY_A2B_ID_Id DEFAULT "" ,
QtyTeamA                 int       NOT NULL
CONSTRAINT DF_PHY_A2B_ID_QtyTeamA DEFAULT 0 ,
QtyTeamB                 int       NOT NULL
CONSTRAINT DF_PHY_A2B_ID_QtyTeamB DEFAULT 0
)
GO

IF OBJECT_ID('PHY_A2B_ID') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_A2B_ID FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_A2B_ID>>>'
GRANT INSERT ON PHY_A2B_ID TO nsql
GRANT UPDATE ON PHY_A2B_ID TO nsql
GRANT DELETE ON PHY_A2B_ID TO nsql
GRANT SELECT ON PHY_A2B_ID TO nsql
END
GO

-- PHY_A2B_LOT 表 (盘点A队B队批次对比表)
CREATE TABLE PHY_A2B_LOT (
StorerKey                char(15)  NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_StorerKey DEFAULT "" ,
Sku                      char(20)  NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_Sku DEFAULT "" ,
Loc                      char(10) NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_Loc DEFAULT "" ,
Lot                      char(10) NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_Id DEFAULT "" ,
QtyTeamA                 int       NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_QtyTeamA DEFAULT 0 ,
QtyTeamB                 int       NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_QtyTeamB DEFAULT 0
)
GO

IF OBJECT_ID('PHY_A2B_LOT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_A2B_LOT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_A2B_LOT>>>'
GRANT INSERT ON PHY_A2B_LOT TO nsql
GRANT UPDATE ON PHY_A2B_LOT TO nsql
GRANT DELETE ON PHY_A2B_LOT TO nsql
GRANT SELECT ON PHY_A2B_LOT TO nsql
END
GO

-- PHY_A2B_SKU 表 (盘点A队B队SKU对比表)
CREATE TABLE PHY_A2B_SKU (
StorerKey                char(15)  NOT NULL
CONSTRAINT DF_PHY_A2B_SKU_StorerKey DEFAULT "" ,
Sku                      char(20)  NOT NULL
CONSTRAINT DF_PHY_A2B_SKU_Sku DEFAULT "" ,
QtyTeamA                 int       NOT NULL
CONSTRAINT DF_PHY_A2B_SKU_QtyTeamA DEFAULT 0 ,
QtyTeamB                 int       NOT NULL
CONSTRAINT DF_PHY_A2B_SKU_QtyTeamB DEFAULT 0
)
GO

IF OBJECT_ID('PHY_A2B_SKU') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_A2B_SKU FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_A2B_SKU>>>'
GRANT INSERT ON PHY_A2B_SKU TO nsql
GRANT UPDATE ON PHY_A2B_SKU TO nsql
GRANT DELETE ON PHY_A2B_SKU TO nsql
GRANT SELECT ON PHY_A2B_SKU TO nsql
END
GO

-- PHY_A2B_TAG 表 (盘点A队B队标签对比表)
CREATE TABLE PHY_A2B_TAG (
StorerKey                char(15)  NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_StorerKey DEFAULT "" ,
Sku                      char(20)  NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_Sku DEFAULT "" ,
Loc                      char(10)  NOT NULL
CONSTRAINT DF_PHY_A2B_Loc DEFAULT "UNKNOWN" ,
Id                       char(18)  NOT NULL
CONSTRAINT DF_PHY_A2B_Id DEFAULT "" ,
InventoryTag             char(18)  NOT NULL
CONSTRAINT DF_PHY_A2B_InventoryTag DEFAULT "" ,
QtyTeamA                 int       NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_QtyTeamA DEFAULT 0 ,
QtyTeamB                 int       NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_QtyTeamB DEFAULT 0
)
GO

IF OBJECT_ID('PHY_A2B_TAG') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_A2B_TAG FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_A2B_TAG>>>'
GRANT INSERT ON PHY_A2B_TAG TO nsql
GRANT UPDATE ON PHY_A2B_TAG TO nsql
GRANT DELETE ON PHY_A2B_TAG TO nsql
GRANT SELECT ON PHY_A2B_TAG TO nsql
END
GO

-- PHY_missing_tag_a 表 (盘点缺失标签A表)
CREATE TABLE PHY_missing_tag_a (
InventoryTag char(18) NOT NULL
CONSTRAINT DF_PHY_mis_tag_a DEFAULT ""
)
GO

IF OBJECT_ID('PHY_missing_tag_a') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_missing_tag_a FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_missing_tag_a>>>'
GRANT INSERT ON PHY_missing_tag_a TO nsql
GRANT UPDATE ON PHY_missing_tag_a TO nsql
GRANT DELETE ON PHY_missing_tag_a TO nsql
GRANT SELECT ON PHY_missing_tag_a TO nsql
END
GO

-- PHY_missing_tag_b 表 (盘点缺失标签B表)
CREATE TABLE PHY_missing_tag_b (
InventoryTag char(18)  NOT NULL
CONSTRAINT DF_PHY_mis_tag_b DEFAULT ""
)
GO

IF OBJECT_ID('PHY_missing_tag_b') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_missing_tag_b FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_missing_tag_b>>>'
GRANT INSERT ON PHY_missing_tag_b TO nsql
GRANT UPDATE ON PHY_missing_tag_b TO nsql
GRANT DELETE ON PHY_missing_tag_b TO nsql
GRANT SELECT ON PHY_missing_tag_b TO nsql
END
GO

-- PHY_POST_DETAIL 表 (盘点过账明细表)
CREATE TABLE PHY_POST_DETAIL (
StorerKey char(15) NOT NULL
CONSTRAINT DF_PPDET_StorerKey DEFAULT "" ,
Sku char(20) NOT NULL
CONSTRAINT DF_PPDET_Sku DEFAULT "" ,
Loc char(10) NOT NULL
CONSTRAINT DF_PPDET_Loc DEFAULT "" ,
Lot char(10) NOT NULL
CONSTRAINT DF_PPDET_Lot DEFAULT "" ,
Id char(18) NOT NULL
CONSTRAINT DF_PPDET_Id DEFAULT "" ,
QtyTeamA int NOT NULL
CONSTRAINT DF_PPDET_QtyTeamA DEFAULT 0 ,
QtyLOTxLOCxID int NOT NULL
CONSTRAINT DF_PPDET_QtyLOTxLOCxID DEFAULT 0
)
GO

IF OBJECT_ID('PHY_POST_DETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_POST_DETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_POST_DETAIL>>>'
GRANT INSERT ON PHY_POST_DETAIL TO nsql
GRANT UPDATE ON PHY_POST_DETAIL TO nsql
GRANT DELETE ON PHY_POST_DETAIL TO nsql
GRANT SELECT ON PHY_POST_DETAIL TO nsql
END
GO

-- PhysicalParameters 表 (盘点参数表)
CREATE TABLE PhysicalParameters (
PhysicalParmKey          int       NOT NULL
CONSTRAINT DF_PhysParams_PhysicalParmKey DEFAULT "" ,
StorerKeyMin             char(15)  NOT NULL
CONSTRAINT DF_PhysParams_StorerKeyMin DEFAULT "" ,
StorerKeyMax             char(15)  NOT NULL
CONSTRAINT DF_PhysParams_StorerKeyMax DEFAULT "" ,
SkuMin                   char(20)  NOT NULL
CONSTRAINT DF_PhysParams_SkuMin DEFAULT "" ,
SkuMax                   char(20)  NOT NULL
CONSTRAINT DF_PhysParams_SkuMax DEFAULT ""
)
GO

IF OBJECT_ID('PhysicalParameters') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PhysicalParameters FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PhysicalParameters>>>'
GRANT INSERT ON PhysicalParameters TO nsql
GRANT UPDATE ON PhysicalParameters TO nsql
GRANT DELETE ON PhysicalParameters TO nsql
GRANT SELECT ON PhysicalParameters TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 报表系统相关表 (Report System Tables)
-- =============================================

-- pbsrpt_reports 表 (报表定义表)
CREATE TABLE pbsrpt_reports
(rpt_id char(8) NOT NULL,
rpt_datawindow varchar(40) NULL,
rpt_library varchar(80) NULL,
rpt_title varchar(100) NULL,
rpt_purpose varchar(255) NULL,
rpt_descr varchar(255) NULL,
rpt_header char(1) NULL,
rpt_active char(1) NULL,
rpt_type int NULL,
rpt_where varchar(255) NULL,
rpt_filter varchar(255) NULL,
rpt_sort varchar(255) NULL,
enable_filter char(1) NULL,
enable_sort char(1) NULL,
autoretrieve char(1) NULL,
category_id int NULL,
show_criteria char(1) NULL,
query_mode char(1) NULL,
shared_rpt_id char(8) NULL
)
GO

IF OBJECT_ID('PBSRPT_REPORTS') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PBSRPT_REPORTS FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PBSRPT_REPORTS>>>'
GRANT INSERT ON PBSRPT_REPORTS TO nsql
GRANT UPDATE ON PBSRPT_REPORTS TO nsql
GRANT DELETE ON PBSRPT_REPORTS TO nsql
GRANT SELECT ON PBSRPT_REPORTS TO nsql
END
GO

-- pbsrpt_category 表 (报表分类表)
CREATE TABLE pbsrpt_category
(category_id int NOT NULL,
category varchar(40) NOT NULL )
GO

IF OBJECT_ID('PBSRPT_CATEGORY') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PBSRPT_CATEGORY FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PBSRPT_CATEGORY>>>'
GRANT INSERT ON PBSRPT_CATEGORY TO nsql
GRANT UPDATE ON PBSRPT_CATEGORY TO nsql
GRANT DELETE ON PBSRPT_CATEGORY TO nsql
GRANT SELECT ON PBSRPT_CATEGORY TO nsql
END
GO

-- pbsrpt_parms 表 (报表参数表)
CREATE TABLE pbsrpt_parms
(rpt_id char(8) NOT NULL,
parm_no tinyint NOT NULL,
parm_datatype varchar(20) NULL,
parm_label varchar(30) NULL,
parm_default varchar(30) NULL,
style varchar(10) NULL,
name varchar(40) NULL,
display varchar(40) NULL,
data varchar(40) NULL,
attributes varchar(100) NULL,
visible char(1) NULL
)
GO

IF OBJECT_ID('PBSRPT_PARMS') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PBSRPT_PARMS FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PBSRPT_PARMS>>>'
GRANT INSERT ON PBSRPT_PARMS TO nsql
GRANT UPDATE ON PBSRPT_PARMS TO nsql
GRANT DELETE ON PBSRPT_PARMS TO nsql
GRANT SELECT ON PBSRPT_PARMS TO nsql
END
GO

-- pbsrpt_sets 表 (报表集合表)
CREATE TABLE pbsrpt_sets
(rpt_set_id tinyint NOT NULL,
name varchar(100) NOT NULL
)
GO

IF OBJECT_ID('PBSRPT_SETS') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PBSRPT_SETS FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PBSRPT_SETS>>>'
GRANT INSERT ON PBSRPT_SETS TO nsql
GRANT UPDATE ON PBSRPT_SETS TO nsql
GRANT DELETE ON PBSRPT_SETS TO nsql
GRANT SELECT ON PBSRPT_SETS TO nsql
END
GO

-- pbsrpt_set_reports 表 (报表集合明细表)
CREATE TABLE pbsrpt_set_reports
(rpt_set_id tinyint NOT NULL,
rpt_seq tinyint NOT NULL,
rpt_id char(8)  NULL
)
GO

IF OBJECT_ID('PBSRPT_SET_REPORTS') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PBSRPT_SET_REPORTS FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PBSRPT_SET_REPORTS>>>'
GRANT INSERT ON PBSRPT_SET_REPORTS TO nsql
GRANT UPDATE ON PBSRPT_SET_REPORTS TO nsql
GRANT DELETE ON PBSRPT_SET_REPORTS TO nsql
GRANT SELECT ON PBSRPT_SET_REPORTS TO nsql
END
GO

-- =============================================
-- 计费相关表 (Billing Tables)
-- =============================================

-- LOTxBILLDATE 表 (批次计费日期表)
CREATE TABLE LOTxBILLDATE
( Lot                char(10) NOT NULL,
TariffKey             char(10) NOT NULL,
LotBillThruDate    datetime NOT NULL
CONSTRAINT DF_LOTxBILLDATE_LotBillThruDat DEFAULT CURRENT_Timestamp,
LastActivity       datetime NOT NULL
CONSTRAINT DF_LOTxBILLDATE_LastActivity  DEFAULT CURRENT_Timestamp,
QtyBilledBalance   int NOT NULL
CONSTRAINT DF_LOTxBILLDATE_QtyB_Bal DEFAULT 0,
QtyBilledGrossWeight    float NOT NULL
CONSTRAINT DF_LOTxBILLDATE_QtyBilled_G_W DEFAULT 0,
QtyBilledNetWeight      float NOT NULL
CONSTRAINT DF_LOTxBILLDATE_QtyBilled_N_W DEFAULT 0,
QtyBilledCube           float NOT NULL
CONSTRAINT DF_LOTxBILLDATE_Cube DEFAULT 0,
AnniversaryStartDate datetime NULL,
AddDate            datetime NOT NULL
CONSTRAINT DF_LOTxBILLDATE_AddDate DEFAULT CURRENT_Timestamp,
AddWho             char(18) NOT NULL
CONSTRAINT DF_LOTxBILLDATE_AddWho DEFAULT USER ,
EditDate           datetime NOT NULL
CONSTRAINT DF_LOTxBILLDATE_EditDate DEFAULT CURRENT_Timestamp,
EditWho            char(18) NOT NULL
CONSTRAINT DF_LOTxBILLDATE_EditWho DEFAULT USER
)
go

IF OBJECT_ID('LOTxBILLDATE') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LOTxBILLDATE FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LOTxBILLDATE>>>'
GRANT INSERT ON LOTxBILLDATE TO nsql
GRANT UPDATE ON LOTxBILLDATE TO nsql
GRANT DELETE ON LOTxBILLDATE TO nsql
GRANT SELECT ON LOTxBILLDATE TO nsql
END
GO

-- FxRATE 表 (汇率表)
CREATE TABLE FxRATE
( CurrencyKey        char(10) NOT NULL
CONSTRAINT DF_FxRATE_CurrencyKey DEFAULT "",
Descrip            char(30) NOT NULL
CONSTRAINT DF_FxRATE_Descrip DEFAULT "",
BaseCurrency       char(10) NOT NULL
CONSTRAINT DF_FxRATE_BaseCurrency DEFAULT "USD",
TargetCurrency     char(10)
CONSTRAINT DF_FxRATE_TargetCurrency DEFAULT "USD",
ConversionRate     Decimal (8,4) NOT NULL
CONSTRAINT DF_FxRATE_ConversionRate DEFAULT 1.0,
FxDate      datetime NOT NULL
CONSTRAINT DF_FxRATE_FxDate  DEFAULT CURRENT_Timestamp,
AddDate            datetime NOT NULL
CONSTRAINT DF_FxRATE_AddDate DEFAULT CURRENT_Timestamp,
AddWho             char(18) NOT NULL
CONSTRAINT DF_FxRATE_AddWho DEFAULT USER ,
EditDate           datetime NOT NULL
CONSTRAINT DF_FxRATE_EditDate DEFAULT CURRENT_Timestamp,
EditWho            char(18) NOT NULL
CONSTRAINT DF_FxRATE_EditWho DEFAULT USER
)
go

IF OBJECT_ID('FxRATE') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE FxRATE FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE FxRATE>>>'
GRANT INSERT ON FxRATE TO nsql
GRANT UPDATE ON FxRATE TO nsql
GRANT DELETE ON FxRATE TO nsql
GRANT SELECT ON FxRATE TO nsql
END
GO

-- =============================================
-- 更多盘点相关表
-- =============================================

-- PHY_INV2A_SKU 表 (盘点库存对比SKU表)
CREATE TABLE PHY_INV2A_SKU (
StorerKey                char(15)  NOT NULL
CONSTRAINT DF_PHY_INV2A_SKU_StorerKey DEFAULT "" ,
Sku                      char(20)  NOT NULL
CONSTRAINT DF_PHY_INV2A_SKU_Sku DEFAULT "" ,
QtyTeamA                 int       NOT NULL
CONSTRAINT DF_PHY_INV2A_SKU_QtyTeamA DEFAULT 0 ,
QtyInventory             int       NOT NULL
CONSTRAINT DF_PHY_INV2A_SKU_QtyInventory DEFAULT 0
)
GO

IF OBJECT_ID('PHY_INV2A_SKU') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_INV2A_SKU FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_INV2A_SKU>>>'
GRANT INSERT ON PHY_INV2A_SKU TO nsql
GRANT UPDATE ON PHY_INV2A_SKU TO nsql
GRANT DELETE ON PHY_INV2A_SKU TO nsql
GRANT SELECT ON PHY_INV2A_SKU TO nsql
END
GO

-- PHY_INV2A_LOT 表 (盘点库存对比批次表)
CREATE TABLE PHY_INV2A_LOT (
StorerKey                char(15)  NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_StorerKey DEFAULT "" ,
Sku                      char(20)  NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_Sku DEFAULT "" ,
Lot                      char(10)  NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_Lot DEFAULT "" ,
QtyTeamA                 int       NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_QtyTeamA DEFAULT 0 ,
QtyInventory             int       NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_QtyInventory DEFAULT 0
)
GO

IF OBJECT_ID('PHY_INV2A_LOT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_INV2A_LOT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_INV2A_LOT>>>'
GRANT INSERT ON PHY_INV2A_LOT TO nsql
GRANT UPDATE ON PHY_INV2A_LOT TO nsql
GRANT DELETE ON PHY_INV2A_LOT TO nsql
GRANT SELECT ON PHY_INV2A_LOT TO nsql
END
GO

-- PHY_INV2A_ID 表 (盘点库存对比ID表)
CREATE TABLE PHY_INV2A_ID (
StorerKey                char(15)  NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_StorerKey DEFAULT "" ,
Sku                      char(20)  NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_Sku DEFAULT "" ,
Id                       char(18)  NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_Id DEFAULT "" ,
QtyTeamA                 int       NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_QtyTeamA DEFAULT 0 ,
QtyInventory             int       NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_QtyInventory DEFAULT 0
)
GO

IF OBJECT_ID('PHY_INV2A_ID') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_INV2A_ID FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_INV2A_ID>>>'
GRANT INSERT ON PHY_INV2A_ID TO nsql
GRANT UPDATE ON PHY_INV2A_ID TO nsql
GRANT DELETE ON PHY_INV2A_ID TO nsql
GRANT SELECT ON PHY_INV2A_ID TO nsql
END
GO

-- PHY_outofrange_tag_a 表 (盘点超范围标签A表)
CREATE TABLE PHY_outofrange_tag_a (
InventoryTag char(18) NOT NULL
CONSTRAINT DF_PHY_outrange_a DEFAULT ""
)
GO

IF OBJECT_ID('PHY_outofrange_tag_a') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_outofrange_tag_a FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_outofrange_tag_a>>>'
GRANT INSERT ON PHY_outofrange_tag_a TO nsql
GRANT UPDATE ON PHY_outofrange_tag_a TO nsql
GRANT DELETE ON PHY_outofrange_tag_a TO nsql
GRANT SELECT ON PHY_outofrange_tag_a TO nsql
END
GO

-- PHY_outofrange_tag_b 表 (盘点超范围标签B表)
CREATE TABLE PHY_outofrange_tag_b (
InventoryTag char(18) NOT NULL
CONSTRAINT DF_PHY_outrange_b DEFAULT ""
)
GO

IF OBJECT_ID('PHY_outofrange_tag_b') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_outofrange_tag_b FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_outofrange_tag_b>>>'
GRANT INSERT ON PHY_outofrange_tag_b TO nsql
GRANT UPDATE ON PHY_outofrange_tag_b TO nsql
GRANT DELETE ON PHY_outofrange_tag_b TO nsql
GRANT SELECT ON PHY_outofrange_tag_b TO nsql
END
GO

-- PHY_POSTED 表 (盘点已过账表)
CREATE TABLE PHY_POSTED (
StorerKey                char(15)  NOT NULL
CONSTRAINT DF_PHY_POSTED_StorerKey DEFAULT "" ,
Sku                      char(20)  NOT NULL
CONSTRAINT DF_PHY_POSTED_Sku DEFAULT "" ,
QtyTeamA                 int       NOT NULL
CONSTRAINT DF_PHY_POSTED_QtyTeamA DEFAULT 0 ,
QtyInventory             int       NOT NULL
CONSTRAINT DF_PHY_POSTED_QtyInventory DEFAULT 0
)
GO

IF OBJECT_ID('PHY_POSTED') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHY_POSTED FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHY_POSTED>>>'
GRANT INSERT ON PHY_POSTED TO nsql
GRANT UPDATE ON PHY_POSTED TO nsql
GRANT DELETE ON PHY_POSTED TO nsql
GRANT SELECT ON PHY_POSTED TO nsql
END
GO

-- 注意：此脚本包含盘点和报表相关表结构
-- 完整的表结构请参考原始 NEPISQL.sql 文件
