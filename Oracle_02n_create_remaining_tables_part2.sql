-- =============================================
-- Oracle版本 - 创建剩余表结构脚本 (第十四部分)
-- 功能：创建仓库管理系统中剩余的重要表结构 (Oracle版本)
-- 用途：设备管理、区域管理、帮助系统、BOM管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 设备和区域管理表
-- =============================================

-- EquipmentProfile 表 (设备配置表)
CREATE TABLE EquipmentProfile
(EquipmentProfileKey      CHAR(10) NOT NULL
CONSTRAINT DF_EP_Key DEFAULT ' ',
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_EP_Descr DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_EP_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_EP_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_EP_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_EP_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE EquipmentProfile ADD CONSTRAINT PK_EquipmentProfile PRIMARY KEY (EquipmentProfileKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON EquipmentProfile TO nsql;

PROMPT '>>> 已创建表 EquipmentProfile (设备配置表)';

-- PutawayZone 表 (上架区域表)
CREATE TABLE PutawayZone
(PutawayZone              CHAR(10) NOT NULL
CONSTRAINT DF_PZ_Key DEFAULT ' ',
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_PZ_Descr DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_PZ_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PZ_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PZ_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PZ_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PutawayZone ADD CONSTRAINT PK_PutawayZone PRIMARY KEY (PutawayZone);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PutawayZone TO nsql;

PROMPT '>>> 已创建表 PutawayZone (上架区域表)';

-- PAZoneEquipmentExcludeDetail 表 (上架区域设备排除明细表)
CREATE TABLE PAZoneEquipmentExcludeDetail
(PutawayZone              CHAR(10) NOT NULL
CONSTRAINT DF_PZEPXD_Key DEFAULT ' ',
EquipmentProfileKey      CHAR(10) NOT NULL
CONSTRAINT DF_PZEPXD_EquipmentProfileKey DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_PZEPXD_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PZEPXD_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PZEPXD_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PZEPXD_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PAZoneEquipmentExcludeDetail ADD CONSTRAINT PK_PAZoneEquipmentExcludeDetail 
PRIMARY KEY (PutawayZone, EquipmentProfileKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PAZoneEquipmentExcludeDetail TO nsql;

PROMPT '>>> 已创建表 PAZoneEquipmentExcludeDetail (上架区域设备排除明细表)';

-- AreaDetail 表 (区域明细表)
CREATE TABLE AreaDetail
(AreaKey       CHAR(10) NOT NULL
CONSTRAINT DF_AD_Key DEFAULT ' ',
PutawayZone   CHAR(10) NOT NULL
CONSTRAINT DF_AD_PutawayZone DEFAULT ' ',
AddDate       DATE NOT NULL
CONSTRAINT DF_AD_AddDate DEFAULT SYSDATE,
AddWho        CHAR(18) NOT NULL
CONSTRAINT DF_AD_AddWho DEFAULT USER,
EditDate      DATE NOT NULL
CONSTRAINT DF_AD_EditDate DEFAULT SYSDATE,
EditWho       CHAR(18) NOT NULL
CONSTRAINT DF_AD_EditWho DEFAULT USER,
TrafficCop    CHAR(1) NULL,
ArchiveCop    CHAR(1) NULL
);

-- 添加主键
ALTER TABLE AreaDetail ADD CONSTRAINT PK_AreaDetail PRIMARY KEY (AreaKey, PutawayZone);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON AreaDetail TO nsql;

PROMPT '>>> 已创建表 AreaDetail (区域明细表)';

-- Section 表 (区段表)
CREATE TABLE Section
(SectionKey    CHAR(10) NOT NULL
CONSTRAINT DF_S_Key DEFAULT ' ',
Descr         CHAR(60) NOT NULL
CONSTRAINT DF_S_Descr DEFAULT ' ',
AddDate       DATE NOT NULL
CONSTRAINT DF_S_AddDate DEFAULT SYSDATE,
AddWho        CHAR(18) NOT NULL
CONSTRAINT DF_S_AddWho DEFAULT USER,
EditDate      DATE NOT NULL
CONSTRAINT DF_S_EditDate DEFAULT SYSDATE,
EditWho       CHAR(18) NOT NULL
CONSTRAINT DF_S_EditWho DEFAULT USER,
TrafficCop    CHAR(1) NULL,
ArchiveCop    CHAR(1) NULL
);

-- 添加主键
ALTER TABLE Section ADD CONSTRAINT PK_Section PRIMARY KEY (SectionKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Section TO nsql;

PROMPT '>>> 已创建表 Section (区段表)';

-- =============================================
-- 任务管理原因表
-- =============================================

-- TaskManagerReason 表 (任务管理原因表)
CREATE TABLE TaskManagerReason
(TaskManagerReasonKey     CHAR(10) NOT NULL
CONSTRAINT DF_TMR_Key DEFAULT ' ',
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_TMR_Descr DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_TMR_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TMR_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TMR_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TMR_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TaskManagerReason ADD CONSTRAINT PK_TaskManagerReason PRIMARY KEY (TaskManagerReasonKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaskManagerReason TO nsql;

PROMPT '>>> 已创建表 TaskManagerReason (任务管理原因表)';

-- TaskManagerSkipTasks 表 (任务管理跳过任务表)
CREATE TABLE TaskManagerSkipTasks
(USERID         CHAR(18) NOT NULL,
TaskDetailKey  CHAR(10) NOT NULL,
SkipReason     CHAR(60) NULL,
AddDate        DATE NOT NULL
CONSTRAINT DF_TMST_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_TMST_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_TMST_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_TMST_EditWho DEFAULT USER,
TrafficCop     CHAR(1) NULL,
ArchiveCop     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TaskManagerSkipTasks ADD CONSTRAINT PK_TaskManagerSkipTasks 
PRIMARY KEY (USERID, TaskDetailKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaskManagerSkipTasks TO nsql;

PROMPT '>>> 已创建表 TaskManagerSkipTasks (任务管理跳过任务表)';

-- =============================================
-- 帮助系统表
-- =============================================

-- help 表 (帮助表)
CREATE TABLE help
(topic            CHAR(64) NOT NULL
CONSTRAINT DF_H_topic DEFAULT ' ',
context          CHAR(128) NOT NULL
CONSTRAINT DF_H_context DEFAULT ' ',
helptext         CLOB NULL,
AddDate          DATE NOT NULL
CONSTRAINT DF_H_AddDate DEFAULT SYSDATE,
AddWho           CHAR(18) NOT NULL
CONSTRAINT DF_H_AddWho DEFAULT USER,
EditDate         DATE NOT NULL
CONSTRAINT DF_H_EditDate DEFAULT SYSDATE,
EditWho          CHAR(18) NOT NULL
CONSTRAINT DF_H_EditWho DEFAULT USER,
TrafficCop       CHAR(1) NULL,
ArchiveCop       CHAR(1) NULL
);

-- 添加主键
ALTER TABLE help ADD CONSTRAINT PK_help PRIMARY KEY (topic, context);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON help TO nsql;

PROMPT '>>> 已创建表 help (帮助表)';

-- =============================================
-- BOM管理表
-- =============================================

-- BillOfMaterial 表 (物料清单表)
CREATE TABLE BillOfMaterial
(Storerkey     CHAR(15) NOT NULL
CONSTRAINT DF_BOM_storerkey DEFAULT ' ',
Sku           CHAR(20) NOT NULL
CONSTRAINT DF_BOM_sku DEFAULT ' ',
ComponentSku  CHAR(20) NOT NULL
CONSTRAINT DF_BOM_ComponentSku DEFAULT ' ',
Qty           NUMBER(10) NOT NULL
CONSTRAINT DF_BOM_Qty DEFAULT 0,
AddDate       DATE NOT NULL
CONSTRAINT DF_BOM_AddDate DEFAULT SYSDATE,
AddWho        CHAR(18) NOT NULL
CONSTRAINT DF_BOM_AddWho DEFAULT USER,
EditDate      DATE NOT NULL
CONSTRAINT DF_BOM_EditDate DEFAULT SYSDATE,
EditWho       CHAR(18) NOT NULL
CONSTRAINT DF_BOM_EditWho DEFAULT USER,
TrafficCop    CHAR(1) NULL,
ArchiveCop    CHAR(1) NULL
);

-- 添加主键
ALTER TABLE BillOfMaterial ADD CONSTRAINT PK_BillOfMaterial 
PRIMARY KEY (Storerkey, Sku, ComponentSku);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BillOfMaterial TO nsql;

PROMPT '>>> 已创建表 BillOfMaterial (物料清单表)';

-- 提交事务
COMMIT;

PROMPT '>>> 剩余表结构脚本第二部分执行完成';
PROMPT '>>> 已创建设备管理、区域管理、帮助系统、BOM管理表 (9张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
