-- =============================================
-- Oracle版本 - 创建盘点和报表表结构脚本 (第四部分)
-- 功能：创建仓库管理系统的盘点和报表相关表结构 (Oracle版本)
-- 用途：物理盘点、报表系统、计费等专业模块
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 盘点相关表 (Physical Inventory Tables)
-- =============================================

-- PHY_A2B_ID 表 (盘点A队B队ID对比表)
CREATE TABLE PHY_A2B_ID (
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PHY_A2B_ID_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_PHY_A2B_ID_Sku DEFAULT ' ',
Loc                      CHAR(10) NOT NULL
CONSTRAINT DF_PHY_A2B_ID_Loc DEFAULT ' ',
Id                       CHAR(18) NOT NULL
CONSTRAINT DF_PHY_A2B_ID_Id DEFAULT ' ',
QtyTeamA                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_A2B_ID_QtyTeamA DEFAULT 0,
QtyTeamB                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_A2B_ID_QtyTeamB DEFAULT 0
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_A2B_ID TO nsql;

PROMPT '>>> 已创建表 PHY_A2B_ID (盘点A队B队ID对比表)';

-- PHY_A2B_LOT 表 (盘点A队B队批次对比表)
CREATE TABLE PHY_A2B_LOT (
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_Sku DEFAULT ' ',
Loc                      CHAR(10) NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_Loc DEFAULT ' ',
Lot                      CHAR(10) NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_Lot DEFAULT ' ',
QtyTeamA                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_QtyTeamA DEFAULT 0,
QtyTeamB                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_A2B_LOT_QtyTeamB DEFAULT 0
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_A2B_LOT TO nsql;

PROMPT '>>> 已创建表 PHY_A2B_LOT (盘点A队B队批次对比表)';

-- PHY_A2B_SKU 表 (盘点A队B队SKU对比表)
CREATE TABLE PHY_A2B_SKU (
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PHY_A2B_SKU_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_PHY_A2B_SKU_Sku DEFAULT ' ',
QtyTeamA                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_A2B_SKU_QtyTeamA DEFAULT 0,
QtyTeamB                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_A2B_SKU_QtyTeamB DEFAULT 0
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_A2B_SKU TO nsql;

PROMPT '>>> 已创建表 PHY_A2B_SKU (盘点A队B队SKU对比表)';

-- PHY_A2B_TAG 表 (盘点A队B队标签对比表)
CREATE TABLE PHY_A2B_TAG (
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_Sku DEFAULT ' ',
Loc                      CHAR(10) NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_Loc DEFAULT 'UNKNOWN',
Id                       CHAR(18) NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_Id DEFAULT ' ',
InventoryTag             CHAR(18) NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_InventoryTag DEFAULT ' ',
QtyTeamA                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_QtyTeamA DEFAULT 0,
QtyTeamB                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_A2B_TAG_QtyTeamB DEFAULT 0
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_A2B_TAG TO nsql;

PROMPT '>>> 已创建表 PHY_A2B_TAG (盘点A队B队标签对比表)';

-- PHY_missing_tag_a 表 (盘点缺失标签A表)
CREATE TABLE PHY_missing_tag_a (
InventoryTag CHAR(18) NOT NULL
CONSTRAINT DF_PHY_missing_tag_a DEFAULT ' '
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_missing_tag_a TO nsql;

PROMPT '>>> 已创建表 PHY_missing_tag_a (盘点缺失标签A表)';

-- PHY_missing_tag_b 表 (盘点缺失标签B表)
CREATE TABLE PHY_missing_tag_b (
InventoryTag CHAR(18) NOT NULL
CONSTRAINT DF_PHY_missing_tag_b DEFAULT ' '
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_missing_tag_b TO nsql;

PROMPT '>>> 已创建表 PHY_missing_tag_b (盘点缺失标签B表)';

-- PHY_POST_DETAIL 表 (盘点过账明细表)
CREATE TABLE PHY_POST_DETAIL (
StorerKey CHAR(15) NOT NULL
CONSTRAINT DF_PHY_POST_DETAIL_StorerKey DEFAULT ' ',
Sku CHAR(20) NOT NULL
CONSTRAINT DF_PHY_POST_DETAIL_Sku DEFAULT ' ',
Loc CHAR(10) NOT NULL
CONSTRAINT DF_PHY_POST_DETAIL_Loc DEFAULT ' ',
Lot CHAR(10) NOT NULL
CONSTRAINT DF_PHY_POST_DETAIL_Lot DEFAULT ' ',
Id CHAR(18) NOT NULL
CONSTRAINT DF_PHY_POST_DETAIL_Id DEFAULT ' ',
QtyTeamA NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_POST_DETAIL_QtyTeamA DEFAULT 0,
QtyLOTxLOCxID NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_POST_DETAIL_QtyLOTxLOCxID DEFAULT 0
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_POST_DETAIL TO nsql;

PROMPT '>>> 已创建表 PHY_POST_DETAIL (盘点过账明细表)';

-- PhysicalParameters 表 (盘点参数表)
CREATE TABLE PhysicalParameters (
PhysicalParmKey          NUMBER(10) NOT NULL
CONSTRAINT DF_PhysicalParameters_PhysicalParmKey DEFAULT 0,
StorerKeyMin             CHAR(15) NOT NULL
CONSTRAINT DF_PhysicalParameters_StorerKeyMin DEFAULT ' ',
StorerKeyMax             CHAR(15) NOT NULL
CONSTRAINT DF_PhysicalParameters_StorerKeyMax DEFAULT ' ',
SkuMin                   CHAR(20) NOT NULL
CONSTRAINT DF_PhysicalParameters_SkuMin DEFAULT ' ',
SkuMax                   CHAR(20) NOT NULL
CONSTRAINT DF_PhysicalParameters_SkuMax DEFAULT ' '
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PhysicalParameters TO nsql;

PROMPT '>>> 已创建表 PhysicalParameters (盘点参数表)';

-- PHY_INV2A_SKU 表 (盘点库存对比SKU表)
CREATE TABLE PHY_INV2A_SKU (
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PHY_INV2A_SKU_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_PHY_INV2A_SKU_Sku DEFAULT ' ',
QtyTeamA                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_INV2A_SKU_QtyTeamA DEFAULT 0,
QtyInventory             NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_INV2A_SKU_QtyInventory DEFAULT 0
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_INV2A_SKU TO nsql;

PROMPT '>>> 已创建表 PHY_INV2A_SKU (盘点库存对比SKU表)';

-- PHY_INV2A_LOT 表 (盘点库存对比批次表)
CREATE TABLE PHY_INV2A_LOT (
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_Sku DEFAULT ' ',
Lot                      CHAR(10) NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_Lot DEFAULT ' ',
QtyTeamA                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_QtyTeamA DEFAULT 0,
QtyInventory             NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_INV2A_LOT_QtyInventory DEFAULT 0
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_INV2A_LOT TO nsql;

PROMPT '>>> 已创建表 PHY_INV2A_LOT (盘点库存对比批次表)';

-- PHY_INV2A_ID 表 (盘点库存对比ID表)
CREATE TABLE PHY_INV2A_ID (
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_Sku DEFAULT ' ',
Id                       CHAR(18) NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_Id DEFAULT ' ',
QtyTeamA                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_QtyTeamA DEFAULT 0,
QtyInventory             NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_INV2A_ID_QtyInventory DEFAULT 0
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_INV2A_ID TO nsql;

PROMPT '>>> 已创建表 PHY_INV2A_ID (盘点库存对比ID表)';

-- PHY_outofrange_tag_a 表 (盘点超范围标签A表)
CREATE TABLE PHY_outofrange_tag_a (
InventoryTag CHAR(18) NOT NULL
CONSTRAINT DF_PHY_outofrange_tag_a DEFAULT ' '
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_outofrange_tag_a TO nsql;

PROMPT '>>> 已创建表 PHY_outofrange_tag_a (盘点超范围标签A表)';

-- PHY_outofrange_tag_b 表 (盘点超范围标签B表)
CREATE TABLE PHY_outofrange_tag_b (
InventoryTag CHAR(18) NOT NULL
CONSTRAINT DF_PHY_outofrange_tag_b DEFAULT ' '
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_outofrange_tag_b TO nsql;

PROMPT '>>> 已创建表 PHY_outofrange_tag_b (盘点超范围标签B表)';

-- PHY_POSTED 表 (盘点已过账表)
CREATE TABLE PHY_POSTED (
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PHY_POSTED_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_PHY_POSTED_Sku DEFAULT ' ',
QtyTeamA                 NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_POSTED_QtyTeamA DEFAULT 0,
QtyInventory             NUMBER(10) NOT NULL
CONSTRAINT DF_PHY_POSTED_QtyInventory DEFAULT 0
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PHY_POSTED TO nsql;

PROMPT '>>> 已创建表 PHY_POSTED (盘点已过账表)';

-- =============================================
-- 报表系统相关表 (Report System Tables)
-- =============================================

-- pbsrpt_reports 表 (报表定义表)
CREATE TABLE pbsrpt_reports
(rpt_id CHAR(8) NOT NULL,
rpt_datawindow VARCHAR2(40) NULL,
rpt_library VARCHAR2(80) NULL,
rpt_title VARCHAR2(100) NULL,
rpt_purpose VARCHAR2(255) NULL,
rpt_descr VARCHAR2(255) NULL,
rpt_header CHAR(1) NULL,
rpt_active CHAR(1) NULL,
rpt_type NUMBER(10) NULL,
rpt_where VARCHAR2(255) NULL,
rpt_filter VARCHAR2(255) NULL,
rpt_sort VARCHAR2(255) NULL,
enable_filter CHAR(1) NULL,
enable_sort CHAR(1) NULL,
autoretrieve CHAR(1) NULL,
category_id NUMBER(10) NULL,
show_criteria CHAR(1) NULL,
query_mode CHAR(1) NULL,
shared_rpt_id CHAR(8) NULL
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON pbsrpt_reports TO nsql;

PROMPT '>>> 已创建表 pbsrpt_reports (报表定义表)';

-- pbsrpt_category 表 (报表分类表)
CREATE TABLE pbsrpt_category
(category_id NUMBER(10) NOT NULL,
category VARCHAR2(40) NOT NULL
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON pbsrpt_category TO nsql;

PROMPT '>>> 已创建表 pbsrpt_category (报表分类表)';

-- pbsrpt_parms 表 (报表参数表)
CREATE TABLE pbsrpt_parms
(rpt_id CHAR(8) NOT NULL,
parm_no NUMBER(3) NOT NULL,
parm_datatype VARCHAR2(20) NULL,
parm_label VARCHAR2(30) NULL,
parm_default VARCHAR2(30) NULL,
style VARCHAR2(10) NULL,
name VARCHAR2(40) NULL,
display VARCHAR2(40) NULL,
data VARCHAR2(40) NULL,
attributes VARCHAR2(100) NULL,
visible CHAR(1) NULL
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON pbsrpt_parms TO nsql;

PROMPT '>>> 已创建表 pbsrpt_parms (报表参数表)';

-- pbsrpt_sets 表 (报表集合表)
CREATE TABLE pbsrpt_sets
(rpt_set_id NUMBER(3) NOT NULL,
name VARCHAR2(100) NOT NULL
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON pbsrpt_sets TO nsql;

PROMPT '>>> 已创建表 pbsrpt_sets (报表集合表)';

-- pbsrpt_set_reports 表 (报表集合明细表)
CREATE TABLE pbsrpt_set_reports
(rpt_set_id NUMBER(3) NOT NULL,
rpt_seq NUMBER(3) NOT NULL,
rpt_id CHAR(8) NULL
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON pbsrpt_set_reports TO nsql;

PROMPT '>>> 已创建表 pbsrpt_set_reports (报表集合明细表)';

-- LOTxBILLDATE 表 (批次计费日期表)
CREATE TABLE LOTxBILLDATE
(Lot                CHAR(10) NOT NULL,
TariffKey             CHAR(10) NOT NULL,
LotBillThruDate    DATE NOT NULL
CONSTRAINT DF_LOTxBILLDATE_LotBillThruDate DEFAULT SYSDATE,
LastActivity       DATE NOT NULL
CONSTRAINT DF_LOTxBILLDATE_LastActivity DEFAULT SYSDATE,
QtyBilledBalance   NUMBER(10) NOT NULL
CONSTRAINT DF_LOTxBILLDATE_QtyBilledBalance DEFAULT 0,
QtyBilledGrossWeight    NUMBER(12,6) NOT NULL
CONSTRAINT DF_LOTxBILLDATE_QtyBilledGrossWeight DEFAULT 0,
QtyBilledNetWeight      NUMBER(12,6) NOT NULL
CONSTRAINT DF_LOTxBILLDATE_QtyBilledNetWeight DEFAULT 0,
QtyBilledCube           NUMBER(12,6) NOT NULL
CONSTRAINT DF_LOTxBILLDATE_QtyBilledCube DEFAULT 0,
AnniversaryStartDate DATE NULL,
AddDate            DATE NOT NULL
CONSTRAINT DF_LOTxBILLDATE_AddDate DEFAULT SYSDATE,
AddWho             CHAR(18) NOT NULL
CONSTRAINT DF_LOTxBILLDATE_AddWho DEFAULT USER,
EditDate           DATE NOT NULL
CONSTRAINT DF_LOTxBILLDATE_EditDate DEFAULT SYSDATE,
EditWho            CHAR(18) NOT NULL
CONSTRAINT DF_LOTxBILLDATE_EditWho DEFAULT USER
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON LOTxBILLDATE TO nsql;

PROMPT '>>> 已创建表 LOTxBILLDATE (批次计费日期表)';

-- FxRATE 表 (汇率表)
CREATE TABLE FxRATE
(CurrencyKey        CHAR(10) NOT NULL
CONSTRAINT DF_FxRATE_CurrencyKey DEFAULT ' ',
Descrip            CHAR(30) NOT NULL
CONSTRAINT DF_FxRATE_Descrip DEFAULT ' ',
BaseCurrency       CHAR(10) NOT NULL
CONSTRAINT DF_FxRATE_BaseCurrency DEFAULT 'USD',
TargetCurrency     CHAR(10)
CONSTRAINT DF_FxRATE_TargetCurrency DEFAULT 'USD',
ConversionRate     NUMBER(8,4) NOT NULL
CONSTRAINT DF_FxRATE_ConversionRate DEFAULT 1.0,
FxDate      DATE NOT NULL
CONSTRAINT DF_FxRATE_FxDate DEFAULT SYSDATE,
AddDate            DATE NOT NULL
CONSTRAINT DF_FxRATE_AddDate DEFAULT SYSDATE,
AddWho             CHAR(18) NOT NULL
CONSTRAINT DF_FxRATE_AddWho DEFAULT USER,
EditDate           DATE NOT NULL
CONSTRAINT DF_FxRATE_EditDate DEFAULT SYSDATE,
EditWho            CHAR(18) NOT NULL
CONSTRAINT DF_FxRATE_EditWho DEFAULT USER
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON FxRATE TO nsql;

PROMPT '>>> 已创建表 FxRATE (汇率表)';

-- 提交事务
COMMIT;

PROMPT '>>> 盘点和报表表结构脚本执行完成 (21张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
