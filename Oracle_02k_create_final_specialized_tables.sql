-- =============================================
-- Oracle版本 - 创建最终专业化表结构脚本 (第十一部分)
-- 功能：创建仓库管理系统中最终专业化表结构 (Oracle版本)
-- 用途：RF设备、帮助系统、物料清单、提单管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- RF设备和日志表
-- =============================================

-- RFDB_LOG 表 (RF数据库日志表)
CREATE TABLE RFDB_LOG (
adddate DATE NOT NULL
CONSTRAINT DF_RFDB_LOG_adddate DEFAULT SYSDATE,
addwho CHAR(18) NOT NULL
CONSTRAINT DF_RFDB_LOG_addwho DEFAULT USER,
rffunction CHAR(30) NOT NULL
CONSTRAINT DF_RFDB_LOG_rffunction DEFAULT ' ',
userkey CHAR(18) NOT NULL
CONSTRAINT DF_RFDB_LOG_userkey DEFAULT ' ',
loc CHAR(10) NOT NULL
CONSTRAINT DF_RFDB_LOG_loc DEFAULT ' ',
sku CHAR(20) NOT NULL
CONSTRAINT DF_RFDB_LOG_sku DEFAULT ' ',
lot CHAR(10) NOT NULL
CONSTRAINT DF_RFDB_LOG_lot DEFAULT ' ',
id CHAR(18) NOT NULL
CONSTRAINT DF_RFDB_LOG_id DEFAULT ' ',
qty NUMBER(10) NOT NULL
CONSTRAINT DF_RFDB_LOG_qty DEFAULT 0,
fromloc CHAR(10) NOT NULL
CONSTRAINT DF_RFDB_LOG_fromloc DEFAULT ' ',
toloc CHAR(10) NOT NULL
CONSTRAINT DF_RFDB_LOG_toloc DEFAULT ' ',
fromid CHAR(18) NOT NULL
CONSTRAINT DF_RFDB_LOG_fromid DEFAULT ' ',
toid CHAR(18) NOT NULL
CONSTRAINT DF_RFDB_LOG_toid DEFAULT ' ',
trantype CHAR(2) NOT NULL
CONSTRAINT DF_RFDB_LOG_trantype DEFAULT ' ',
reference CHAR(30) NOT NULL
CONSTRAINT DF_RFDB_LOG_reference DEFAULT ' '
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON RFDB_LOG TO nsql;

PROMPT '>>> 已创建表 RFDB_LOG (RF数据库日志表)';

-- RFPUTAWAY 表 (RF上架表)
CREATE TABLE RFPUTAWAY (
StorerKey                CHAR(15) NOT NULL,
Sku                      CHAR(20) NOT NULL,
Lot                      CHAR(10) NOT NULL,
Id                       CHAR(18) NOT NULL,
FromLoc                  CHAR(10) NOT NULL,
ToLoc                    CHAR(10) NOT NULL,
Qty                      NUMBER(10) NOT NULL
CONSTRAINT DF_RFPUTAWAY_Qty DEFAULT 0,
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_RFPUTAWAY_Status DEFAULT '0',
UserKey                  CHAR(18) NOT NULL
CONSTRAINT DF_RFPUTAWAY_UserKey DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_RFPUTAWAY_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_RFPUTAWAY_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_RFPUTAWAY_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_RFPUTAWAY_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE RFPUTAWAY ADD CONSTRAINT PK_RFPUTAWAY 
PRIMARY KEY (StorerKey, Sku, Lot, Id, FromLoc, ToLoc);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON RFPUTAWAY TO nsql;

PROMPT '>>> 已创建表 RFPUTAWAY (RF上架表)';

-- =============================================
-- 帮助和文档表
-- =============================================

-- help 表 (帮助表)
CREATE TABLE help
(topic            CHAR(64) NOT NULL
CONSTRAINT DF_help_topic DEFAULT ' ',
subtopic         CHAR(64) NOT NULL
CONSTRAINT DF_help_subtopic DEFAULT ' ',
info             CLOB NULL,
AddDate          DATE NOT NULL
CONSTRAINT DF_help_AddDate DEFAULT SYSDATE,
AddWho           CHAR(18) NOT NULL
CONSTRAINT DF_help_AddWho DEFAULT USER,
EditDate         DATE NOT NULL
CONSTRAINT DF_help_EditDate DEFAULT SYSDATE,
EditWho          CHAR(18) NOT NULL
CONSTRAINT DF_help_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE help ADD CONSTRAINT PK_help PRIMARY KEY (topic, subtopic);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON help TO nsql;

PROMPT '>>> 已创建表 help (帮助表)';

-- =============================================
-- 物料清单表
-- =============================================

-- BillOfMaterial 表 (物料清单表)
CREATE TABLE BillOfMaterial
(Storerkey     CHAR(15) NOT NULL
CONSTRAINT DF_BillOfMaterial_Storerkey DEFAULT ' ',
ParentSku      CHAR(20) NOT NULL
CONSTRAINT DF_BillOfMaterial_ParentSku DEFAULT ' ',
ComponentSku   CHAR(20) NOT NULL
CONSTRAINT DF_BillOfMaterial_ComponentSku DEFAULT ' ',
QtyRequired    NUMBER(10) NOT NULL
CONSTRAINT DF_BillOfMaterial_QtyRequired DEFAULT 0,
UOM            CHAR(10) NOT NULL
CONSTRAINT DF_BillOfMaterial_UOM DEFAULT ' ',
EffectiveDate  DATE NOT NULL
CONSTRAINT DF_BillOfMaterial_EffectiveDate DEFAULT SYSDATE,
ExpirationDate DATE NOT NULL
CONSTRAINT DF_BillOfMaterial_ExpirationDate DEFAULT TO_DATE('2100-01-01', 'YYYY-MM-DD'),
AddDate        DATE NOT NULL
CONSTRAINT DF_BillOfMaterial_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_BillOfMaterial_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_BillOfMaterial_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_BillOfMaterial_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE BillOfMaterial ADD CONSTRAINT PK_BillOfMaterial 
PRIMARY KEY (Storerkey, ParentSku, ComponentSku);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BillOfMaterial TO nsql;

PROMPT '>>> 已创建表 BillOfMaterial (物料清单表)';

-- =============================================
-- 投放区域管理表
-- =============================================

-- Dropid 表 (投放ID表)
CREATE TABLE Dropid
(Dropid        CHAR(18) NOT NULL
CONSTRAINT DF_Dropid_Dropid DEFAULT ' ',
Descr          CHAR(60) NOT NULL
CONSTRAINT DF_Dropid_Descr DEFAULT ' ',
DropZone       CHAR(10) NOT NULL
CONSTRAINT DF_Dropid_DropZone DEFAULT ' ',
Status         CHAR(10) NOT NULL
CONSTRAINT DF_Dropid_Status DEFAULT '0',
AddDate        DATE NOT NULL
CONSTRAINT DF_Dropid_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_Dropid_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_Dropid_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_Dropid_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE Dropid ADD CONSTRAINT PK_Dropid PRIMARY KEY (Dropid);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Dropid TO nsql;

PROMPT '>>> 已创建表 Dropid (投放ID表)';

-- DropidDetail 表 (投放ID明细表)
CREATE TABLE DropidDetail
(Dropid        CHAR(18) NOT NULL,
DropidLineNumber CHAR(5) NOT NULL,
CaseId         CHAR(10) NOT NULL
CONSTRAINT DF_DropidDetail_CaseId DEFAULT ' ',
OrderKey       CHAR(10) NOT NULL
CONSTRAINT DF_DropidDetail_OrderKey DEFAULT ' ',
AddDate        DATE NOT NULL
CONSTRAINT DF_DropidDetail_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_DropidDetail_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_DropidDetail_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_DropidDetail_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE DropidDetail ADD CONSTRAINT PK_DropidDetail PRIMARY KEY (Dropid, DropidLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON DropidDetail TO nsql;

PROMPT '>>> 已创建表 DropidDetail (投放ID明细表)';

-- =============================================
-- 任务管理扩展表
-- =============================================

-- TaskManagerSkipTasks 表 (任务管理跳过任务表)
CREATE TABLE TaskManagerSkipTasks
(
UserKey        CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_UserKey DEFAULT ' ',
TaskType       CHAR(10) NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_TaskType DEFAULT ' ',
SkipReason     CHAR(60) NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_SkipReason DEFAULT ' ',
AddDate        DATE NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE TaskManagerSkipTasks ADD CONSTRAINT PK_TaskManagerSkipTasks 
PRIMARY KEY (UserKey, TaskType);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaskManagerSkipTasks TO nsql;

PROMPT '>>> 已创建表 TaskManagerSkipTasks (任务管理跳过任务表)';

-- =============================================
-- 提单管理表
-- =============================================

-- BOL 表 (提单表)
CREATE TABLE BOL
(BolKey                        CHAR(10) NOT NULL,
Status                         CHAR(10)
CONSTRAINT DF_BOL_Status DEFAULT '0',
ExternBolKey                   CHAR(30) NOT NULL
CONSTRAINT DF_BOL_ExternBolKey DEFAULT ' ',
BolDate                        DATE NOT NULL
CONSTRAINT DF_BOL_BolDate DEFAULT SYSDATE,
CarrierKey                     CHAR(10) NOT NULL
CONSTRAINT DF_BOL_CarrierKey DEFAULT ' ',
EffectiveDate                  DATE NOT NULL
CONSTRAINT DF_BOL_EffectiveDate DEFAULT SYSDATE,
AddDate                        DATE NOT NULL
CONSTRAINT DF_BOL_AddDate DEFAULT SYSDATE,
AddWho                         CHAR(18) NOT NULL
CONSTRAINT DF_BOL_AddWho DEFAULT USER,
EditDate                       DATE NOT NULL
CONSTRAINT DF_BOL_EditDate DEFAULT SYSDATE,
EditWho                        CHAR(18) NOT NULL
CONSTRAINT DF_BOL_EditWho DEFAULT USER,
TrafficCop                     CHAR(1) NULL,
ArchiveCop                     CHAR(1) NULL,
TimeStamp                      CHAR(18) NULL
);

-- 添加主键
ALTER TABLE BOL ADD CONSTRAINT PK_BOL PRIMARY KEY (BolKey);

-- 添加检查约束
ALTER TABLE BOL ADD CONSTRAINT CK_BOL_Status CHECK (Status IN ('0', '9'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BOL TO nsql;

PROMPT '>>> 已创建表 BOL (提单表)';

-- BOLDETAIL 表 (提单明细表)
CREATE TABLE BOLDETAIL
(BolKey         CHAR(10) NOT NULL,
BolLineNumber  CHAR(5) NOT NULL,
OrderKey       CHAR(10) NOT NULL
CONSTRAINT DF_BOLDETAIL_OrderKey DEFAULT ' ',
PalletKey      CHAR(10) NOT NULL
CONSTRAINT DF_BOLDETAIL_PalletKey DEFAULT ' ',
Description    CHAR(30) NOT NULL
CONSTRAINT DF_BOLDETAIL_Description DEFAULT ' ',
AddDate        DATE NOT NULL
CONSTRAINT DF_BOLDETAIL_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_BOLDETAIL_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_BOLDETAIL_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_BOLDETAIL_EditWho DEFAULT USER,
TrafficCop     CHAR(1) NULL,
ArchiveCop     CHAR(1) NULL,
TimeStamp      CHAR(18) NULL
);

-- 添加主键
ALTER TABLE BOLDETAIL ADD CONSTRAINT PK_BOLDETAIL PRIMARY KEY (BolKey, BolLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BOLDETAIL TO nsql;

PROMPT '>>> 已创建表 BOLDETAIL (提单明细表)';

-- =============================================
-- 批次ID明细表
-- =============================================

-- LotxIdDetail 表 (批次ID明细表)
CREATE TABLE LotxIdDetail (
LotxIdDetailKey     CHAR(10) NOT NULL,
StorerKey           CHAR(15) NOT NULL
CONSTRAINT DF_LotxIdDetail_StorerKey DEFAULT ' ',
Sku                 CHAR(20) NOT NULL
CONSTRAINT DF_LotxIdDetail_Sku DEFAULT ' ',
Lot                 CHAR(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_Lot DEFAULT ' ',
Id                  CHAR(18) NOT NULL
CONSTRAINT DF_LotxIdDetail_Id DEFAULT ' ',
Loc                 CHAR(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_Loc DEFAULT ' ',
Qty                 NUMBER(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_Qty DEFAULT 0,
QtyAllocated        NUMBER(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_QtyAllocated DEFAULT 0,
QtyPicked           NUMBER(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_QtyPicked DEFAULT 0,
PackKey             CHAR(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_PackKey DEFAULT 'STD',
UOM                 CHAR(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_UOM DEFAULT ' ',
RotationRule        CHAR(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_RotationRule DEFAULT ' ',
MaxQty              NUMBER(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_MaxQty DEFAULT 0,
MaxWeight           NUMBER(12,6) NOT NULL
CONSTRAINT DF_LotxIdDetail_MaxWeight DEFAULT 0,
MaxCube             NUMBER(12,6) NOT NULL
CONSTRAINT DF_LotxIdDetail_MaxCube DEFAULT 0,
EffectiveDate       DATE NOT NULL
CONSTRAINT DF_LotxIdDetail_EffectiveDate DEFAULT SYSDATE,
AddDate             DATE NOT NULL
CONSTRAINT DF_LotxIdDetail_AddDate DEFAULT SYSDATE,
AddWho              CHAR(18) NOT NULL
CONSTRAINT DF_LotxIdDetail_AddWho DEFAULT USER,
EditDate            DATE NOT NULL
CONSTRAINT DF_LotxIdDetail_EditDate DEFAULT SYSDATE,
EditWho             CHAR(18) NOT NULL
CONSTRAINT DF_LotxIdDetail_EditWho DEFAULT USER,
TrafficCop          CHAR(1) NULL,
ArchiveCop          CHAR(1) NULL
);

-- 添加主键
ALTER TABLE LotxIdDetail ADD CONSTRAINT PK_LotxIdDetail PRIMARY KEY (LotxIdDetailKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON LotxIdDetail TO nsql;

PROMPT '>>> 已创建表 LotxIdDetail (批次ID明细表)';

-- =============================================
-- 补货锁定表
-- =============================================

-- REPLENISHMENT_LOCK 表 (补货锁定表)
CREATE TABLE REPLENISHMENT_LOCK
(
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_Sku DEFAULT ' ',
FromLoc                  CHAR(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_FromLoc DEFAULT ' ',
ToLoc                    CHAR(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_ToLoc DEFAULT ' ',
LockType                 CHAR(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_LockType DEFAULT ' ',
LockReason               CHAR(60) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_LockReason DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE REPLENISHMENT_LOCK ADD CONSTRAINT PK_REPLENISHMENT_LOCK
PRIMARY KEY (StorerKey, Sku, FromLoc, ToLoc);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON REPLENISHMENT_LOCK TO nsql;

PROMPT '>>> 已创建表 REPLENISHMENT_LOCK (补货锁定表)';

-- 提交事务
COMMIT;

PROMPT '>>> 最终专业化表结构脚本执行完成 (12张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
