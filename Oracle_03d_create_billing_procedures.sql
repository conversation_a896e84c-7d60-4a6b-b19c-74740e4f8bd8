-- =============================================
-- Oracle版本 - 创建计费管理存储过程脚本
-- 功能：创建仓库管理系统的计费管理相关存储过程 (Oracle版本)
-- 用途：计费计算、发票生成、最低费用、税费计算等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中提取的计费管理存储过程转换为Oracle语法
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 计费计算存储过程
-- =============================================

-- nspCalculateRSCharges 存储过程 (计算RS费用)
CREATE OR REPLACE PROCEDURE nspCalculateRSCharges (
    p_billinggroupmin   IN CHAR,
    p_billinggroupmax   IN CHAR,
    p_chargetypes       IN CHAR,
    p_cutoffdate        IN DATE,
    p_invoicebatchkey   IN CHAR,
    p_newcharges        OUT NUMBER,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_chargeamount NUMBER;
    v_cnt NUMBER;
    
    -- 游标定义
    CURSOR billing_cursor IS
        SELECT sb.StorerKey, sb.TariffKey, td.ChargeType, td.Rate, td.UOM
        FROM STORERBILLING sb
        JOIN TariffDetail td ON sb.TariffKey = td.TariffKey
        WHERE sb.BillingGroup BETWEEN p_billinggroupmin AND p_billinggroupmax
        AND INSTR(p_chargetypes, td.ChargeType) > 0
        AND td.EffectiveDate <= p_cutoffdate
        AND td.ExpirationDate >= p_cutoffdate;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_newcharges := 0;
    
    -- 处理每个计费项目
    FOR billing_rec IN billing_cursor LOOP
        v_chargeamount := 0;
        
        -- 根据费用类型计算费用
        CASE billing_rec.ChargeType
            WHEN 'RS' THEN -- 收货服务费
                SELECT COUNT(*) * billing_rec.Rate INTO v_chargeamount
                FROM RECEIPTDETAIL rd
                JOIN RECEIPT r ON rd.ReceiptKey = r.ReceiptKey
                WHERE rd.StorerKey = billing_rec.StorerKey
                AND r.ReceiptDate <= p_cutoffdate
                AND r.Status = '9'; -- 已完成
                
            WHEN 'SS' THEN -- 发货服务费
                SELECT COUNT(*) * billing_rec.Rate INTO v_chargeamount
                FROM ORDERDETAIL od
                JOIN ORDERS o ON od.OrderKey = o.OrderKey
                WHERE od.StorerKey = billing_rec.StorerKey
                AND o.OrderDate <= p_cutoffdate
                AND o.Status = '9'; -- 已发货
                
            WHEN 'IS' THEN -- 库存存储费
                SELECT SUM(lli.Qty) * billing_rec.Rate INTO v_chargeamount
                FROM LOTxLOCxID lli
                WHERE lli.StorerKey = billing_rec.StorerKey
                AND lli.Qty > 0;
                
            WHEN 'HI' THEN -- 入库处理费
                SELECT SUM(rd.Qty) * billing_rec.Rate INTO v_chargeamount
                FROM RECEIPTDETAIL rd
                JOIN RECEIPT r ON rd.ReceiptKey = r.ReceiptKey
                WHERE rd.StorerKey = billing_rec.StorerKey
                AND r.ReceiptDate <= p_cutoffdate
                AND r.Status = '9';
                
            WHEN 'HO' THEN -- 出库处理费
                SELECT SUM(od.ShippedQty) * billing_rec.Rate INTO v_chargeamount
                FROM ORDERDETAIL od
                JOIN ORDERS o ON od.OrderKey = o.OrderKey
                WHERE od.StorerKey = billing_rec.StorerKey
                AND o.OrderDate <= p_cutoffdate
                AND o.Status = '9';
                
            ELSE
                v_chargeamount := 0;
        END CASE;
        
        -- 如果有费用，插入累计费用表
        IF v_chargeamount > 0 THEN
            INSERT INTO ACCUMULATEDCHARGES (
                AccumulatedChargesKey, Descrip, Status, StorerKey, TariffKey,
                ChargeType, ChargeAmount, ChargeDate, BillingPeriod, PrintCount,
                AddDate, AddWho, EditDate, EditWho
            ) VALUES (
                'AC' || LPAD(TO_CHAR(SEQ_ACCUMULATEDCHARGES.NEXTVAL), 8, '0'),
                billing_rec.ChargeType || ' Charges for ' || TO_CHAR(p_cutoffdate, 'YYYY-MM'),
                '0',
                billing_rec.StorerKey,
                billing_rec.TariffKey,
                billing_rec.ChargeType,
                v_chargeamount,
                v_currentdatetime,
                TO_CHAR(p_cutoffdate, 'YYYY-MM'),
                0,
                v_currentdatetime,
                v_currentuser,
                v_currentdatetime,
                v_currentuser
            );
            
            p_newcharges := p_newcharges + 1;
        END IF;
    END LOOP;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspCalculateRSCharges');
        ROLLBACK;
END nspCalculateRSCharges;
/

-- nspCalculateRSChargesWrapper 存储过程 (计算RS费用包装器)
CREATE OR REPLACE PROCEDURE nspCalculateRSChargesWrapper (
    p_storerkeymin      IN CHAR,
    p_storerkeymax      IN CHAR,
    p_chargetypes       IN CHAR,
    p_cutoffdate        IN DATE
) IS
    v_success NUMBER;
    v_err NUMBER;
    v_errmsg CHAR(250);
    v_newcharges NUMBER;
    v_invoicebatchkey CHAR(10);
BEGIN
    -- 获取发票批次键
    nspg_getkey('InvoiceBatchKey', 10, v_invoicebatchkey, v_success, v_err, v_errmsg);
    
    IF v_success = 1 THEN
        -- 调用主计费过程
        nspCalculateRSCharges(
            p_storerkeymin, p_storerkeymax, p_chargetypes, p_cutoffdate, v_invoicebatchkey,
            v_newcharges, v_success, v_err, v_errmsg
        );
        
        -- 输出结果
        DBMS_OUTPUT.PUT_LINE('RS Charges Calculation Results:');
        DBMS_OUTPUT.PUT_LINE('Success: ' || v_success);
        DBMS_OUTPUT.PUT_LINE('New Charges: ' || v_newcharges);
        DBMS_OUTPUT.PUT_LINE('Invoice Batch Key: ' || v_invoicebatchkey);
        
        IF v_success = 0 THEN
            DBMS_OUTPUT.PUT_LINE('Error: ' || v_err);
            DBMS_OUTPUT.PUT_LINE('Message: ' || v_errmsg);
        END IF;
    ELSE
        DBMS_OUTPUT.PUT_LINE('Failed to get invoice batch key');
        DBMS_OUTPUT.PUT_LINE('Error: ' || v_err);
        DBMS_OUTPUT.PUT_LINE('Message: ' || v_errmsg);
    END IF;
    
END nspCalculateRSChargesWrapper;
/

-- =============================================
-- 最低费用计算存储过程
-- =============================================

-- nspBillDocumentMinimumCharge 存储过程 (单据最低费用)
CREATE OR REPLACE PROCEDURE nspBillDocumentMinimumCharge (
    p_invoicebatchkey   IN CHAR,
    p_newcharges        OUT NUMBER,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_minimumcharge NUMBER;
    v_currentcharges NUMBER;
    v_additionalcharge NUMBER;
    
    -- 游标定义
    CURSOR document_cursor IS
        SELECT ac.StorerKey, ac.TariffKey, td.MinimumCharge,
               SUM(ac.ChargeAmount) AS TotalCharges
        FROM ACCUMULATEDCHARGES ac
        JOIN TariffDetail td ON ac.TariffKey = td.TariffKey
        WHERE ac.Status = '0'
        AND td.MinimumCharge > 0
        GROUP BY ac.StorerKey, ac.TariffKey, td.MinimumCharge
        HAVING SUM(ac.ChargeAmount) < td.MinimumCharge;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_newcharges := 0;
    
    -- 处理每个需要最低费用的单据
    FOR doc_rec IN document_cursor LOOP
        v_additionalcharge := doc_rec.MinimumCharge - doc_rec.TotalCharges;
        
        -- 插入最低费用调整
        INSERT INTO ACCUMULATEDCHARGES (
            AccumulatedChargesKey, Descrip, Status, StorerKey, TariffKey,
            ChargeType, ChargeAmount, ChargeDate, BillingPeriod, PrintCount,
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AC' || LPAD(TO_CHAR(SEQ_ACCUMULATEDCHARGES.NEXTVAL), 8, '0'),
            'Minimum Charge Adjustment',
            '0',
            doc_rec.StorerKey,
            doc_rec.TariffKey,
            'MC', -- Minimum Charge
            v_additionalcharge,
            v_currentdatetime,
            TO_CHAR(SYSDATE, 'YYYY-MM'),
            0,
            v_currentdatetime,
            v_currentuser,
            v_currentdatetime,
            v_currentuser
        );
        
        p_newcharges := p_newcharges + 1;
    END LOOP;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspBillDocumentMinimumCharge');
        ROLLBACK;
END nspBillDocumentMinimumCharge;
/

-- nspBillInvoiceMinimumCharge 存储过程 (发票最低费用)
CREATE OR REPLACE PROCEDURE nspBillInvoiceMinimumCharge (
    p_invoicebatchkey   IN CHAR,
    p_newcharges        OUT NUMBER,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_minimumcharge NUMBER := 50.00; -- 默认最低发票金额
    v_currentcharges NUMBER;
    v_additionalcharge NUMBER;
    
    -- 游标定义
    CURSOR invoice_cursor IS
        SELECT StorerKey, SUM(ChargeAmount) AS TotalCharges
        FROM ACCUMULATEDCHARGES
        WHERE Status = '0'
        GROUP BY StorerKey
        HAVING SUM(ChargeAmount) < v_minimumcharge;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_newcharges := 0;
    
    -- 处理每个需要最低费用的发票
    FOR inv_rec IN invoice_cursor LOOP
        v_additionalcharge := v_minimumcharge - inv_rec.TotalCharges;
        
        -- 插入最低发票费用调整
        INSERT INTO ACCUMULATEDCHARGES (
            AccumulatedChargesKey, Descrip, Status, StorerKey, TariffKey,
            ChargeType, ChargeAmount, ChargeDate, BillingPeriod, PrintCount,
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AC' || LPAD(TO_CHAR(SEQ_ACCUMULATEDCHARGES.NEXTVAL), 8, '0'),
            'Minimum Invoice Charge Adjustment',
            '0',
            inv_rec.StorerKey,
            'DEFAULT',
            'MI', -- Minimum Invoice
            v_additionalcharge,
            v_currentdatetime,
            TO_CHAR(SYSDATE, 'YYYY-MM'),
            0,
            v_currentdatetime,
            v_currentuser,
            v_currentdatetime,
            v_currentuser
        );
        
        p_newcharges := p_newcharges + 1;
    END LOOP;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspBillInvoiceMinimumCharge');
        ROLLBACK;
END nspBillInvoiceMinimumCharge;
/

-- nspBillLotMinimumCharge 存储过程 (批次最低费用)
CREATE OR REPLACE PROCEDURE nspBillLotMinimumCharge (
    p_invoicebatchkey   IN CHAR,
    p_newcharges        OUT NUMBER,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_minimumcharge NUMBER;
    v_currentcharges NUMBER;
    v_additionalcharge NUMBER;

    -- 游标定义
    CURSOR lot_cursor IS
        SELECT lb.Lot, lb.TariffKey, td.MinimumCharge,
               SUM(ac.ChargeAmount) AS TotalCharges
        FROM LOTxBILLDATE lb
        JOIN ACCUMULATEDCHARGES ac ON lb.Lot = ac.Lot
        JOIN TariffDetail td ON lb.TariffKey = td.TariffKey
        WHERE ac.Status = '0'
        AND td.MinimumCharge > 0
        AND td.ChargeType = 'LS' -- Lot Storage
        GROUP BY lb.Lot, lb.TariffKey, td.MinimumCharge
        HAVING SUM(ac.ChargeAmount) < td.MinimumCharge;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_newcharges := 0;

    -- 处理每个需要最低费用的批次
    FOR lot_rec IN lot_cursor LOOP
        v_additionalcharge := lot_rec.MinimumCharge - lot_rec.TotalCharges;

        -- 插入最低批次费用调整
        INSERT INTO ACCUMULATEDCHARGES (
            AccumulatedChargesKey, Descrip, Status, StorerKey, TariffKey,
            ChargeType, ChargeAmount, ChargeDate, BillingPeriod, PrintCount,
            Lot, AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AC' || LPAD(TO_CHAR(SEQ_ACCUMULATEDCHARGES.NEXTVAL), 8, '0'),
            'Minimum Lot Charge Adjustment',
            '0',
            'DEFAULT',
            lot_rec.TariffKey,
            'ML', -- Minimum Lot
            v_additionalcharge,
            v_currentdatetime,
            TO_CHAR(SYSDATE, 'YYYY-MM'),
            0,
            lot_rec.Lot,
            v_currentdatetime,
            v_currentuser,
            v_currentdatetime,
            v_currentuser
        );

        p_newcharges := p_newcharges + 1;
    END LOOP;

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspBillLotMinimumCharge');
        ROLLBACK;
END nspBillLotMinimumCharge;
/

-- nspBillRetrievePendingCharge 存储过程 (检索待处理费用)
CREATE OR REPLACE PROCEDURE nspBillRetrievePendingCharge (
    p_invoicebatchkey   IN CHAR,
    p_billinggroupmin   IN CHAR,
    p_billinggroupmax   IN CHAR,
    p_chargetypes       IN CHAR,
    p_cutoffdate        IN DATE,
    p_chargesretrieved  OUT NUMBER,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_chargesretrieved := 0;

    -- 将待处理费用移动到累计费用表
    INSERT INTO ACCUMULATEDCHARGES (
        AccumulatedChargesKey, Descrip, Status, StorerKey, TariffKey,
        ChargeType, ChargeAmount, ChargeDate, BillingPeriod, PrintCount,
        AddDate, AddWho, EditDate, EditWho
    )
    SELECT
        'AC' || LPAD(TO_CHAR(SEQ_ACCUMULATEDCHARGES.NEXTVAL), 8, '0'),
        'Pending Charge Retrieved',
        '0',
        pc.StorerKey,
        pc.TariffKey,
        pc.ChargeType,
        pc.ChargeAmount,
        v_currentdatetime,
        TO_CHAR(p_cutoffdate, 'YYYY-MM'),
        0,
        v_currentdatetime,
        v_currentuser,
        v_currentdatetime,
        v_currentuser
    FROM PENDINGCHARGES pc
    JOIN STORERBILLING sb ON pc.StorerKey = sb.StorerKey
    WHERE sb.BillingGroup BETWEEN p_billinggroupmin AND p_billinggroupmax
    AND INSTR(p_chargetypes, pc.ChargeType) > 0
    AND pc.ChargeDate <= p_cutoffdate
    AND pc.Status = '0'; -- 待处理

    p_chargesretrieved := SQL%ROWCOUNT;

    -- 更新待处理费用状态
    UPDATE PENDINGCHARGES pc
    SET Status = '1', -- 已处理
        ProcessDate = v_currentdatetime,
        ProcessWho = v_currentuser
    WHERE EXISTS (
        SELECT 1 FROM STORERBILLING sb
        WHERE sb.StorerKey = pc.StorerKey
        AND sb.BillingGroup BETWEEN p_billinggroupmin AND p_billinggroupmax
    )
    AND INSTR(p_chargetypes, pc.ChargeType) > 0
    AND pc.ChargeDate <= p_cutoffdate
    AND pc.Status = '0';

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspBillRetrievePendingCharge');
        ROLLBACK;
END nspBillRetrievePendingCharge;
/

-- nspBillSetInvoiceNumbers 存储过程 (设置发票号码)
CREATE OR REPLACE PROCEDURE nspBillSetInvoiceNumbers (
    p_invoicebatchkey   IN CHAR,
    p_totinvoices       OUT NUMBER,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_invoicenumber CHAR(10);
    v_cnt NUMBER := 0;

    -- 游标定义
    CURSOR invoice_cursor IS
        SELECT DISTINCT StorerKey
        FROM ACCUMULATEDCHARGES
        WHERE Status = '0'
        ORDER BY StorerKey;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_totinvoices := 0;

    -- 为每个存储商生成发票号码
    FOR inv_rec IN invoice_cursor LOOP
        -- 获取发票号码
        nspg_getkey('InvoiceNumber', 10, v_invoicenumber, p_success, p_err, p_errmsg);

        IF p_success = 1 THEN
            -- 更新累计费用表的发票号码
            UPDATE ACCUMULATEDCHARGES
            SET InvoiceNumber = v_invoicenumber,
                InvoiceBatchKey = p_invoicebatchkey,
                EditDate = v_currentdatetime,
                EditWho = v_currentuser
            WHERE StorerKey = inv_rec.StorerKey
            AND Status = '0';

            p_totinvoices := p_totinvoices + 1;
        ELSE
            EXIT;
        END IF;
    END LOOP;

    IF p_success = 1 THEN
        COMMIT;
    ELSE
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspBillSetInvoiceNumbers');
        ROLLBACK;
END nspBillSetInvoiceNumbers;
/

-- nspBillTaxCharge 存储过程 (计算税费)
CREATE OR REPLACE PROCEDURE nspBillTaxCharge (
    p_invoicebatchkey   IN CHAR,
    p_newcharges        OUT NUMBER,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_taxamount NUMBER;
    v_taxrate NUMBER;

    -- 游标定义
    CURSOR tax_cursor IS
        SELECT ac.StorerKey, ac.TariffKey, SUM(ac.ChargeAmount) AS TaxableAmount,
               tg.TaxGroupKey, tr.Rate AS TaxRate
        FROM ACCUMULATEDCHARGES ac
        JOIN Tariff t ON ac.TariffKey = t.TariffKey
        JOIN TaxGroup tg ON t.TaxGroupKey = tg.TaxGroupKey
        JOIN TaxGroupDetail tgd ON tg.TaxGroupKey = tgd.TaxGroupKey
        JOIN TaxRate tr ON tgd.TaxRateKey = tr.TaxRateKey
        WHERE ac.Status = '0'
        AND ac.ChargeType != 'TX' -- 排除已有税费
        GROUP BY ac.StorerKey, ac.TariffKey, tg.TaxGroupKey, tr.Rate;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_newcharges := 0;

    -- 计算每个税组的税费
    FOR tax_rec IN tax_cursor LOOP
        v_taxamount := tax_rec.TaxableAmount * tax_rec.TaxRate;

        -- 插入税费记录
        IF v_taxamount > 0 THEN
            INSERT INTO ACCUMULATEDCHARGES (
                AccumulatedChargesKey, Descrip, Status, StorerKey, TariffKey,
                ChargeType, ChargeAmount, ChargeDate, BillingPeriod, PrintCount,
                AddDate, AddWho, EditDate, EditWho
            ) VALUES (
                'AC' || LPAD(TO_CHAR(SEQ_ACCUMULATEDCHARGES.NEXTVAL), 8, '0'),
                'Tax Charge (' || ROUND(tax_rec.TaxRate * 100, 2) || '%)',
                '0',
                tax_rec.StorerKey,
                tax_rec.TariffKey,
                'TX', -- Tax
                v_taxamount,
                v_currentdatetime,
                TO_CHAR(SYSDATE, 'YYYY-MM'),
                0,
                v_currentdatetime,
                v_currentuser,
                v_currentdatetime,
                v_currentuser
            );

            p_newcharges := p_newcharges + 1;
        END IF;
    END LOOP;

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspBillTaxCharge');
        ROLLBACK;
END nspBillTaxCharge;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle计费管理存储过程脚本执行完成';
PROMPT '>>> 已创建完整的计费管理存储过程集合 (8个)';
PROMPT '>>> 包含：费用计算、最低费用、税费计算、发票管理等功能';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 计费管理存储过程创建完成！';
PROMPT '总计费存储过程数量：8个专业计费存储过程';
PROMPT '功能覆盖：';
PROMPT '- RS费用计算 (收货/发货/存储/处理费)';
PROMPT '- 最低费用管理 (单据/发票/批次最低费用)';
PROMPT '- 待处理费用检索';
PROMPT '- 发票号码生成';
PROMPT '- 税费计算';
PROMPT '=============================================';
