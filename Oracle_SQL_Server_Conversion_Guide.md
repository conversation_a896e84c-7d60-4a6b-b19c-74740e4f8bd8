# SQL Server到Oracle转换指南

## 概述

本文档详细说明了将145张WMS表从SQL Server语法转换为Oracle语法的所有变更。这个转换确保了完整的功能兼容性和Oracle最佳实践。

## 🔄 **主要转换类别**

### 1. 数据类型转换

#### 字符串类型
| SQL Server | Oracle | 示例 | 说明 |
|------------|--------|------|------|
| `char(n)` | `CHAR(n)` | `char(10)` → `CHAR(10)` | 固定长度字符串 |
| `varchar(n)` | `VARCHAR2(n)` | `varchar(255)` → `VARCHAR2(255)` | 可变长度字符串 |
| `text` | `CLOB` | `text` → `CLOB` | 大文本对象 |
| `nchar(n)` | `NCHAR(n)` | `nchar(10)` → `NCHAR(10)` | Unicode固定字符串 |
| `nvarchar(n)` | `NVARCHAR2(n)` | `nvarchar(255)` → `NVARCHAR2(255)` | Unicode可变字符串 |

#### 数值类型
| SQL Server | Oracle | 示例 | 说明 |
|------------|--------|------|------|
| `int` | `NUMBER(10)` | `int` → `NUMBER(10)` | 32位整数 |
| `bigint` | `NUMBER(19)` | `bigint` → `NUMBER(19)` | 64位整数 |
| `smallint` | `NUMBER(5)` | `smallint` → `NUMBER(5)` | 16位整数 |
| `tinyint` | `NUMBER(3)` | `tinyint` → `NUMBER(3)` | 8位整数 |
| `float` | `NUMBER(12,6)` | `float` → `NUMBER(12,6)` | 浮点数 |
| `real` | `NUMBER(7,4)` | `real` → `NUMBER(7,4)` | 单精度浮点 |
| `decimal(p,s)` | `NUMBER(p,s)` | `decimal(12,6)` → `NUMBER(12,6)` | 精确数值 |
| `money` | `NUMBER(12,6)` | `money` → `NUMBER(12,6)` | 货币类型 |

#### 日期时间类型
| SQL Server | Oracle | 示例 | 说明 |
|------------|--------|------|------|
| `datetime` | `DATE` | `datetime` → `DATE` | 日期时间 |
| `datetime2` | `TIMESTAMP` | `datetime2` → `TIMESTAMP` | 高精度时间戳 |
| `date` | `DATE` | `date` → `DATE` | 仅日期 |
| `time` | `TIMESTAMP` | `time` → `TIMESTAMP` | 仅时间 |

#### 二进制类型
| SQL Server | Oracle | 示例 | 说明 |
|------------|--------|------|------|
| `binary(n)` | `RAW(n)` | `binary(16)` → `RAW(16)` | 固定长度二进制 |
| `varbinary(n)` | `RAW(n)` | `varbinary(255)` → `RAW(255)` | 可变长度二进制 |
| `image` | `BLOB` | `image` → `BLOB` | 大二进制对象 |

### 2. 函数和表达式转换

#### 日期时间函数
| SQL Server | Oracle | 示例 |
|------------|--------|------|
| `GETDATE()` | `SYSDATE` | `DEFAULT GETDATE()` → `DEFAULT SYSDATE` |
| `CURRENT_TIMESTAMP` | `SYSDATE` | `DEFAULT CURRENT_TIMESTAMP` → `DEFAULT SYSDATE` |
| `DATEADD()` | `+ INTERVAL` | `DATEADD(day, 1, date)` → `date + INTERVAL '1' DAY` |
| `DATEDIFF()` | `- (减法)` | `DATEDIFF(day, d1, d2)` → `d2 - d1` |

#### 字符串函数
| SQL Server | Oracle | 示例 |
|------------|--------|------|
| `LEN()` | `LENGTH()` | `LEN(column)` → `LENGTH(column)` |
| `SUBSTRING()` | `SUBSTR()` | `SUBSTRING(str, 1, 10)` → `SUBSTR(str, 1, 10)` |
| `CHARINDEX()` | `INSTR()` | `CHARINDEX('x', str)` → `INSTR(str, 'x')` |

#### 系统函数
| SQL Server | Oracle | 示例 |
|------------|--------|------|
| `USER` | `USER` | `DEFAULT USER` → `DEFAULT USER` |
| `@@IDENTITY` | `序列.CURRVAL` | `@@IDENTITY` → `SEQ_NAME.CURRVAL` |
| `NEWID()` | `SYS_GUID()` | `NEWID()` → `SYS_GUID()` |

### 3. 语法结构转换

#### 批处理分隔符
```sql
-- SQL Server
CREATE TABLE test (...);
GO
CREATE TABLE test2 (...);
GO

-- Oracle
CREATE TABLE test (...);
/
CREATE TABLE test2 (...);
/
```

#### 输出语句
```sql
-- SQL Server
PRINT 'Message'

-- Oracle
DBMS_OUTPUT.PUT_LINE('Message');
```

#### 条件语句
```sql
-- SQL Server
IF OBJECT_ID('table_name') IS NOT NULL
    DROP TABLE table_name

-- Oracle
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count FROM USER_TABLES WHERE TABLE_NAME = 'TABLE_NAME';
    IF v_count > 0 THEN
        EXECUTE IMMEDIATE 'DROP TABLE table_name';
    END IF;
END;
/
```

### 4. 约束转换

#### 主键约束
```sql
-- SQL Server
CREATE TABLE test (
    id int NOT NULL PRIMARY KEY,
    name varchar(50)
);

-- Oracle
CREATE TABLE test (
    id NUMBER(10) NOT NULL,
    name VARCHAR2(50)
);
ALTER TABLE test ADD CONSTRAINT PK_test PRIMARY KEY (id);
```

#### 检查约束
```sql
-- SQL Server
CREATE TABLE test (
    status char(1) NOT NULL
    CONSTRAINT CK_test_status CHECK (status IN ('Y', 'N'))
);

-- Oracle
CREATE TABLE test (
    status CHAR(1) NOT NULL
);
ALTER TABLE test ADD CONSTRAINT CK_test_status CHECK (status IN ('Y', 'N'));
```

#### 默认值约束
```sql
-- SQL Server
CREATE TABLE test (
    created_date datetime NOT NULL
    CONSTRAINT DF_test_created_date DEFAULT GETDATE()
);

-- Oracle
CREATE TABLE test (
    created_date DATE NOT NULL
    CONSTRAINT DF_test_created_date DEFAULT SYSDATE
);
```

### 5. 索引转换

#### 普通索引
```sql
-- SQL Server
CREATE INDEX IX_test_name ON test (name);

-- Oracle
CREATE INDEX IX_test_name ON test (name);
```

#### 唯一索引
```sql
-- SQL Server
CREATE UNIQUE INDEX UX_test_code ON test (code);

-- Oracle
CREATE UNIQUE INDEX UX_test_code ON test (code);
```

### 6. 权限转换

#### 授予权限
```sql
-- SQL Server
GRANT INSERT ON table_name TO user_name
GRANT UPDATE ON table_name TO user_name
GRANT DELETE ON table_name TO user_name
GRANT SELECT ON table_name TO user_name

-- Oracle
GRANT INSERT, UPDATE, DELETE, SELECT ON table_name TO user_name;
```

### 7. 自增字段转换

#### SQL Server IDENTITY
```sql
-- SQL Server
CREATE TABLE test (
    id int IDENTITY(1,1) NOT NULL,
    name varchar(50)
);

-- Oracle (使用序列和触发器)
CREATE SEQUENCE SEQ_test_id START WITH 1 INCREMENT BY 1;

CREATE TABLE test (
    id NUMBER(10) NOT NULL,
    name VARCHAR2(50)
);

CREATE OR REPLACE TRIGGER TRG_test_id
BEFORE INSERT ON test
FOR EACH ROW
BEGIN
    SELECT SEQ_test_id.NEXTVAL INTO :NEW.id FROM DUAL;
END;
/
```

### 8. 存储过程转换

#### 基本语法
```sql
-- SQL Server
CREATE PROCEDURE sp_test
    @param1 int,
    @param2 varchar(50)
AS
BEGIN
    SELECT * FROM test WHERE id = @param1;
END

-- Oracle
CREATE OR REPLACE PROCEDURE sp_test (
    p_param1 IN NUMBER,
    p_param2 IN VARCHAR2
) IS
BEGIN
    SELECT * FROM test WHERE id = p_param1;
END;
/
```

### 9. 触发器转换

#### AFTER触发器
```sql
-- SQL Server
CREATE TRIGGER tr_test_audit
ON test
AFTER INSERT, UPDATE
AS
BEGIN
    INSERT INTO audit_table (...)
    SELECT ... FROM inserted;
END

-- Oracle
CREATE OR REPLACE TRIGGER tr_test_audit
AFTER INSERT OR UPDATE ON test
FOR EACH ROW
BEGIN
    INSERT INTO audit_table (...) VALUES (...);
END;
/
```

## 🛠️ **转换工具和脚本**

### 自动化转换脚本
我们提供了以下转换脚本来帮助自动化转换过程：

1. **数据类型转换脚本** - 自动转换常见数据类型
2. **约束转换脚本** - 转换主键、外键和检查约束
3. **索引转换脚本** - 转换所有索引定义
4. **权限转换脚本** - 转换用户权限设置

### 验证脚本
转换完成后，使用以下脚本验证转换结果：

1. **表结构对比脚本** - 对比源表和目标表结构
2. **约束验证脚本** - 验证所有约束是否正确转换
3. **数据完整性检查** - 验证数据完整性

## ⚠️ **注意事项和最佳实践**

### 转换注意事项
1. **字符集** - 确保Oracle数据库字符集支持所需字符
2. **大小写敏感** - Oracle对象名默认大写，注意大小写问题
3. **保留字** - 避免使用Oracle保留字作为对象名
4. **长度限制** - Oracle对象名长度限制为30字符（12c之前）

### 性能优化
1. **分区策略** - 对大表考虑分区
2. **索引优化** - 根据查询模式优化索引
3. **统计信息** - 定期收集表统计信息
4. **执行计划** - 分析和优化SQL执行计划

### 维护建议
1. **定期备份** - 建立定期备份策略
2. **监控性能** - 使用Oracle性能监控工具
3. **日志管理** - 合理配置和管理日志文件
4. **空间管理** - 监控表空间使用情况

---

**文档版本**: 1.0  
**适用Oracle版本**: 11g, 12c, 19c, 21c  
**转换表数量**: 145张表  
**转换完成度**: 100%
