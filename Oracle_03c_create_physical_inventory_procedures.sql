-- =============================================
-- Oracle版本 - 创建物理盘点存储过程脚本
-- 功能：创建仓库管理系统的物理盘点相关存储过程 (Oracle版本)
-- 用途：盘点比较、标签管理、盘点过账等专业盘点功能
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中提取的物理盘点存储过程转换为Oracle语法
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 盘点比较存储过程
-- =============================================

-- nspCompare_a2b_id 存储过程 (A队B队ID比较)
CREATE OR REPLACE PROCEDURE nspCompare_a2b_id (
    p_resultmode    IN CHAR
) IS
    v_debug NUMBER := 0;
    v_cnt NUMBER;
BEGIN
    -- 清空比较表
    DELETE FROM PHY_A2B_ID;
    
    -- 插入A队数据
    INSERT INTO PHY_A2B_ID (StorerKey, Sku, Loc, Id, QtyTeamA, QtyTeamB)
    SELECT 
        StorerKey, Sku, Loc, Id, 
        SUM(QtyActual) AS QtyTeamA,
        0 AS QtyTeamB
    FROM PHYSICALDETAIL
    WHERE Team = 'A'
    GROUP BY StorerKey, Sku, Loc, Id;
    
    -- 更新B队数据
    FOR b_rec IN (
        SELECT StorerKey, Sku, Loc, Id, SUM(QtyActual) AS QtyTeamB
        FROM PHYSICALDETAIL
        WHERE Team = 'B'
        GROUP BY StorerKey, Sku, Loc, Id
    ) LOOP
        UPDATE PHY_A2B_ID
        SET QtyTeamB = b_rec.QtyTeamB
        WHERE StorerKey = b_rec.StorerKey 
        AND Sku = b_rec.Sku 
        AND Loc = b_rec.Loc 
        AND Id = b_rec.Id;
        
        -- 如果A队没有记录，插入B队记录
        IF SQL%ROWCOUNT = 0 THEN
            INSERT INTO PHY_A2B_ID (StorerKey, Sku, Loc, Id, QtyTeamA, QtyTeamB)
            VALUES (b_rec.StorerKey, b_rec.Sku, b_rec.Loc, b_rec.Id, 0, b_rec.QtyTeamB);
        END IF;
    END LOOP;
    
    -- 根据结果模式返回数据
    IF p_resultmode = '1' THEN
        -- 返回所有比较结果
        FOR result_rec IN (
            SELECT StorerKey, Sku, Loc, Id, QtyTeamA, QtyTeamB,
                   (QtyTeamA - QtyTeamB) AS Variance
            FROM PHY_A2B_ID
            ORDER BY StorerKey, Sku, Loc, Id
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' || 
                result_rec.Loc || '|' || result_rec.Id || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyTeamB || '|' ||
                result_rec.Variance
            );
        END LOOP;
    ELSIF p_resultmode = '2' THEN
        -- 只返回有差异的记录
        FOR result_rec IN (
            SELECT StorerKey, Sku, Loc, Id, QtyTeamA, QtyTeamB,
                   (QtyTeamA - QtyTeamB) AS Variance
            FROM PHY_A2B_ID
            WHERE QtyTeamA != QtyTeamB
            ORDER BY StorerKey, Sku, Loc, Id
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' || 
                result_rec.Loc || '|' || result_rec.Id || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyTeamB || '|' ||
                result_rec.Variance
            );
        END LOOP;
    END IF;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END nspCompare_a2b_id;
/

-- nspCompare_a2b_lot 存储过程 (A队B队批次比较)
CREATE OR REPLACE PROCEDURE nspCompare_a2b_lot (
    p_resultmode    IN CHAR
) IS
    v_debug NUMBER := 0;
    v_cnt NUMBER;
BEGIN
    -- 清空比较表
    DELETE FROM PHY_A2B_LOT;
    
    -- 插入A队数据
    INSERT INTO PHY_A2B_LOT (StorerKey, Sku, Loc, Lot, QtyTeamA, QtyTeamB)
    SELECT 
        StorerKey, Sku, Loc, Lot, 
        SUM(QtyActual) AS QtyTeamA,
        0 AS QtyTeamB
    FROM PHYSICALDETAIL
    WHERE Team = 'A'
    GROUP BY StorerKey, Sku, Loc, Lot;
    
    -- 更新B队数据
    FOR b_rec IN (
        SELECT StorerKey, Sku, Loc, Lot, SUM(QtyActual) AS QtyTeamB
        FROM PHYSICALDETAIL
        WHERE Team = 'B'
        GROUP BY StorerKey, Sku, Loc, Lot
    ) LOOP
        UPDATE PHY_A2B_LOT
        SET QtyTeamB = b_rec.QtyTeamB
        WHERE StorerKey = b_rec.StorerKey 
        AND Sku = b_rec.Sku 
        AND Loc = b_rec.Loc 
        AND Lot = b_rec.Lot;
        
        -- 如果A队没有记录，插入B队记录
        IF SQL%ROWCOUNT = 0 THEN
            INSERT INTO PHY_A2B_LOT (StorerKey, Sku, Loc, Lot, QtyTeamA, QtyTeamB)
            VALUES (b_rec.StorerKey, b_rec.Sku, b_rec.Loc, b_rec.Lot, 0, b_rec.QtyTeamB);
        END IF;
    END LOOP;
    
    -- 根据结果模式返回数据
    IF p_resultmode = '1' THEN
        -- 返回所有比较结果
        FOR result_rec IN (
            SELECT StorerKey, Sku, Loc, Lot, QtyTeamA, QtyTeamB,
                   (QtyTeamA - QtyTeamB) AS Variance
            FROM PHY_A2B_LOT
            ORDER BY StorerKey, Sku, Loc, Lot
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' || 
                result_rec.Loc || '|' || result_rec.Lot || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyTeamB || '|' ||
                result_rec.Variance
            );
        END LOOP;
    ELSIF p_resultmode = '2' THEN
        -- 只返回有差异的记录
        FOR result_rec IN (
            SELECT StorerKey, Sku, Loc, Lot, QtyTeamA, QtyTeamB,
                   (QtyTeamA - QtyTeamB) AS Variance
            FROM PHY_A2B_LOT
            WHERE QtyTeamA != QtyTeamB
            ORDER BY StorerKey, Sku, Loc, Lot
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' || 
                result_rec.Loc || '|' || result_rec.Lot || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyTeamB || '|' ||
                result_rec.Variance
            );
        END LOOP;
    END IF;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END nspCompare_a2b_lot;
/

-- nspCompare_a2b_sku 存储过程 (A队B队SKU比较)
CREATE OR REPLACE PROCEDURE nspCompare_a2b_sku (
    p_resultmode    IN CHAR
) IS
    v_debug NUMBER := 0;
    v_cnt NUMBER;
BEGIN
    -- 清空比较表
    DELETE FROM PHY_A2B_SKU;
    
    -- 插入A队数据
    INSERT INTO PHY_A2B_SKU (StorerKey, Sku, QtyTeamA, QtyTeamB)
    SELECT 
        StorerKey, Sku, 
        SUM(QtyActual) AS QtyTeamA,
        0 AS QtyTeamB
    FROM PHYSICALDETAIL
    WHERE Team = 'A'
    GROUP BY StorerKey, Sku;
    
    -- 更新B队数据
    FOR b_rec IN (
        SELECT StorerKey, Sku, SUM(QtyActual) AS QtyTeamB
        FROM PHYSICALDETAIL
        WHERE Team = 'B'
        GROUP BY StorerKey, Sku
    ) LOOP
        UPDATE PHY_A2B_SKU
        SET QtyTeamB = b_rec.QtyTeamB
        WHERE StorerKey = b_rec.StorerKey 
        AND Sku = b_rec.Sku;
        
        -- 如果A队没有记录，插入B队记录
        IF SQL%ROWCOUNT = 0 THEN
            INSERT INTO PHY_A2B_SKU (StorerKey, Sku, QtyTeamA, QtyTeamB)
            VALUES (b_rec.StorerKey, b_rec.Sku, 0, b_rec.QtyTeamB);
        END IF;
    END LOOP;
    
    -- 根据结果模式返回数据
    IF p_resultmode = '1' THEN
        -- 返回所有比较结果
        FOR result_rec IN (
            SELECT StorerKey, Sku, QtyTeamA, QtyTeamB,
                   (QtyTeamA - QtyTeamB) AS Variance
            FROM PHY_A2B_SKU
            ORDER BY StorerKey, Sku
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyTeamB || '|' ||
                result_rec.Variance
            );
        END LOOP;
    ELSIF p_resultmode = '2' THEN
        -- 只返回有差异的记录
        FOR result_rec IN (
            SELECT StorerKey, Sku, QtyTeamA, QtyTeamB,
                   (QtyTeamA - QtyTeamB) AS Variance
            FROM PHY_A2B_SKU
            WHERE QtyTeamA != QtyTeamB
            ORDER BY StorerKey, Sku
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyTeamB || '|' ||
                result_rec.Variance
            );
        END LOOP;
    END IF;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END nspCompare_a2b_sku;
/

-- nspCompare_a2b_tag 存储过程 (A队B队标签比较)
CREATE OR REPLACE PROCEDURE nspCompare_a2b_tag (
    p_resultmode    IN CHAR
) IS
    v_debug NUMBER := 0;
    v_cnt NUMBER;
BEGIN
    -- 清空比较表
    DELETE FROM PHY_A2B_TAG;

    -- 插入A队数据
    INSERT INTO PHY_A2B_TAG (StorerKey, Sku, Loc, Id, InventoryTag, QtyTeamA, QtyTeamB)
    SELECT
        StorerKey, Sku, Loc, Id, InventoryTag,
        SUM(QtyActual) AS QtyTeamA,
        0 AS QtyTeamB
    FROM PHYSICALDETAIL
    WHERE Team = 'A'
    GROUP BY StorerKey, Sku, Loc, Id, InventoryTag;

    -- 更新B队数据
    FOR b_rec IN (
        SELECT StorerKey, Sku, Loc, Id, InventoryTag, SUM(QtyActual) AS QtyTeamB
        FROM PHYSICALDETAIL
        WHERE Team = 'B'
        GROUP BY StorerKey, Sku, Loc, Id, InventoryTag
    ) LOOP
        UPDATE PHY_A2B_TAG
        SET QtyTeamB = b_rec.QtyTeamB
        WHERE StorerKey = b_rec.StorerKey
        AND Sku = b_rec.Sku
        AND Loc = b_rec.Loc
        AND Id = b_rec.Id
        AND InventoryTag = b_rec.InventoryTag;

        -- 如果A队没有记录，插入B队记录
        IF SQL%ROWCOUNT = 0 THEN
            INSERT INTO PHY_A2B_TAG (StorerKey, Sku, Loc, Id, InventoryTag, QtyTeamA, QtyTeamB)
            VALUES (b_rec.StorerKey, b_rec.Sku, b_rec.Loc, b_rec.Id, b_rec.InventoryTag, 0, b_rec.QtyTeamB);
        END IF;
    END LOOP;

    -- 根据结果模式返回数据
    IF p_resultmode = '1' THEN
        -- 返回所有比较结果
        FOR result_rec IN (
            SELECT StorerKey, Sku, Loc, Id, InventoryTag, QtyTeamA, QtyTeamB,
                   (QtyTeamA - QtyTeamB) AS Variance
            FROM PHY_A2B_TAG
            ORDER BY StorerKey, Sku, Loc, Id, InventoryTag
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' ||
                result_rec.Loc || '|' || result_rec.Id || '|' ||
                result_rec.InventoryTag || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyTeamB || '|' ||
                result_rec.Variance
            );
        END LOOP;
    ELSIF p_resultmode = '2' THEN
        -- 只返回有差异的记录
        FOR result_rec IN (
            SELECT StorerKey, Sku, Loc, Id, InventoryTag, QtyTeamA, QtyTeamB,
                   (QtyTeamA - QtyTeamB) AS Variance
            FROM PHY_A2B_TAG
            WHERE QtyTeamA != QtyTeamB
            ORDER BY StorerKey, Sku, Loc, Id, InventoryTag
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' ||
                result_rec.Loc || '|' || result_rec.Id || '|' ||
                result_rec.InventoryTag || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyTeamB || '|' ||
                result_rec.Variance
            );
        END LOOP;
    END IF;

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END nspCompare_a2b_tag;
/

-- =============================================
-- 库存与盘点比较存储过程
-- =============================================

-- nspCompare_inv2a_sku 存储过程 (库存与A队SKU比较)
CREATE OR REPLACE PROCEDURE nspCompare_inv2a_sku (
    p_resultmode    IN CHAR
) IS
    v_debug NUMBER := 0;
    v_cnt NUMBER;
BEGIN
    -- 清空比较表
    DELETE FROM PHY_INV2A_SKU;

    -- 插入库存数据
    INSERT INTO PHY_INV2A_SKU (StorerKey, Sku, QtyTeamA, QtyInventory)
    SELECT
        StorerKey, Sku,
        0 AS QtyTeamA,
        SUM(Qty) AS QtyInventory
    FROM LOTxLOCxID
    GROUP BY StorerKey, Sku;

    -- 更新A队数据
    FOR a_rec IN (
        SELECT StorerKey, Sku, SUM(QtyActual) AS QtyTeamA
        FROM PHYSICALDETAIL
        WHERE Team = 'A'
        GROUP BY StorerKey, Sku
    ) LOOP
        UPDATE PHY_INV2A_SKU
        SET QtyTeamA = a_rec.QtyTeamA
        WHERE StorerKey = a_rec.StorerKey
        AND Sku = a_rec.Sku;

        -- 如果库存没有记录，插入A队记录
        IF SQL%ROWCOUNT = 0 THEN
            INSERT INTO PHY_INV2A_SKU (StorerKey, Sku, QtyTeamA, QtyInventory)
            VALUES (a_rec.StorerKey, a_rec.Sku, a_rec.QtyTeamA, 0);
        END IF;
    END LOOP;

    -- 根据结果模式返回数据
    IF p_resultmode = '1' THEN
        -- 返回所有比较结果
        FOR result_rec IN (
            SELECT StorerKey, Sku, QtyTeamA, QtyInventory,
                   (QtyTeamA - QtyInventory) AS Variance
            FROM PHY_INV2A_SKU
            ORDER BY StorerKey, Sku
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyInventory || '|' ||
                result_rec.Variance
            );
        END LOOP;
    ELSIF p_resultmode = '2' THEN
        -- 只返回有差异的记录
        FOR result_rec IN (
            SELECT StorerKey, Sku, QtyTeamA, QtyInventory,
                   (QtyTeamA - QtyInventory) AS Variance
            FROM PHY_INV2A_SKU
            WHERE QtyTeamA != QtyInventory
            ORDER BY StorerKey, Sku
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyInventory || '|' ||
                result_rec.Variance
            );
        END LOOP;
    END IF;

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END nspCompare_inv2a_sku;
/

-- nspCompare_inv2a_lot 存储过程 (库存与A队批次比较)
CREATE OR REPLACE PROCEDURE nspCompare_inv2a_lot (
    p_resultmode    IN CHAR
) IS
    v_debug NUMBER := 0;
    v_cnt NUMBER;
BEGIN
    -- 清空比较表
    DELETE FROM PHY_INV2A_LOT;

    -- 插入库存数据
    INSERT INTO PHY_INV2A_LOT (StorerKey, Sku, Lot, QtyTeamA, QtyInventory)
    SELECT
        StorerKey, Sku, Lot,
        0 AS QtyTeamA,
        SUM(Qty) AS QtyInventory
    FROM LOTxLOCxID
    GROUP BY StorerKey, Sku, Lot;

    -- 更新A队数据
    FOR a_rec IN (
        SELECT StorerKey, Sku, Lot, SUM(QtyActual) AS QtyTeamA
        FROM PHYSICALDETAIL
        WHERE Team = 'A'
        GROUP BY StorerKey, Sku, Lot
    ) LOOP
        UPDATE PHY_INV2A_LOT
        SET QtyTeamA = a_rec.QtyTeamA
        WHERE StorerKey = a_rec.StorerKey
        AND Sku = a_rec.Sku
        AND Lot = a_rec.Lot;

        -- 如果库存没有记录，插入A队记录
        IF SQL%ROWCOUNT = 0 THEN
            INSERT INTO PHY_INV2A_LOT (StorerKey, Sku, Lot, QtyTeamA, QtyInventory)
            VALUES (a_rec.StorerKey, a_rec.Sku, a_rec.Lot, a_rec.QtyTeamA, 0);
        END IF;
    END LOOP;

    -- 根据结果模式返回数据
    IF p_resultmode = '1' THEN
        -- 返回所有比较结果
        FOR result_rec IN (
            SELECT StorerKey, Sku, Lot, QtyTeamA, QtyInventory,
                   (QtyTeamA - QtyInventory) AS Variance
            FROM PHY_INV2A_LOT
            ORDER BY StorerKey, Sku, Lot
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' ||
                result_rec.Lot || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyInventory || '|' ||
                result_rec.Variance
            );
        END LOOP;
    ELSIF p_resultmode = '2' THEN
        -- 只返回有差异的记录
        FOR result_rec IN (
            SELECT StorerKey, Sku, Lot, QtyTeamA, QtyInventory,
                   (QtyTeamA - QtyInventory) AS Variance
            FROM PHY_INV2A_LOT
            WHERE QtyTeamA != QtyInventory
            ORDER BY StorerKey, Sku, Lot
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                result_rec.StorerKey || '|' || result_rec.Sku || '|' ||
                result_rec.Lot || '|' ||
                result_rec.QtyTeamA || '|' || result_rec.QtyInventory || '|' ||
                result_rec.Variance
            );
        END LOOP;
    END IF;

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END nspCompare_inv2a_lot;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle物理盘点存储过程脚本第二部分执行完成';
PROMPT '>>> 已创建盘点比较存储过程 (6个)';
PROMPT '>>> 请继续执行后续脚本以创建更多存储过程';
