-- =============================================
-- 创建触发器脚本
-- 功能：创建仓库管理系统的数据完整性触发器
-- 用途：维护数据一致性和业务规则
-- =============================================

-- =============================================
-- PALLET 表触发器
-- =============================================

-- PALLET 表插入触发器
IF OBJECT_ID('ntrPalletHeaderAdd') IS NOT NULL
DROP TRIGGER ntrPalletHeaderAdd
GO

CREATE TRIGGER ntrPalletHeaderAdd
ON  Pallet
FOR INSERT
AS
BEGIN
DECLARE
@b_Success            int       -- Populated by calls to stored procedures - was the proc successful?
,         @n_err                int       -- Error number returned by stored procedure or this trigger
,         @n_err2 int              -- For Additional Error Detection
,         @c_errmsg             char(250) -- Error message returned by stored procedure or this trigger
,         @n_continue int                 

SELECT @n_continue = 1
SELECT @b_Success = 1

-- 检查是否有记录被插入
IF @@ROWCOUNT = 0
BEGIN
RETURN
END

-- 验证插入的数据
IF EXISTS (SELECT 1 FROM inserted WHERE PalletKey IS NULL OR PalletKey = '')
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'PalletKey cannot be null or empty'
GOTO ERROR_HANDLER
END

-- 记录成功插入
PRINT '<<<Pallet Header Record Added Successfully>>>'

RETURN

ERROR_HANDLER:
SELECT @b_Success = 0
RAISERROR(@c_errmsg, 16, 1)
ROLLBACK TRANSACTION

END
GO

-- PALLET 表更新触发器
IF OBJECT_ID('ntrPalletHeaderUpdate') IS NOT NULL
DROP TRIGGER ntrPalletHeaderUpdate
GO

CREATE TRIGGER ntrPalletHeaderUpdate
ON  Pallet
FOR UPDATE
AS
BEGIN
IF @@ROWCOUNT = 0
BEGIN
RETURN
END

DECLARE @b_debug int
SELECT @b_debug = 0

DECLARE
@b_Success            int       -- Populated by calls to stored procedures - was the proc successful?
,         @n_err                int       -- Error number returned by stored procedure or this trigger
,         @c_errmsg             char(250) -- Error message returned by stored procedure or this trigger

SELECT @b_Success = 1

-- 验证更新的数据
IF EXISTS (SELECT 1 FROM inserted WHERE PalletKey IS NULL OR PalletKey = '')
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'PalletKey cannot be null or empty'
GOTO ERROR_HANDLER
END

-- 记录成功更新
IF @b_debug = 1
PRINT '<<<Pallet Header Record Updated Successfully>>>'

RETURN

ERROR_HANDLER:
SELECT @b_Success = 0
RAISERROR(@c_errmsg, 16, 1)
ROLLBACK TRANSACTION

END
GO

-- =============================================
-- PALLETDETAIL 表触发器
-- =============================================

-- PALLETDETAIL 表删除触发器
IF OBJECT_ID('ntrPalletDetailDelete') IS NOT NULL
DROP TRIGGER ntrPalletDetailDelete
GO

CREATE TRIGGER ntrPalletDetailDelete
ON PALLETDETAIL
FOR DELETE
AS
BEGIN
IF @@ROWCOUNT = 0
BEGIN
RETURN
END

DECLARE @b_Success       int,       -- Populated by calls to stored procedures - was the proc successful?
@n_err              int,       -- Error number returned by stored procedure or this trigger
@c_errmsg           char(250), -- Error message returned by stored procedure or this trigger
@c_palletkey        char(10)

SELECT @b_Success = 1

-- 获取被删除记录的托盘键
SELECT @c_palletkey = PalletKey FROM deleted

-- 更新托盘头表的相关信息
UPDATE Pallet 
SET EditDate = GETDATE(),
    EditWho = USER
WHERE PalletKey = @c_palletkey

-- 记录删除操作
PRINT '<<<Pallet Detail Record Deleted Successfully>>>'

RETURN

ERROR_HANDLER:
SELECT @b_Success = 0
RAISERROR(@c_errmsg, 16, 1)

END
GO

-- PALLETDETAIL 表插入触发器
IF OBJECT_ID('ntrPalletDetailAdd') IS NOT NULL
DROP TRIGGER ntrPalletDetailAdd
GO

CREATE TRIGGER ntrPalletDetailAdd
ON  PALLETDETAIL
FOR INSERT
AS
BEGIN
DECLARE @b_debug int
SELECT @b_debug = 0

DECLARE
@b_Success            int       -- Populated by calls to stored procedures - was the proc successful?
,         @n_err                int       -- Error number returned by stored procedure or this trigger
,         @n_err2 int              -- For Additional Error Detection
,         @c_errmsg             char(250) -- Error message returned by stored procedure or this trigger
,         @c_palletkey          char(10)

SELECT @b_Success = 1

-- 检查是否有记录被插入
IF @@ROWCOUNT = 0
BEGIN
RETURN
END

-- 获取插入记录的托盘键
SELECT @c_palletkey = PalletKey FROM inserted

-- 验证插入的数据
IF EXISTS (SELECT 1 FROM inserted WHERE PalletKey IS NULL OR PalletKey = '')
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'PalletKey cannot be null or empty'
GOTO ERROR_HANDLER
END

-- 更新托盘头表的相关信息
UPDATE Pallet 
SET EditDate = GETDATE(),
    EditWho = USER
WHERE PalletKey = @c_palletkey

-- 记录成功插入
IF @b_debug = 1
PRINT '<<<Pallet Detail Record Added Successfully>>>'

RETURN

ERROR_HANDLER:
SELECT @b_Success = 0
RAISERROR(@c_errmsg, 16, 1)
ROLLBACK TRANSACTION

END
GO

-- PALLETDETAIL 表更新触发器
IF OBJECT_ID('ntrPalletDetailUpdate') IS NOT NULL
DROP TRIGGER ntrPalletDetailUpdate
GO

CREATE TRIGGER ntrPalletDetailUpdate
ON  PALLETDETAIL
FOR UPDATE
AS
BEGIN
IF @@ROWCOUNT = 0
BEGIN
RETURN
END

DECLARE
@b_Success            int       -- Populated by calls to stored procedures - was the proc successful?
,         @n_err                int       -- Error number returned by stored procedure or this trigger
,         @c_errmsg             char(250) -- Error message returned by stored procedure or this trigger
,         @c_palletkey          char(10)

SELECT @b_Success = 1

-- 获取更新记录的托盘键
SELECT @c_palletkey = PalletKey FROM inserted

-- 验证更新的数据
IF EXISTS (SELECT 1 FROM inserted WHERE PalletKey IS NULL OR PalletKey = '')
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'PalletKey cannot be null or empty'
GOTO ERROR_HANDLER
END

-- 更新托盘头表的相关信息
UPDATE Pallet 
SET EditDate = GETDATE(),
    EditWho = USER
WHERE PalletKey = @c_palletkey

-- 记录成功更新
PRINT '<<<Pallet Detail Record Updated Successfully>>>'

RETURN

ERROR_HANDLER:
SELECT @b_Success = 0
RAISERROR(@c_errmsg, 16, 1)
ROLLBACK TRANSACTION

END
GO

-- =============================================
-- SKUxLOC 表触发器
-- =============================================

-- SKUxLOC 表更新触发器
IF OBJECT_ID('ntrSkuXLocUpdate') IS NOT NULL
DROP TRIGGER ntrSkuXLocUpdate
GO

CREATE TRIGGER ntrSkuXLocUpdate
ON  SKUxLOC
FOR UPDATE
AS
BEGIN
IF @@ROWCOUNT = 0
BEGIN
RETURN
END

DECLARE
@b_Success            int       -- Populated by calls to stored procedures - was the proc successful?
,         @n_err                int       -- Error number returned by stored procedure or this trigger
,         @c_errmsg             char(250) -- Error message returned by stored procedure or this trigger

SELECT @b_Success = 1

-- 验证数量约束
IF EXISTS (SELECT 1 FROM inserted WHERE Qty < 0)
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'Quantity cannot be negative'
GOTO ERROR_HANDLER
END

-- 验证分配数量约束
IF EXISTS (SELECT 1 FROM inserted WHERE QtyAllocated < 0)
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'Allocated quantity cannot be negative'
GOTO ERROR_HANDLER
END

-- 验证拣选数量约束
IF EXISTS (SELECT 1 FROM inserted WHERE QtyPicked < 0)
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'Picked quantity cannot be negative'
GOTO ERROR_HANDLER
END

-- 验证总量约束
IF EXISTS (SELECT 1 FROM inserted WHERE Qty + QtyExpected < QtyAllocated + QtyPicked)
BEGIN
SELECT @n_err = 9999
SELECT @c_errmsg = 'Available quantity cannot be less than allocated and picked quantities'
GOTO ERROR_HANDLER
END

-- 记录成功更新
PRINT '<<<SKUxLOC Record Updated Successfully>>>'

RETURN

ERROR_HANDLER:
SELECT @b_Success = 0
RAISERROR(@c_errmsg, 16, 1)
ROLLBACK TRANSACTION

END
GO

-- 注意：此脚本包含核心触发器
-- 更多触发器请参考原始文件或后续脚本
