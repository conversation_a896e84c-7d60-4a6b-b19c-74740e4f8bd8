-- =============================================
-- Oracle版本 - 创建附加表结构脚本 (第二部分)
-- 功能：创建仓库管理系统的附加表结构 (Oracle版本)
-- 用途：ID管理、存储商、系统配置等核心支持表
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- ID 表 (标识符表) - 容器/托盘标识表
-- =============================================
CREATE TABLE ID
(Id                       CHAR(18) NOT NULL,
IdType                   CHAR(10) NOT NULL
CONSTRAINT DF_ID_IdType DEFAULT '0',
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_ID_Status DEFAULT '0',
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_ID_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_ID_Sku DEFAULT ' ',
Lot                      CHAR(10) NOT NULL
CONSTRAINT DF_ID_Lot DEFAULT ' ',
Loc                      CHAR(10) NOT NULL
CONSTRAINT DF_ID_Loc DEFAULT 'UNKNOWN',
Qty                      NUMBER(10) NOT NULL
CONSTRAINT DF_ID_Qty DEFAULT 0,
PackKey                  CHAR(10) NOT NULL
CONSTRAINT DF_ID_PackKey DEFAULT 'STD',
UOM                      CHAR(10) NOT NULL
CONSTRAINT DF_ID_UOM DEFAULT 'EA',
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_ID_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_ID_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_ID_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_ID_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_ID_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE ID ADD CONSTRAINT PK_ID PRIMARY KEY (Id);

-- 添加检查约束
ALTER TABLE ID ADD CONSTRAINT CK_ID_IdType CHECK (IdType IN ('0', '1', '2', '3', '4', '5'));
ALTER TABLE ID ADD CONSTRAINT CK_ID_Status CHECK (Status IN ('0', '1', '2', '3', '4', '5'));

-- 创建索引
CREATE INDEX IX_ID_StorerKey ON ID (StorerKey);
CREATE INDEX IX_ID_Sku ON ID (Sku);
CREATE INDEX IX_ID_Lot ON ID (Lot);
CREATE INDEX IX_ID_Loc ON ID (Loc);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON ID TO nsql;

PROMPT '>>> 已创建表 ID (标识符表)';

-- =============================================
-- STORER 表 (存储商表) - 客户/供应商主数据表
-- =============================================
CREATE TABLE STORER
(StorerKey                CHAR(15) NOT NULL,
Company                  VARCHAR2(45) NOT NULL
CONSTRAINT DF_STORER_Company DEFAULT ' ',
Contact1                 VARCHAR2(30) NULL,
Contact2                 VARCHAR2(30) NULL,
Address1                 VARCHAR2(45) NULL,
Address2                 VARCHAR2(45) NULL,
Address3                 VARCHAR2(45) NULL,
Address4                 VARCHAR2(45) NULL,
City                     VARCHAR2(45) NULL,
State                    CHAR(2) NULL,
Zip                      VARCHAR2(18) NULL,
Country                  VARCHAR2(30) NULL,
ISOCntryCode             CHAR(10) NULL,
Phone1                   VARCHAR2(18) NULL,
Phone2                   VARCHAR2(18) NULL,
Fax1                     VARCHAR2(18) NULL,
Fax2                     VARCHAR2(18) NULL,
Email1                   VARCHAR2(60) NULL,
Email2                   VARCHAR2(60) NULL,
Type                     CHAR(10) NOT NULL
CONSTRAINT DF_STORER_Type DEFAULT '0',
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_STORER_Status DEFAULT '0',
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_STORER_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_STORER_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_STORER_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_STORER_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_STORER_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE STORER ADD CONSTRAINT PK_STORER PRIMARY KEY (StorerKey);

-- 添加检查约束
ALTER TABLE STORER ADD CONSTRAINT CK_STORER_Type CHECK (Type IN ('0', '1', '2', '3', '4', '5'));
ALTER TABLE STORER ADD CONSTRAINT CK_STORER_Status CHECK (Status IN ('0', '1', '2', '3', '4', '5'));

-- 创建索引
CREATE INDEX IX_STORER_Company ON STORER (Company);
CREATE INDEX IX_STORER_Type ON STORER (Type);
CREATE INDEX IX_STORER_Status ON STORER (Status);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON STORER TO nsql;

PROMPT '>>> 已创建表 STORER (存储商表)';

-- =============================================
-- NCOUNTER 表 (计数器表) - 序号生成表
-- =============================================
CREATE TABLE NCOUNTER
(keyname       CHAR(30) NOT NULL,
keyvalue       NUMBER(10) NOT NULL
CONSTRAINT DF_NCOUNTER_keyvalue DEFAULT 0,
AddDate        DATE NOT NULL
CONSTRAINT DF_NCOUNTER_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_NCOUNTER_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_NCOUNTER_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_NCOUNTER_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE NCOUNTER ADD CONSTRAINT PK_NCOUNTER PRIMARY KEY (keyname);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON NCOUNTER TO nsql;

PROMPT '>>> 已创建表 NCOUNTER (计数器表)';

-- =============================================
-- NCOUNTERITRN 表 (事务计数器表) - 事务序号生成表
-- =============================================
CREATE TABLE NCOUNTERITRN
(keyname       CHAR(30) NOT NULL,
keyvalue       NUMBER(10) NOT NULL
CONSTRAINT DF_NCOUNTERITRN_keyvalue DEFAULT 0,
AddDate        DATE NOT NULL
CONSTRAINT DF_NCOUNTERITRN_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_NCOUNTERITRN_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_NCOUNTERITRN_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_NCOUNTERITRN_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE NCOUNTERITRN ADD CONSTRAINT PK_NCOUNTERITRN PRIMARY KEY (keyname);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON NCOUNTERITRN TO nsql;

PROMPT '>>> 已创建表 NCOUNTERITRN (事务计数器表)';

-- =============================================
-- NCOUNTERPICK 表 (拣选计数器表) - 拣选序号生成表
-- =============================================
CREATE TABLE NCOUNTERPICK
(keyname       CHAR(30) NOT NULL,
keyvalue       NUMBER(10) NOT NULL
CONSTRAINT DF_NCOUNTERPICK_keyvalue DEFAULT 0,
AddDate        DATE NOT NULL
CONSTRAINT DF_NCOUNTERPICK_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_NCOUNTERPICK_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_NCOUNTERPICK_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_NCOUNTERPICK_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE NCOUNTERPICK ADD CONSTRAINT PK_NCOUNTERPICK PRIMARY KEY (keyname);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON NCOUNTERPICK TO nsql;

PROMPT '>>> 已创建表 NCOUNTERPICK (拣选计数器表)';

-- =============================================
-- CODELIST 表 (代码列表表) - 代码分类表
-- =============================================
CREATE TABLE CODELIST
(LISTNAME                 CHAR(10) NOT NULL,
LISTTYPE                 CHAR(10) NOT NULL
CONSTRAINT DF_CODELIST_LISTTYPE DEFAULT '0',
DESCRIPTION              VARCHAR2(60) NOT NULL
CONSTRAINT DF_CODELIST_DESCRIPTION DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_CODELIST_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_CODELIST_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_CODELIST_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_CODELIST_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE CODELIST ADD CONSTRAINT PK_CODELIST PRIMARY KEY (LISTNAME);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON CODELIST TO nsql;

PROMPT '>>> 已创建表 CODELIST (代码列表表)';

-- 提交事务
COMMIT;

PROMPT '>>> 附加表结构脚本执行完成';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
