-- =============================================
-- Oracle版本 - 创建剩余遗漏表结构脚本 (第九部分)
-- 功能：创建仓库管理系统中剩余遗漏的重要表结构 (Oracle版本)
-- 用途：日历管理、计费明细、轮询任务、服务管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 日历管理表
-- =============================================

-- CALENDAR 表 (日历表)
CREATE TABLE CALENDAR
(CalendarGroup          CHAR(10) NOT NULL,
CalendarDate           DATE NOT NULL
CONSTRAINT DF_CALENDAR_CalendarDate DEFAULT SYSDATE,
WorkingDay             CHAR(1) NOT NULL
CONSTRAINT DF_CALENDAR_WorkingDay DEFAULT 'Y',
AddDate                DATE NOT NULL
CONSTRAINT DF_CALENDAR_AddDate DEFAULT SYSDATE,
AddWho                 CHAR(18) NOT NULL
CONSTRAINT DF_CALENDAR_AddWho DEFAULT USER,
EditDate               DATE NOT NULL
CONSTRAINT DF_CALENDAR_EditDate DEFAULT SYSDATE,
EditWho                CHAR(18) NOT NULL
CONSTRAINT DF_CALENDAR_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE CALENDAR ADD CONSTRAINT PK_CALENDAR PRIMARY KEY (CalendarGroup, CalendarDate);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON CALENDAR TO nsql;

PROMPT '>>> 已创建表 CALENDAR (日历表)';

-- CALENDARDETAIL 表 (日历明细表)
CREATE TABLE CALENDARDETAIL
(CalendarGroup           CHAR(10) NOT NULL,
CalendarDate            DATE NOT NULL,
StartTime               DATE NOT NULL
CONSTRAINT DF_CALENDARDETAIL_StartTime DEFAULT SYSDATE,
EndTime                 DATE NOT NULL
CONSTRAINT DF_CALENDARDETAIL_EndTime DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_CALENDARDETAIL_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_CALENDARDETAIL_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_CALENDARDETAIL_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_CALENDARDETAIL_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE CALENDARDETAIL ADD CONSTRAINT PK_CALENDARDETAIL 
PRIMARY KEY (CalendarGroup, CalendarDate, StartTime);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON CALENDARDETAIL TO nsql;

PROMPT '>>> 已创建表 CALENDARDETAIL (日历明细表)';

-- =============================================
-- 计费明细表
-- =============================================

-- BILL_STOCKMOVEMENT 表 (库存移动计费表)
CREATE TABLE BILL_STOCKMOVEMENT
(StorerKey       CHAR(15) NOT NULL,
BillThruDate    DATE NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_BillThruDate DEFAULT SYSDATE,
QtyReceived     NUMBER(10) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_QtyReceived DEFAULT 0,
QtyShipped      NUMBER(10) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_QtyShipped DEFAULT 0,
AddDate         DATE NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_AddDate DEFAULT SYSDATE,
AddWho          CHAR(18) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_AddWho DEFAULT USER,
EditDate        DATE NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_EditDate DEFAULT SYSDATE,
EditWho         CHAR(18) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE BILL_STOCKMOVEMENT ADD CONSTRAINT PK_BILL_STOCKMOVEMENT 
PRIMARY KEY (StorerKey, BillThruDate);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BILL_STOCKMOVEMENT TO nsql;

PROMPT '>>> 已创建表 BILL_STOCKMOVEMENT (库存移动计费表)';

-- BILL_STOCKMOVEMENT_DETAIL 表 (库存移动计费明细表)
CREATE TABLE BILL_STOCKMOVEMENT_DETAIL
(StorerKey       CHAR(15) NOT NULL,
BillThruDate    DATE NOT NULL,
Sku             CHAR(20) NOT NULL,
QtyReceived     NUMBER(10) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_QtyReceived DEFAULT 0,
QtyShipped      NUMBER(10) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_QtyShipped DEFAULT 0,
AddDate         DATE NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_AddDate DEFAULT SYSDATE,
AddWho          CHAR(18) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_AddWho DEFAULT USER,
EditDate        DATE NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_EditDate DEFAULT SYSDATE,
EditWho         CHAR(18) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE BILL_STOCKMOVEMENT_DETAIL ADD CONSTRAINT PK_BILL_STOCKMOVEMENT_DETAIL 
PRIMARY KEY (StorerKey, BillThruDate, Sku);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BILL_STOCKMOVEMENT_DETAIL TO nsql;

PROMPT '>>> 已创建表 BILL_STOCKMOVEMENT_DETAIL (库存移动计费明细表)';

-- =============================================
-- 轮询任务表
-- =============================================

-- POLL_ALLOCATE 表 (分配轮询表)
CREATE TABLE POLL_ALLOCATE
(orderkey                 CHAR(10) NOT NULL,
orderlineNumber          CHAR(5) NOT NULL,
storerkey                CHAR(15) NOT NULL,
sku                      CHAR(20) NOT NULL,
requestedqty             NUMBER(10) NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_requestedqty DEFAULT 0,
allocatedqty             NUMBER(10) NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_allocatedqty DEFAULT 0,
status                   CHAR(10) NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_status DEFAULT '0',
AddDate                  DATE NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE POLL_ALLOCATE ADD CONSTRAINT PK_POLL_ALLOCATE 
PRIMARY KEY (orderkey, orderlineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON POLL_ALLOCATE TO nsql;

PROMPT '>>> 已创建表 POLL_ALLOCATE (分配轮询表)';

-- POLL_PICK 表 (拣选轮询表)
CREATE TABLE POLL_PICK
(Caseid                   CHAR(10) NOT NULL,
orderkey                 CHAR(10) NOT NULL,
orderlineNumber          CHAR(5) NOT NULL,
storerkey                CHAR(15) NOT NULL,
sku                      CHAR(20) NOT NULL,
requestedqty             NUMBER(10) NOT NULL
CONSTRAINT DF_POLL_PICK_requestedqty DEFAULT 0,
pickedqty                NUMBER(10) NOT NULL
CONSTRAINT DF_POLL_PICK_pickedqty DEFAULT 0,
status                   CHAR(10) NOT NULL
CONSTRAINT DF_POLL_PICK_status DEFAULT '0',
AddDate                  DATE NOT NULL
CONSTRAINT DF_POLL_PICK_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_POLL_PICK_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_POLL_PICK_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_POLL_PICK_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE POLL_PICK ADD CONSTRAINT PK_POLL_PICK 
PRIMARY KEY (Caseid, orderkey, orderlineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON POLL_PICK TO nsql;

PROMPT '>>> 已创建表 POLL_PICK (拣选轮询表)';

-- POLL_SHIP 表 (发货轮询表)
CREATE TABLE POLL_SHIP
(Caseid                   CHAR(10) NOT NULL,
orderkey                 CHAR(10) NOT NULL,
storerkey                CHAR(15) NOT NULL,
status                   CHAR(10) NOT NULL
CONSTRAINT DF_POLL_SHIP_status DEFAULT '0',
shippeddate              DATE NULL,
AddDate                  DATE NOT NULL
CONSTRAINT DF_POLL_SHIP_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_POLL_SHIP_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_POLL_SHIP_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_POLL_SHIP_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE POLL_SHIP ADD CONSTRAINT PK_POLL_SHIP PRIMARY KEY (Caseid, orderkey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON POLL_SHIP TO nsql;

PROMPT '>>> 已创建表 POLL_SHIP (发货轮询表)';

-- POLL_UPDATE 表 (更新轮询表)
CREATE TABLE POLL_UPDATE
(PollUpdateKey            NUMBER(10) NOT NULL,
tablename                CHAR(30) NOT NULL,
keyfield1                CHAR(30) NOT NULL,
keyvalue1                CHAR(30) NOT NULL,
keyfield2                CHAR(30) NOT NULL,
keyvalue2                CHAR(30) NOT NULL,
keyfield3                CHAR(30) NOT NULL,
keyvalue3                CHAR(30) NOT NULL,
status                   CHAR(10) NOT NULL
CONSTRAINT DF_POLL_UPDATE_status DEFAULT '0',
AddDate                  DATE NOT NULL
CONSTRAINT DF_POLL_UPDATE_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_POLL_UPDATE_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_POLL_UPDATE_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_POLL_UPDATE_EditWho DEFAULT USER
);

-- 创建序列
CREATE SEQUENCE SEQ_POLL_UPDATE START WITH 1 INCREMENT BY 1;

-- 添加主键
ALTER TABLE POLL_UPDATE ADD CONSTRAINT PK_POLL_UPDATE PRIMARY KEY (PollUpdateKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON POLL_UPDATE TO nsql;

PROMPT '>>> 已创建表 POLL_UPDATE (更新轮询表)';

-- POLL_PRINT 表 (打印轮询表)
CREATE TABLE POLL_PRINT
(printtype                CHAR(10) NOT NULL,
keyfield1                CHAR(30) NOT NULL,
keyvalue1                CHAR(30) NOT NULL,
keyfield2                CHAR(30) NOT NULL,
keyvalue2                CHAR(30) NOT NULL,
keyfield3                CHAR(30) NOT NULL,
keyvalue3                CHAR(30) NOT NULL,
keyfield4                CHAR(30) NOT NULL,
keyvalue4                CHAR(30) NOT NULL,
keyfield5                CHAR(30) NOT NULL,
keyvalue5                CHAR(30) NOT NULL,
status                   CHAR(10) NOT NULL
CONSTRAINT DF_POLL_PRINT_status DEFAULT '0',
AddDate                  DATE NOT NULL
CONSTRAINT DF_POLL_PRINT_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_POLL_PRINT_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_POLL_PRINT_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_POLL_PRINT_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE POLL_PRINT ADD CONSTRAINT PK_POLL_PRINT
PRIMARY KEY (printtype, keyfield1, keyvalue1, keyfield2, keyvalue2, keyfield3, keyvalue3);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON POLL_PRINT TO nsql;

PROMPT '>>> 已创建表 POLL_PRINT (打印轮询表)';

-- =============================================
-- 服务管理表
-- =============================================

-- Services 表 (服务表)
CREATE TABLE Services
(Servicekey              CHAR(10) NOT NULL,
SupportFlag             CHAR(1) NOT NULL
CONSTRAINT DF_Services_SupportFlag DEFAULT 'A',
Descrip                 CHAR(30) NOT NULL
CONSTRAINT DF_Services_Descrip DEFAULT ' ',
AddDate                 DATE NOT NULL
CONSTRAINT DF_Services_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_Services_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_Services_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_Services_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE Services ADD CONSTRAINT PK_Services PRIMARY KEY (Servicekey);

-- 添加检查约束
ALTER TABLE Services ADD CONSTRAINT CK_Services_SupportFlag
CHECK (SupportFlag IN ('A', 'I', 'D'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Services TO nsql;

PROMPT '>>> 已创建表 Services (服务表)';

-- Accessorial 表 (附加服务表)
CREATE TABLE Accessorial
(Accessorialkey              CHAR(10) NOT NULL,
SupportFlag                 CHAR(1) NOT NULL
CONSTRAINT DF_Accessorial_SupportFlag DEFAULT 'A',
Descrip                     CHAR(30) NOT NULL
CONSTRAINT DF_Accessorial_Descrip DEFAULT ' ',
TariffKey                   CHAR(10) NOT NULL
CONSTRAINT DF_Accessorial_TariffKey DEFAULT ' ',
AddDate                     DATE NOT NULL
CONSTRAINT DF_Accessorial_AddDate DEFAULT SYSDATE,
AddWho                      CHAR(18) NOT NULL
CONSTRAINT DF_Accessorial_AddWho DEFAULT USER,
EditDate                    DATE NOT NULL
CONSTRAINT DF_Accessorial_EditDate DEFAULT SYSDATE,
EditWho                     CHAR(18) NOT NULL
CONSTRAINT DF_Accessorial_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE Accessorial ADD CONSTRAINT PK_Accessorial PRIMARY KEY (Accessorialkey);

-- 添加检查约束
ALTER TABLE Accessorial ADD CONSTRAINT CK_Accessorial_SupportFlag
CHECK (SupportFlag IN ('A', 'I', 'D'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Accessorial TO nsql;

PROMPT '>>> 已创建表 Accessorial (附加服务表)';

-- 提交事务
COMMIT;

PROMPT '>>> 剩余遗漏表结构脚本执行完成 (10张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
