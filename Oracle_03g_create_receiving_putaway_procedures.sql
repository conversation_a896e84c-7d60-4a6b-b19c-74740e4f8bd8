-- =============================================
-- Oracle版本 - 创建接收和上架管理存储过程脚本
-- 功能：创建仓库管理系统的接收和上架管理相关存储过程 (Oracle版本)
-- 用途：ASN处理、收货确认、上架策略、质量检查等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中提取的接收和上架管理存储过程转换为Oracle语法
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- ASN和收货处理存储过程
-- =============================================

-- nspASNCreate 存储过程 (创建ASN)
CREATE OR REPLACE PROCEDURE nspASNCreate (
    p_externasnkey      IN CHAR,
    p_storerkey         IN CHAR,
    p_supplierkey       IN CHAR,
    p_asndate           IN DATE,
    p_expecteddate      IN DATE,
    p_asnkey            OUT CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 检查外部ASN键是否已存在
    SELECT COUNT(*) INTO v_cnt
    FROM ASN
    WHERE ExternASNKey = p_externasnkey AND StorerKey = p_storerkey;
    
    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 81001;
        p_errmsg := 'NSQL81001: External ASN key already exists';
        RETURN;
    END IF;
    
    -- 获取ASN键
    nspg_getkey('ASNKey', 10, p_asnkey, p_success, p_err, p_errmsg);
    
    IF p_success = 1 THEN
        -- 创建ASN头
        INSERT INTO ASN (
            ASNKey, ExternASNKey, StorerKey, SupplierKey,
            ASNDate, ExpectedDate, Status, Type,
            EffectiveDate, AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            p_asnkey, p_externasnkey, p_storerkey, p_supplierkey,
            p_asndate, p_expecteddate, '0', 'ASN',
            v_currentdatetime, v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
        );
        
        COMMIT;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspASNCreate');
        ROLLBACK;
END nspASNCreate;
/

-- nspASNAddDetail 存储过程 (添加ASN明细)
CREATE OR REPLACE PROCEDURE nspASNAddDetail (
    p_asnkey            IN CHAR,
    p_asnlinenumber     IN CHAR,
    p_externlinenum     IN CHAR,
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_qty               IN NUMBER,
    p_uom               IN CHAR,
    p_packkey           IN CHAR,
    p_lot               IN CHAR,
    p_lottable01        IN CHAR,
    p_lottable02        IN CHAR,
    p_lottable03        IN CHAR,
    p_lottable04        IN DATE,
    p_lottable05        IN DATE,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
    v_asndetailkey CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 检查ASN是否存在
    SELECT COUNT(*) INTO v_cnt
    FROM ASN
    WHERE ASNKey = p_asnkey;
    
    IF v_cnt = 0 THEN
        p_success := 0;
        p_err := 81002;
        p_errmsg := 'NSQL81002: ASN not found';
        RETURN;
    END IF;
    
    -- 检查ASN行是否已存在
    SELECT COUNT(*) INTO v_cnt
    FROM ASNDETAIL
    WHERE ASNKey = p_asnkey AND ASNLineNumber = p_asnlinenumber;
    
    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 81003;
        p_errmsg := 'NSQL81003: ASN line already exists';
        RETURN;
    END IF;
    
    -- 获取ASN明细键
    nspg_getkey('ASNDetailKey', 10, v_asndetailkey, p_success, p_err, p_errmsg);
    
    IF p_success = 1 THEN
        -- 创建ASN明细
        INSERT INTO ASNDETAIL (
            ASNDetailKey, ASNKey, ASNLineNumber, ExternLineNumber,
            StorerKey, Sku, Qty, QtyReceived, UOM, PackKey,
            Lot, Lottable01, Lottable02, Lottable03, Lottable04, Lottable05,
            Status, EffectiveDate, AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            v_asndetailkey, p_asnkey, p_asnlinenumber, p_externlinenum,
            p_storerkey, p_sku, p_qty, 0, p_uom, p_packkey,
            p_lot, p_lottable01, p_lottable02, p_lottable03, p_lottable04, p_lottable05,
            '0', v_currentdatetime, v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
        );
        
        COMMIT;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspASNAddDetail');
        ROLLBACK;
END nspASNAddDetail;
/

-- nspReceiptCreate 存储过程 (创建收货单)
CREATE OR REPLACE PROCEDURE nspReceiptCreate (
    p_externreceiptkey  IN CHAR,
    p_storerkey         IN CHAR,
    p_asnkey            IN CHAR,
    p_receiptdate       IN DATE,
    p_receiptkey        OUT CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 检查外部收货键是否已存在
    SELECT COUNT(*) INTO v_cnt
    FROM RECEIPT
    WHERE ExternReceiptKey = p_externreceiptkey AND StorerKey = p_storerkey;
    
    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 81004;
        p_errmsg := 'NSQL81004: External receipt key already exists';
        RETURN;
    END IF;
    
    -- 获取收货键
    nspg_getkey('ReceiptKey', 10, p_receiptkey, p_success, p_err, p_errmsg);
    
    IF p_success = 1 THEN
        -- 创建收货头
        INSERT INTO RECEIPT (
            ReceiptKey, ExternReceiptKey, StorerKey, ASNKey,
            ReceiptDate, Status, Type,
            EffectiveDate, AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            p_receiptkey, p_externreceiptkey, p_storerkey, p_asnkey,
            p_receiptdate, '0', 'RECEIPT',
            v_currentdatetime, v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
        );
        
        -- 如果有ASN，从ASN创建收货明细
        IF TRIM(p_asnkey) IS NOT NULL THEN
            INSERT INTO RECEIPTDETAIL (
                ReceiptDetailKey, ReceiptKey, ReceiptLineNumber,
                StorerKey, Sku, Qty, QtyReceived, UOM, PackKey,
                Lot, Lottable01, Lottable02, Lottable03, Lottable04, Lottable05,
                Status, EffectiveDate, AddDate, AddWho, EditDate, EditWho
            )
            SELECT 
                'RD' || LPAD(TO_CHAR(SEQ_RECEIPTDETAIL.NEXTVAL), 8, '0'),
                p_receiptkey, ASNLineNumber,
                StorerKey, Sku, Qty, 0, UOM, PackKey,
                Lot, Lottable01, Lottable02, Lottable03, Lottable04, Lottable05,
                '0', v_currentdatetime, v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
            FROM ASNDETAIL
            WHERE ASNKey = p_asnkey;
        END IF;
        
        COMMIT;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspReceiptCreate');
        ROLLBACK;
END nspReceiptCreate;
/

-- 创建必要的序列
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_RECEIPTDETAIL';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_RECEIPTDETAIL START WITH 1 INCREMENT BY 1';
    END IF;
END;
/

-- nspReceiptConfirm 存储过程 (收货确认)
CREATE OR REPLACE PROCEDURE nspReceiptConfirm (
    p_receiptkey        IN CHAR,
    p_receiptlinenum    IN CHAR,
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_lot               IN CHAR,
    p_id                IN CHAR,
    p_loc               IN CHAR,
    p_qtyreceived       IN NUMBER,
    p_userkey           IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_packkey CHAR(10);
    v_expectedqty NUMBER;
    v_receivedqty NUMBER;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 验证收货明细是否存在
    BEGIN
        SELECT Qty, QtyReceived INTO v_expectedqty, v_receivedqty
        FROM RECEIPTDETAIL
        WHERE ReceiptKey = p_receiptkey
        AND ReceiptLineNumber = p_receiptlinenum
        AND StorerKey = p_storerkey
        AND Sku = p_sku;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 81005;
            p_errmsg := 'NSQL81005: Receipt detail not found';
            RETURN;
    END;

    -- 检查接收数量是否超过预期
    IF (v_receivedqty + p_qtyreceived) > v_expectedqty THEN
        p_success := 0;
        p_err := 81006;
        p_errmsg := 'NSQL81006: Received quantity exceeds expected quantity';
        RETURN;
    END IF;

    -- 获取包装信息
    nspGetPack(p_storerkey, p_sku, p_lot, p_loc, p_id, v_packkey, p_success, p_err, p_errmsg);

    IF p_success = 1 THEN
        -- 更新收货明细
        UPDATE RECEIPTDETAIL
        SET QtyReceived = QtyReceived + p_qtyreceived,
            Status = CASE WHEN QtyReceived + p_qtyreceived >= Qty THEN '9' ELSE Status END,
            EditDate = v_currentdatetime,
            EditWho = v_currentuser
        WHERE ReceiptKey = p_receiptkey
        AND ReceiptLineNumber = p_receiptlinenum;

        -- 执行入库事务
        nspItrnAddDeposit(
            NULL, p_storerkey, p_sku, p_lot, p_loc, p_id, '',
            '', '', '', NULL, NULL,
            0, 0, p_qtyreceived, 0, 0, 0, 0,
            v_packkey, 'EA', 'nspReceiptConfirm', p_receiptkey,
            p_success, p_err, p_errmsg
        );

        IF p_success = 1 THEN
            -- 检查收货是否完成
            DECLARE
                v_cnt NUMBER;
            BEGIN
                SELECT COUNT(*) INTO v_cnt
                FROM RECEIPTDETAIL
                WHERE ReceiptKey = p_receiptkey AND Status != '9';

                IF v_cnt = 0 THEN
                    UPDATE RECEIPT
                    SET Status = '9', -- 完成
                        EditDate = v_currentdatetime,
                        EditWho = v_currentuser
                    WHERE ReceiptKey = p_receiptkey;
                END IF;
            END;

            COMMIT;
        ELSE
            ROLLBACK;
        END IF;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspReceiptConfirm');
        ROLLBACK;
END nspReceiptConfirm;
/

-- =============================================
-- 上架策略存储过程
-- =============================================

-- nspPutawayStrategyExecute 存储过程 (执行上架策略)
CREATE OR REPLACE PROCEDURE nspPutawayStrategyExecute (
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_lot               IN CHAR,
    p_id                IN CHAR,
    p_fromloc           IN CHAR,
    p_qty               IN NUMBER,
    p_toloc             OUT CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_strategykey CHAR(10);
    v_putawaystrategykey CHAR(10);
    v_found NUMBER := 0;

    -- 游标定义
    CURSOR strategy_cursor IS
        SELECT psd.TOLOC, psd.LOCTYPE, psd.PUTAWAYZONE, psd.MAXQTY
        FROM PutawayStrategyDetail psd
        WHERE psd.PutawayStrategyKey = v_putawaystrategykey
        ORDER BY psd.PutawayStrategyLineNumber;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_toloc := ' ';

    -- 获取SKU的策略
    BEGIN
        SELECT StrategyKey INTO v_strategykey
        FROM SKU
        WHERE StorerKey = p_storerkey AND Sku = p_sku;

        SELECT PutawayStrategyKey INTO v_putawaystrategykey
        FROM Strategy
        WHERE StrategyKey = v_strategykey;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            v_putawaystrategykey := 'DEFAULT';
    END;

    -- 执行上架策略
    FOR strategy_rec IN strategy_cursor LOOP
        -- 检查位置类型和可用性
        IF strategy_rec.LOCTYPE = 'RESERVE' THEN
            -- 查找储备位置
            BEGIN
                SELECT Loc INTO p_toloc
                FROM LOC
                WHERE LocationType = 'RESERVE'
                AND Status = 'OK'
                AND ROWNUM = 1;

                v_found := 1;
                EXIT;
            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    CONTINUE;
            END;
        ELSIF strategy_rec.LOCTYPE = 'PICK' THEN
            -- 查找拣选位置
            BEGIN
                SELECT l.Loc INTO p_toloc
                FROM LOC l
                LEFT JOIN LOTxLOCxID lli ON l.Loc = lli.Loc
                WHERE l.LocationType = 'PICK'
                AND l.Status = 'OK'
                AND (lli.Qty IS NULL OR lli.Qty + p_qty <= strategy_rec.MAXQTY)
                AND ROWNUM = 1;

                v_found := 1;
                EXIT;
            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    CONTINUE;
            END;
        ELSIF TRIM(strategy_rec.TOLOC) != ' ' THEN
            -- 指定位置
            DECLARE
                v_currentqty NUMBER := 0;
            BEGIN
                SELECT NVL(SUM(Qty), 0) INTO v_currentqty
                FROM LOTxLOCxID
                WHERE Loc = strategy_rec.TOLOC;

                IF v_currentqty + p_qty <= strategy_rec.MAXQTY THEN
                    p_toloc := strategy_rec.TOLOC;
                    v_found := 1;
                    EXIT;
                END IF;
            END;
        END IF;
    END LOOP;

    -- 如果没有找到合适位置，使用默认位置
    IF v_found = 0 THEN
        p_toloc := 'UNKNOWN';
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspPutawayStrategyExecute');
END nspPutawayStrategyExecute;
/

-- nspPutawayTaskCreate 存储过程 (创建上架任务)
CREATE OR REPLACE PROCEDURE nspPutawayTaskCreate (
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_lot               IN CHAR,
    p_id                IN CHAR,
    p_fromloc           IN CHAR,
    p_qty               IN NUMBER,
    p_userkey           IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_toloc CHAR(10);
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 执行上架策略获取目标位置
    nspPutawayStrategyExecute(
        p_storerkey, p_sku, p_lot, p_id, p_fromloc, p_qty,
        v_toloc, p_success, p_err, p_errmsg
    );

    IF p_success = 1 THEN
        -- 创建上架任务
        INSERT INTO RFPUTAWAY (
            StorerKey, Sku, Lot, Id, FromLoc, ToLoc, Qty, Status, UserKey,
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            p_storerkey, p_sku, p_lot, p_id, p_fromloc, v_toloc, p_qty, '0', p_userkey,
            v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
        );

        COMMIT;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspPutawayTaskCreate');
        ROLLBACK;
END nspPutawayTaskCreate;
/

-- nspQualityCheck 存储过程 (质量检查)
CREATE OR REPLACE PROCEDURE nspQualityCheck (
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_lot               IN CHAR,
    p_id                IN CHAR,
    p_loc               IN CHAR,
    p_qty               IN NUMBER,
    p_qcresult          IN CHAR,
    p_qcreason          IN CHAR,
    p_userkey           IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_qckey CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 获取质检键
    nspg_getkey('QCKey', 10, v_qckey, p_success, p_err, p_errmsg);

    IF p_success = 1 THEN
        -- 记录质检结果
        INSERT INTO QUALITYCONTROL (
            QCKey, StorerKey, Sku, Lot, ID, Loc, Qty,
            QCResult, QCReason, QCUser, QCDate,
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            v_qckey, p_storerkey, p_sku, p_lot, p_id, p_loc, p_qty,
            p_qcresult, p_qcreason, p_userkey, v_currentdatetime,
            v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
        );

        -- 根据质检结果处理库存
        IF p_qcresult = 'PASS' THEN
            -- 质检通过，释放库存
            UPDATE LOTxLOCxID
            SET Status = 'OK',
                EditDate = v_currentdatetime,
                EditWho = v_currentuser
            WHERE StorerKey = p_storerkey AND Sku = p_sku AND Lot = p_lot
            AND ID = p_id AND Loc = p_loc;
        ELSIF p_qcresult = 'FAIL' THEN
            -- 质检失败，冻结库存
            nspInventoryHold(p_lot, p_loc, p_id, 'QC_FAIL', '1', p_success, p_err, p_errmsg);
        END IF;

        IF p_success = 1 THEN
            COMMIT;
        ELSE
            ROLLBACK;
        END IF;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspQualityCheck');
        ROLLBACK;
END nspQualityCheck;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle接收和上架管理存储过程脚本执行完成';
PROMPT '>>> 已创建完整的接收和上架管理存储过程集合 (7个)';
PROMPT '>>> 包含：ASN管理、收货确认、上架策略、质量检查等功能';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 接收和上架管理存储过程创建完成！';
PROMPT '总接收上架存储过程数量：7个专业存储过程';
PROMPT '功能覆盖：';
PROMPT '- ASN创建和管理';
PROMPT '- ASN明细处理';
PROMPT '- 收货单创建';
PROMPT '- 收货确认';
PROMPT '- 上架策略执行';
PROMPT '- 上架任务创建';
PROMPT '- 质量检查';
PROMPT '=============================================';
