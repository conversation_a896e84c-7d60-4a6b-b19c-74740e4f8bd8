-- =============================================
-- 创建视图和索引脚本
-- 功能：创建仓库管理系统的视图和性能优化索引
-- 用途：数据查询优化和业务视图
-- =============================================

-- =============================================
-- 第一部分：创建主键约束
-- =============================================

-- ITRN 表主键
IF NOT OBJECT_ID('PKITRN') IS NULL
BEGIN
PRINT '<<<Creating Primary Key PKITRN On Table ITRN>>>'
ALTER TABLE ITRN
ADD CONSTRAINT PKItrn PRIMARY KEY (ItrnKey)
END
GO

-- SKU 表主键
IF NOT OBJECT_ID('PKSKU') IS NULL
BEGIN
PRINT '<<<Creating Primary Key PKSKU On Table SKU>>>'
ALTER TABLE SKU
ADD CONSTRAINT PKSKU PRIMARY KEY (StorerKey, Sku)
END
GO

-- ID 表主键
IF NOT OBJECT_ID('PKID') IS NULL
BEGIN
PRINT '<<<Creating Primary Key PKID On Table ID>>>'
ALTER TABLE ID
ADD CONSTRAINT PKID PRIMARY KEY (Id)
END
GO

-- LOTATTRIBUTE 表主键
IF NOT OBJECT_ID('PKLOTATTRIBUTE') IS NULL
BEGIN
PRINT '<<<Creating Primary Key PKLOTATTRIBUTE On Table LOTATTRIBUTE>>>'
ALTER TABLE LOTATTRIBUTE
ADD CONSTRAINT PKLOTAttribute PRIMARY KEY (StorerKey, Sku, Lot)
END
GO

-- LOT 表主键
IF NOT OBJECT_ID('PKLOT') IS NULL
BEGIN
PRINT '<<<Creating Primary Key PKLOT On Table LOT>>>'
ALTER TABLE LOT
ADD CONSTRAINT PKLot PRIMARY KEY (Lot, StorerKey, Sku)
END
GO

-- LOC 表主键
IF NOT OBJECT_ID('PKLOC') IS NULL
BEGIN
PRINT '<<<Creating Primary Key PKLOC On Table LOC>>>'
ALTER TABLE LOC
ADD CONSTRAINT PKLOC PRIMARY KEY (Loc)
END
GO

-- LOTxLOCxID 表主键
IF NOT OBJECT_ID('PKLOTxLOCxID') IS NULL
BEGIN
PRINT '<<<Creating Primary Key PKLOTxLOCxID On Table LOTxLOCxID>>>'
ALTER TABLE LOTxLOCxID
ADD CONSTRAINT PKLOTxLOCxID PRIMARY KEY (Lot, Loc, Id, StorerKey, Sku)
END
GO

-- =============================================
-- 第二部分：创建外键约束
-- =============================================

-- LOT 表外键约束
IF NOT OBJECT_ID('FK_LOT_STORER_01') IS NULL
BEGIN
PRINT '<<<Creating Foreign Key FK_LOT_STORER_01 On Table LOT>>>'
ALTER TABLE LOT
ADD CONSTRAINT FK_LOT_STORER_01 
FOREIGN KEY (StorerKey) REFERENCES STORER(StorerKey)
END
GO

IF NOT OBJECT_ID('FK_LOT_SKU_01') IS NULL
BEGIN
PRINT '<<<Creating Foreign Key FK_LOT_SKU_01 On Table LOT>>>'
ALTER TABLE LOT
ADD CONSTRAINT FK_LOT_SKU_01 
FOREIGN KEY (StorerKey, Sku) REFERENCES SKU(StorerKey, Sku)
END
GO

-- LOTATTRIBUTE 表外键约束
IF NOT OBJECT_ID('FK_LOTATTRIBUTE_STORER_01') IS NULL
BEGIN
PRINT '<<<Creating Foreign Key FK_LOTATTRIBUTE_STORER_01 On Table LOTATTRIBUTE>>>'
ALTER TABLE LOTATTRIBUTE
ADD CONSTRAINT FK_LOTATTRIBUTE_STORER_01 
FOREIGN KEY (StorerKey) REFERENCES STORER(StorerKey)
END
GO

IF NOT OBJECT_ID('FK_LTATTR_SKU_01') IS NULL
BEGIN
PRINT '<<<Creating Foreign Key FK_LTATTR_SKU_01 On Table LOTATTRIBUTE>>>'
ALTER TABLE LOTATTRIBUTE
ADD CONSTRAINT FK_LTATTR_SKU_01 
FOREIGN KEY (StorerKey, Sku) REFERENCES SKU(StorerKey, Sku)
END
GO

-- LOTxLOCxID 表外键约束
IF NOT OBJECT_ID('FK_LOTxLOCxID_LOT_01') IS NULL
BEGIN
PRINT '<<<Creating Foreign Key FK_LOTxLOCxID_LOT_01 On Table LOTxLOCxID>>>'
ALTER TABLE LOTxLOCxID
ADD CONSTRAINT FK_LOTxLOCxID_LOT_01 
FOREIGN KEY (Lot, StorerKey, Sku) REFERENCES LOT(Lot, StorerKey, Sku)
END
GO

IF NOT OBJECT_ID('FK_LOTxLOCxID_LOC_01') IS NULL
BEGIN
PRINT '<<<Creating Foreign Key FK_LOTxLOCxID_LOC_01 On Table LOTxLOCxID>>>'
ALTER TABLE LOTxLOCxID
ADD CONSTRAINT FK_LOTxLOCxID_LOC_01 
FOREIGN KEY (Loc) REFERENCES LOC(Loc)
END
GO

IF NOT OBJECT_ID('FK_LOTxLOCxID_ID_01') IS NULL
BEGIN
PRINT '<<<Creating Foreign Key FK_LOTxLOCxID_ID_01 On Table LOTxLOCxID>>>'
ALTER TABLE LOTxLOCxID
ADD CONSTRAINT FK_LOTxLOCxID_ID_01 
FOREIGN KEY (Id) REFERENCES ID(Id)
END
GO

-- =============================================
-- 第三部分：创建性能优化索引
-- =============================================

-- LOC 表索引
IF NOT OBJECT_ID('IDX_LOC_PUTAWAYZONE') IS NULL
BEGIN
PRINT '<<<Creating Index IDX_LOC_PUTAWAYZONE On Table LOC>>>'
CREATE INDEX IDX_LOC_PUTAWAYZONE ON LOC(PutawayZone)
END
GO

IF NOT OBJECT_ID('IDX_LOC_LOGICALLOCATION') IS NULL
BEGIN
PRINT '<<<Creating Index IDX_LOC_LOGICALLOCATION On Table LOC>>>'
CREATE INDEX IDX_LOC_LOGICALLOCATION ON LOC(LogicalLocation)
END
GO

-- SKU 表索引
IF NOT OBJECT_ID('IDX_SKU_SKU') IS NULL
BEGIN
PRINT '<<<Creating Index IDX_SKU_SKU On Table SKU>>>'
CREATE INDEX IDX_SKU_SKU ON SKU(Sku)
END
GO

-- LOTxLOCxID 表索引
IF NOT OBJECT_ID('IDX_LOTxLOCxID_LOC') IS NULL
BEGIN
PRINT '<<<Creating Index IDX_LOTxLOCxID_LOC On Table LOTxLOCxID>>>'
CREATE INDEX IDX_LOTxLOCxID_LOC ON LOTxLOCxID(Loc)
END
GO

IF NOT OBJECT_ID('IDX_LOTxLOCxID_SKU') IS NULL
BEGIN
PRINT '<<<Creating Index IDX_LOTxLOCxID_SKU On Table LOTxLOCxID>>>'
CREATE INDEX IDX_LOTxLOCxID_SKU ON LOTxLOCxID(StorerKey, Sku)
END
GO

IF NOT OBJECT_ID('IDX_LOTxLOCxID_ID') IS NULL
BEGIN
PRINT '<<<Creating Index IDX_LOTxLOCxID_ID On Table LOTxLOCxID>>>'
CREATE INDEX IDX_LOTxLOCxID_ID ON LOTxLOCxID(Id)
END
GO

-- =============================================
-- 第四部分：创建业务视图
-- =============================================

-- 创建 LOTxID 视图
IF OBJECT_ID('LOTxID') IS NOT NULL
DROP VIEW LOTxID
GO

CREATE VIEW LOTxID
( Lot,
Id,
Qty
) AS
SELECT    LOTxLOCxID.Lot,
LOTxLOCxID.Id,
Sum(Qty)
FROM LOTxLOCxID
GROUP BY  LOTxLOCxID.Lot,
LOTxLOCxID.Id
HAVING Sum(Qty) > 0
GO

IF OBJECT_ID('LOTxID') IS NULL
BEGIN
PRINT '<<<CREATION OF VIEW LOTxID FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED VIEW LOTxID>>>'
GRANT INSERT ON LOTxID TO nsql
GRANT UPDATE ON LOTxID TO nsql
GRANT DELETE ON LOTxID TO nsql
GRANT SELECT ON LOTxID TO nsql
END
GO

-- 创建 LOTxLOC 视图
IF OBJECT_ID('LOTxLOC') IS NOT NULL
DROP VIEW LOTxLOC
GO

CREATE VIEW LOTxLOC
( Lot,
Loc,
Storerkey,
Sku,
Qty
) AS
SELECT    LOTxLOCxID.Lot,
LOTxLOCxID.Loc,
LOTxLOCxID.Storerkey,
LOTxLOCxID.Sku,
Sum(Qty)
FROM LOTxLOCxID
GROUP BY  LOTxLOCxID.Lot,
LOTxLOCxID.Loc,
LOTxLOCxID.Storerkey,
LOTxLOCxID.Sku
HAVING Sum(Qty) > 0
GO

IF OBJECT_ID('LOTxLOC') IS NULL
BEGIN
PRINT '<<<CREATION OF VIEW LOTxLOC FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED VIEW LOTxLOC>>>'
GRANT INSERT ON LOTxLOC TO nsql
GRANT UPDATE ON LOTxLOC TO nsql
GRANT DELETE ON LOTxLOC TO nsql
GRANT SELECT ON LOTxLOC TO nsql
END
GO

-- 创建 IDxLOC 视图
IF OBJECT_ID('IDxLOC') IS NOT NULL
DROP VIEW IDxLOC
GO

CREATE VIEW IDxLOC
( Loc,
Id,
Qty
) AS
SELECT    LOTxLOCxID.Loc,
LOTxLOCxID.Id,
Sum(Qty)
FROM LOTxLOCxID
GROUP BY  LOTxLOCxID.Loc,
LOTxLOCxID.Id
HAVING Sum(Qty) > 0
GO

IF OBJECT_ID('IDxLOC') IS NULL
BEGIN
PRINT '<<<CREATION OF VIEW IDxLOC FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED VIEW IDxLOC>>>'
GRANT INSERT ON IDxLOC TO nsql
GRANT UPDATE ON IDxLOC TO nsql
GRANT DELETE ON IDxLOC TO nsql
GRANT SELECT ON IDxLOC TO nsql
END
GO

-- 注意：此脚本包含核心视图和索引
-- 更多视图和索引请参考原始文件或后续脚本
