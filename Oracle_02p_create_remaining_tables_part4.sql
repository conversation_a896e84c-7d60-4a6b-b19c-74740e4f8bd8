-- =============================================
-- Oracle版本 - 创建剩余表结构脚本 (第十六部分)
-- 功能：创建仓库管理系统中最后的重要表结构 (Oracle版本)
-- 用途：RF上架、服务管理、附加服务、消息管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- RF上架表
-- =============================================

-- RFPUTAWAY 表 (RF上架表)
CREATE TABLE RFPUTAWAY
(StorerKey                CHAR(15) NOT NULL,
Sku                      CHAR(20) NOT NULL,
Lot                      CHAR(10) NOT NULL,
ID                       CHAR(18) NOT NULL,
FromLoc                  CHAR(10) NOT NULL,
ToLoc                    CHAR(10) NOT NULL,
Qty                      NUMBER(10) NOT NULL
CONSTRAINT DF_RFPUTAWAY_Qty DEFAULT 0,
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_RFPUTAWAY_Status DEFAULT '0',
UserKey                  CHAR(18) NULL,
AddDate                  DATE NOT NULL
CONSTRAINT DF_RFPUTAWAY_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_RFPUTAWAY_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_RFPUTAWAY_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_RFPUTAWAY_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE RFPUTAWAY ADD CONSTRAINT PK_RFPUTAWAY 
PRIMARY KEY (StorerKey, Sku, Lot, ID, FromLoc, ToLoc);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON RFPUTAWAY TO nsql;

PROMPT '>>> 已创建表 RFPUTAWAY (RF上架表)';

-- =============================================
-- 服务管理表
-- =============================================

-- Services 表 (服务表)
CREATE TABLE Services
(Servicekey              CHAR(10) NOT NULL,
Descrip                 CHAR(30) NOT NULL
CONSTRAINT DF_SERV_Descrip DEFAULT ' ',
AddDate                 DATE NOT NULL
CONSTRAINT DF_SERV_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_SERV_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_SERV_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_SERV_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE Services ADD CONSTRAINT PK_Services PRIMARY KEY (Servicekey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Services TO nsql;

PROMPT '>>> 已创建表 Services (服务表)';

-- Accessorial 表 (附加服务表)
CREATE TABLE Accessorial
(Accessorialkey              CHAR(10) NOT NULL,
Descrip                     CHAR(30) NOT NULL
CONSTRAINT DF_ACCS_Descrip DEFAULT ' ',
AddDate                     DATE NOT NULL
CONSTRAINT DF_ACCS_AddDate DEFAULT SYSDATE,
AddWho                      CHAR(18) NOT NULL
CONSTRAINT DF_ACCS_AddWho DEFAULT USER,
EditDate                    DATE NOT NULL
CONSTRAINT DF_ACCS_EditDate DEFAULT SYSDATE,
EditWho                     CHAR(18) NOT NULL
CONSTRAINT DF_ACCS_EditWho DEFAULT USER,
TrafficCop                  CHAR(1) NULL,
ArchiveCop                  CHAR(1) NULL
);

-- 添加主键
ALTER TABLE Accessorial ADD CONSTRAINT PK_Accessorial PRIMARY KEY (Accessorialkey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Accessorial TO nsql;

PROMPT '>>> 已创建表 Accessorial (附加服务表)';

-- AccessorialDetail 表 (附加服务明细表)
CREATE TABLE AccessorialDetail
(Accessorialkey              CHAR(10) NOT NULL,
AccessorialDetailkey        CHAR(10) NOT NULL
CONSTRAINT DF_AccDetKey DEFAULT ' ',
Descrip                     CHAR(30) NOT NULL
CONSTRAINT DF_AccDet_Descrip DEFAULT ' ',
Rate                        NUMBER(12,6) NOT NULL
CONSTRAINT DF_AccDet_Rate DEFAULT 0,
UOM                         CHAR(10) NOT NULL
CONSTRAINT DF_AccDet_UOM DEFAULT ' ',
AddDate                     DATE NOT NULL
CONSTRAINT DF_AccDet_AddDate DEFAULT SYSDATE,
AddWho                      CHAR(18) NOT NULL
CONSTRAINT DF_AccDet_AddWho DEFAULT USER,
EditDate                    DATE NOT NULL
CONSTRAINT DF_AccDet_EditDate DEFAULT SYSDATE,
EditWho                     CHAR(18) NOT NULL
CONSTRAINT DF_AccDet_EditWho DEFAULT USER,
TrafficCop                  CHAR(1) NULL,
ArchiveCop                  CHAR(1) NULL
);

-- 添加主键
ALTER TABLE AccessorialDetail ADD CONSTRAINT PK_AccessorialDetail 
PRIMARY KEY (Accessorialkey, AccessorialDetailkey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON AccessorialDetail TO nsql;

PROMPT '>>> 已创建表 AccessorialDetail (附加服务明细表)';

-- =============================================
-- 消息管理表
-- =============================================

-- MESSAGE_ID 表 (消息ID表)
CREATE TABLE MESSAGE_ID
(MsgId                   CHAR(40) NOT NULL
CONSTRAINT DF_MESSAGE_ID_MSgId DEFAULT ' ',
MsgIcon                  CHAR(12) NOT NULL
CONSTRAINT DF_MESSAGE_ID_MsgIcon DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_MESSAGE_ID_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_MESSAGE_ID_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_MESSAGE_ID_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_MESSAGE_ID_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE MESSAGE_ID ADD CONSTRAINT PK_MESSAGE_ID PRIMARY KEY (MsgId);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON MESSAGE_ID TO nsql;

PROMPT '>>> 已创建表 MESSAGE_ID (消息ID表)';

-- MESSAGE_TEXT 表 (消息文本表)
CREATE TABLE MESSAGE_TEXT
(MsgId                   CHAR(40) NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_MsgId DEFAULT ' ',
MsgLangId                NUMBER NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_MsgLangId DEFAULT 0,
MsgText                  CLOB NULL,
AddDate                  DATE NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE MESSAGE_TEXT ADD CONSTRAINT PK_MESSAGE_TEXT PRIMARY KEY (MsgId, MsgLangId);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON MESSAGE_TEXT TO nsql;

PROMPT '>>> 已创建表 MESSAGE_TEXT (消息文本表)';

-- =============================================
-- 调度管理表
-- =============================================

-- TRIDENTSCHEDULER 表 (调度器表)
CREATE TABLE TRIDENTSCHEDULER
(TridentSchedulerKey CHAR(10) NOT NULL,
Hikey               CHAR(10) NULL,
ScheduleType        CHAR(10) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_ScheduleType DEFAULT ' ',
ScheduleTime        DATE NULL,
Status              CHAR(10) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_Status DEFAULT '0',
AddDate             DATE NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_AddDate DEFAULT SYSDATE,
AddWho              CHAR(18) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_AddWho DEFAULT USER,
EditDate            DATE NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_EditDate DEFAULT SYSDATE,
EditWho             CHAR(18) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_EditWho DEFAULT USER,
TrafficCop          CHAR(1) NULL,
ArchiveCop          CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TRIDENTSCHEDULER ADD CONSTRAINT PK_TRIDENTSCHEDULER PRIMARY KEY (TridentSchedulerKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TRIDENTSCHEDULER TO nsql;

PROMPT '>>> 已创建表 TRIDENTSCHEDULER (调度器表)';

-- =============================================
-- 预分配拣选明细表
-- =============================================

-- PreAllocatePickDetail 表 (预分配拣选明细表)
CREATE TABLE PreAllocatePickDetail
(PreAllocatePickDetailKey CHAR(10) NOT NULL
CONSTRAINT DF_PAPD_Key DEFAULT ' ',
OrderKey                 CHAR(10) NOT NULL
CONSTRAINT DF_PAPD_OrderKey DEFAULT ' ',
OrderLineNumber          CHAR(5) NOT NULL
CONSTRAINT DF_PAPD_OrderLineNumber DEFAULT ' ',
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PAPD_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_PAPD_Sku DEFAULT ' ',
Lot                      CHAR(10) NOT NULL
CONSTRAINT DF_PAPD_Lot DEFAULT ' ',
Loc                      CHAR(10) NOT NULL
CONSTRAINT DF_PAPD_Loc DEFAULT ' ',
ID                       CHAR(18) NOT NULL
CONSTRAINT DF_PAPD_ID DEFAULT ' ',
Qty                      NUMBER(10) NOT NULL
CONSTRAINT DF_PAPD_Qty DEFAULT 0,
AddDate                  DATE NOT NULL
CONSTRAINT DF_PAPD_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PAPD_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PAPD_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PAPD_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PreAllocatePickDetail ADD CONSTRAINT PK_PreAllocatePickDetail 
PRIMARY KEY (PreAllocatePickDetailKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PreAllocatePickDetail TO nsql;

PROMPT '>>> 已创建表 PreAllocatePickDetail (预分配拣选明细表)';

-- 提交事务
COMMIT;

PROMPT '>>> 剩余表结构脚本第四部分执行完成';
PROMPT '>>> 已创建RF上架、服务管理、消息管理、调度管理表 (8张表)';
PROMPT '>>> 所有剩余表结构创建完成！';
