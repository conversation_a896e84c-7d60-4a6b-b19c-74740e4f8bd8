-- =============================================
-- Oracle版本 - 创建剩余触发器脚本 (第八部分)
-- 功能：创建仓库管理系统的剩余触发器集合 (Oracle版本)
-- 用途：XDOCK、PREALLOCATE、WAVE、PUTAWAY、SECTION、LOTxIDDETAIL等剩余触发器
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中的108个SQL Server触发器转换为Oracle语法 (剩余部分)
-- 包含：交叉转运、预分配、波次、上架区域、LOT ID明细等触发器
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- XDOCK管理触发器 (从SQL Server转换)
-- =============================================

-- XDOCK表的插入触发器 (从ntrXdockHeaderAdd转换)
CREATE OR REPLACE TRIGGER NTR_XDOCK_ADD
BEFORE INSERT ON XDOCK
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成交叉转运键如果为空
    IF :NEW.XdockKey IS NULL OR TRIM(:NEW.XdockKey) = ' ' THEN
        SELECT 'XD' || LPAD(TO_CHAR(SEQ_XDOCK.NEXTVAL), 8, '0') INTO :NEW.XdockKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_XDOCK_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_XDOCK_ADD (交叉转运插入触发器)';

-- XDOCK表的更新触发器 (从ntrXdockHeaderUpdate转换)
CREATE OR REPLACE TRIGGER NTR_XDOCK_UPDATE
BEFORE UPDATE ON XDOCK
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_XDOCK_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_XDOCK_UPDATE (交叉转运更新触发器)';

-- XDOCK表的删除触发器 (从ntrXDockHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_XDOCK_DELETE
BEFORE DELETE ON XDOCK
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有交叉转运明细
    SELECT COUNT(*) INTO v_cnt
    FROM XDOCKDETAIL
    WHERE XdockKey = :OLD.XdockKey;
    
    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20025, 'Cannot delete xdock with existing xdock details');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_XDOCK_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_XDOCK_DELETE (交叉转运删除触发器)';

-- XDOCKDETAIL表的插入触发器 (从ntrXDockDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_XDOCKDETAIL_ADD
BEFORE INSERT ON XDOCKDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成交叉转运明细键如果为空
    IF :NEW.XdockDetailKey IS NULL OR TRIM(:NEW.XdockDetailKey) = ' ' THEN
        SELECT 'XDD' || LPAD(TO_CHAR(SEQ_XDOCKDETAIL.NEXTVAL), 7, '0') INTO :NEW.XdockDetailKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_XDOCKDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_XDOCKDETAIL_ADD (交叉转运明细插入触发器)';

-- XDOCKDETAIL表的更新触发器 (从ntrXDockDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_XDOCKDETAIL_UPDATE
BEFORE UPDATE ON XDOCKDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_XDOCKDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_XDOCKDETAIL_UPDATE (交叉转运明细更新触发器)';

-- XDOCKDETAIL表的删除触发器 (从ntrXDockDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_XDOCKDETAIL_DELETE
AFTER DELETE ON XDOCKDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'XDOCKDETAIL',
        'RECORD_DELETE',
        'Xdock Detail deleted: ' || :OLD.XdockKey || '/' || :OLD.XdockDetailKey,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_XDOCKDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_XDOCKDETAIL_DELETE (交叉转运明细删除触发器)';

-- =============================================
-- PREALLOCATE PICK DETAIL管理触发器 (从SQL Server转换)
-- =============================================

-- PREALLOCATEPICKDETAIL表的插入触发器 (从ntrPreAllocatePickDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_PREALLOCATEPICKDETAIL_ADD
BEFORE INSERT ON PREALLOCATEPICKDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成预分配拣选明细键如果为空
    IF :NEW.PreAllocatePickDetailKey IS NULL OR TRIM(:NEW.PreAllocatePickDetailKey) = ' ' THEN
        SELECT 'PAPD' || LPAD(TO_CHAR(SEQ_PREALLOCATEPICKDETAIL.NEXTVAL), 6, '0') INTO :NEW.PreAllocatePickDetailKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PREALLOCATEPICKDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PREALLOCATEPICKDETAIL_ADD (预分配拣选明细插入触发器)';

-- PREALLOCATEPICKDETAIL表的更新触发器 (从ntrPreAllocatePickDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_PREALLOCATEPICKDETAIL_UPD
BEFORE UPDATE ON PREALLOCATEPICKDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PREALLOCATEPICKDETAIL_UPD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PREALLOCATEPICKDETAIL_UPD (预分配拣选明细更新触发器)';

-- PREALLOCATEPICKDETAIL表的删除触发器 (从ntrPreAllocatePickDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_PREALLOCATEPICKDETAIL_DEL
AFTER DELETE ON PREALLOCATEPICKDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'PREALLOCATEPICKDETAIL',
        'RECORD_DELETE',
        'PreAllocate Pick Detail deleted: ' || :OLD.PreAllocatePickDetailKey,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PREALLOCATEPICKDETAIL_DEL');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PREALLOCATEPICKDETAIL_DEL (预分配拣选明细删除触发器)';

-- =============================================
-- WAVE管理触发器 (从SQL Server转换)
-- =============================================

-- WAVE表的删除触发器 (从ntrWaveHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_WAVE_DELETE
BEFORE DELETE ON WAVE
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有相关的拣选头
    SELECT COUNT(*) INTO v_cnt
    FROM PICKHEADER
    WHERE WaveKey = :OLD.WaveKey;

    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20026, 'Cannot delete wave with existing pick headers');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_WAVE_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_WAVE_DELETE (波次删除触发器)';

-- =============================================
-- PUTAWAY ZONE管理触发器 (从SQL Server转换)
-- =============================================

-- PUTAWAYZONE表的更新触发器 (从ntrPutawayZoneUpdate转换)
CREATE OR REPLACE TRIGGER NTR_PUTAWAYZONE_UPDATE
BEFORE UPDATE ON PUTAWAYZONE
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PUTAWAYZONE_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PUTAWAYZONE_UPDATE (上架区域更新触发器)';

-- PUTAWAYZONE表的删除触发器 (从ntrPutawayZoneDelete转换)
CREATE OR REPLACE TRIGGER NTR_PUTAWAYZONE_DELETE
AFTER DELETE ON PUTAWAYZONE
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'PUTAWAYZONE',
        'RECORD_DELETE',
        'Putaway Zone deleted: ' || :OLD.PutawayZone,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PUTAWAYZONE_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PUTAWAYZONE_DELETE (上架区域删除触发器)';

-- =============================================
-- SECTION管理触发器 (从SQL Server转换)
-- =============================================

-- SECTION表的更新触发器 (从ntrSectionUpdate转换)
CREATE OR REPLACE TRIGGER NTR_SECTION_UPDATE
BEFORE UPDATE ON SECTION
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_SECTION_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_SECTION_UPDATE (区段更新触发器)';

-- SECTION表的删除触发器 (从ntrSectionDelete转换)
CREATE OR REPLACE TRIGGER NTR_SECTION_DELETE
AFTER DELETE ON SECTION
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'SECTION',
        'RECORD_DELETE',
        'Section deleted: ' || :OLD.Section,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_SECTION_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_SECTION_DELETE (区段删除触发器)';

-- =============================================
-- PACK删除触发器 (从SQL Server转换)
-- =============================================

-- PACK表的删除触发器 (从ntrPackDelete转换)
CREATE OR REPLACE TRIGGER NTR_PACK_DELETE
BEFORE DELETE ON PACK
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有SKU使用此包装
    SELECT COUNT(*) INTO v_cnt
    FROM SKU
    WHERE PackKey = :OLD.PackKey;

    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20027, 'Cannot delete pack with existing SKUs');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PACK_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PACK_DELETE (包装删除触发器)';

-- =============================================
-- LOTxIDDETAIL管理触发器 (从SQL Server转换)
-- =============================================

-- LOTxIDDETAIL表的插入触发器 (从ntrLotxIDDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_LOTXIDDETAIL_ADD
BEFORE INSERT ON LOTXIDDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 生成LOTxID明细键如果为空
    IF :NEW.LotxIDDetailKey IS NULL OR TRIM(:NEW.LotxIDDetailKey) = ' ' THEN
        SELECT 'LXIDD' || LPAD(TO_CHAR(SEQ_LOTXIDDETAIL.NEXTVAL), 5, '0') INTO :NEW.LotxIDDetailKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOTXIDDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOTXIDDETAIL_ADD (LOTxID明细插入触发器)';

-- LOTxIDDETAIL表的更新触发器 (从ntrLotxIDDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_LOTXIDDETAIL_UPDATE
BEFORE UPDATE ON LOTXIDDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOTXIDDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOTXIDDETAIL_UPDATE (LOTxID明细更新触发器)';

-- LOTxIDDETAIL表的删除触发器 (从ntrLotxIDDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_LOTXIDDETAIL_DELETE
AFTER DELETE ON LOTXIDDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'LOTXIDDETAIL',
        'RECORD_DELETE',
        'LOTxID Detail deleted: ' || :OLD.LotxIDDetailKey,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOTXIDDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOTXIDDETAIL_DELETE (LOTxID明细删除触发器)';

-- =============================================
-- 创建必要的序列 (支持剩余触发器功能)
-- =============================================

-- 创建序列（如果不存在）
DECLARE
    v_count NUMBER;
    TYPE sequence_list_type IS TABLE OF VARCHAR2(30);
    sequences sequence_list_type := sequence_list_type(
        'SEQ_PALLET', 'SEQ_PALLETDETAIL', 'SEQ_CASEMANIFEST', 'SEQ_TRANSFERDETAIL',
        'SEQ_CONTAINERDETAIL', 'SEQ_MASTERAIRWAYBILL', 'SEQ_MASTERAIRWAYBILLDETAIL',
        'SEQ_HOUSEAIRWAYBILL', 'SEQ_HOUSEAIRWAYBILLDETAIL', 'SEQ_MBOL', 'SEQ_MBOLDETAIL',
        'SEQ_XDOCK', 'SEQ_XDOCKDETAIL', 'SEQ_PREALLOCATEPICKDETAIL', 'SEQ_LOTXIDDETAIL'
    );
BEGIN
    FOR i IN 1..sequences.COUNT LOOP
        SELECT COUNT(*) INTO v_count
        FROM USER_SEQUENCES
        WHERE SEQUENCE_NAME = sequences(i);

        IF v_count = 0 THEN
            EXECUTE IMMEDIATE 'CREATE SEQUENCE ' || sequences(i) || ' START WITH 1 INCREMENT BY 1';
            DBMS_OUTPUT.PUT_LINE('>>> 已创建序列 ' || sequences(i));
        END IF;
    END LOOP;
END;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle剩余触发器脚本执行完成';
PROMPT '>>> 已创建XDOCK、PREALLOCATE、WAVE、PUTAWAY、SECTION、PACK、LOTxIDDETAIL管理触发器 (18个)';
PROMPT '>>> 所有剩余触发器创建完成！';
