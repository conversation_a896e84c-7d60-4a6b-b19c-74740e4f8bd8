-- =============================================
-- 创建专业化表结构脚本 (第三部分)
-- 功能：创建仓库管理系统的专业化表结构
-- 用途：航空货运、海运、盘点、报表等专业模块
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- MASTERAIRWAYBILL 表 (主运单表)
-- =============================================
CREATE TABLE MASTERAIRWAYBILL
(MAWBKEY                  char(15) NOT NULL,
Status                   char(10) NOT NULL
CONSTRAINT DF_MAWB_Status DEFAULT "0"
CONSTRAINT CK_MAWB_Status CHECK ( Status LIKE '[0-9]' ),
ExternMAWBKey            char(30) NOT NULL
CONSTRAINT DF_MAWB_XMAWBKey DEFAULT "" ,
OriginCountry            char(30) NOT NULL
CONSTRAINT DF_MAWB_origincountry DEFAULT " ",
DestinationCountry       char(30) NOT NULL
CONSTRAINT DF_MAWB_destcountry DEFAULT " ",
CarrierKey               char(15) NOT NULL
CONSTRAINT DF_MAWB_CarrierKey DEFAULT "" ,
FlightNumber             char(18) NOT NULL
CONSTRAINT DF_MAWB_FlightNumber DEFAULT " ",
FlightDate               datetime NOT NULL
CONSTRAINT DF_MAWB_FlightDate DEFAULT CURRENT_TIMESTAMP ,
DepartureDate            datetime NOT NULL
CONSTRAINT DF_MAWB_Departure DEFAULT CURRENT_TIMESTAMP ,
ArrivalDate              datetime NOT NULL
CONSTRAINT DF_MAWB_Arrival DEFAULT CURRENT_TIMESTAMP ,
Currency                 char(10) NULL
CONSTRAINT DF_MAWB_Currency DEFAULT "USD" ,
TotalCharge              float NOT NULL
CONSTRAINT DF_MAWB_TotalCharge DEFAULT 0 ,
TotalPkgReceived         float NOT NULL
CONSTRAINT DF_MAWB_TotalPkgRcp DEFAULT 0 ,
TotalGrossWgt            float NOT NULL
CONSTRAINT DF_MAWB_TotalGrossWgt DEFAULT 0 ,
Notes                    text NULL ,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_MAWB_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_MAWB_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_MAWB_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_MAWB_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_MAWB_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
TimeStamp                timestamp NULL
)
GO

IF OBJECT_ID('MASTERAIRWAYBILL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE MASTERAIRWAYBILL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE MASTERAIRWAYBILL>>>'
GRANT INSERT ON MASTERAIRWAYBILL TO nsql
GRANT UPDATE ON MASTERAIRWAYBILL TO nsql
GRANT DELETE ON MASTERAIRWAYBILL TO nsql
GRANT SELECT ON MASTERAIRWAYBILL TO nsql
END
GO

-- =============================================
-- MASTERAIRWAYBILLDETAIL 表 (主运单明细表)
-- =============================================
CREATE TABLE MASTERAIRWAYBILLDETAIL
(MAWBKEY                  char(15) NOT NULL,
MAWBLineNumber           char(5) NOT NULL
CONSTRAINT DF_MAWBDET_MAWBLineNumber DEFAULT "" ,
HAWBKEY                  char(15) NOT NULL
CONSTRAINT DF_MWBDET_HAWBKey DEFAULT "" ,
NumberOfPieces           int NOT NULL
CONSTRAINT DF_MAWBDET_NumberOfPieces DEFAULT 1 ,
GrossWeight              float NOT NULL
CONSTRAINT DF_MAWBDET_GrossWeight DEFAULT 0 ,
UOMWeight                char(10) NOT NULL
CONSTRAINT DF_MAWBDET_UOMWeight DEFAULT "" ,
RateClass                char(10) NOT NULL
CONSTRAINT DF_MAWBDET_RateClass DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_MAWBDET_Sku DEFAULT "" ,
SkuDescription           char(45) NOT NULL
CONSTRAINT DF_MAWBDET_SkuDesc DEFAULT "" ,
ChargeableWeight         float NOT NULL
CONSTRAINT DF_MAWBDET_ChargeableWeight DEFAULT 0 ,
Rate                     float NOT NULL
CONSTRAINT DF_MAWBDET_Rate DEFAULT 0 ,
Extension                float NOT NULL   
CONSTRAINT DF_MAWBDET_Extension DEFAULT 0 ,
Notes                    text NULL ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_MAWBDET_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_MAWBDET_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_MAWBDET_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_MAWBDET_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
TimeStamp                timestamp NULL
)
GO

IF OBJECT_ID('MASTERAIRWAYBILLDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE MASTERAIRWAYBILLDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE MASTERAIRWAYBILLDETAIL>>>'
GRANT INSERT ON MASTERAIRWAYBILLDETAIL TO nsql
GRANT UPDATE ON MASTERAIRWAYBILLDETAIL TO nsql
GRANT DELETE ON MASTERAIRWAYBILLDETAIL TO nsql
GRANT SELECT ON MASTERAIRWAYBILLDETAIL TO nsql
END
GO

-- =============================================
-- HOUSEAIRWAYBILL 表 (分运单表)
-- =============================================
CREATE TABLE HOUSEAIRWAYBILL
(HAWBKEY                  char(15) NOT NULL ,
MAWBKEY                  char(15) NOT NULL
CONSTRAINT DF_HAWB_MAWBKEY DEFAULT "" ,
ConsigneeKey             char(15) NOT NULL
CONSTRAINT DF_HAWB_ConsigneeKey DEFAULT "" ,
C_Company                char(30) NULL,
C_Address1               char(30) NULL,
C_City                   char(30) NULL,
C_State                  char(2)  NULL,
C_Country                char(30) NULL,
C_Phone1                 char(18) NULL,
ShipperKey               char(15) NOT NULL
CONSTRAINT DF_HAWB_ShipperKey DEFAULT "" ,
S_Company                char(30) NULL,
S_Address1               char(30) NULL,
S_City                   char(30) NULL,
S_State                  char(2)  NULL,
S_Country                char(30) NULL,
S_Phone1                 char(18) NULL,
Currency                 char(10) NULL
CONSTRAINT DF_HAWB_Currency DEFAULT "USD" ,
TotalCharge              float NOT NULL
CONSTRAINT DF_HAWB_TotalCharge DEFAULT 0 ,
TotalPkgReceived         float NOT NULL
CONSTRAINT DF_HAWB_TotalPkgRcp DEFAULT 0 ,
TotalGrossWgt            float NOT NULL
CONSTRAINT DF_HAWB_TotalGrossWgt DEFAULT 0 ,
Notes                    text NULL ,
Status                   char(10) NOT NULL
CONSTRAINT DF_HAWB_Status DEFAULT "0"
CONSTRAINT CK_HAWB_Status CHECK ( Status LIKE '[0-9]' ),
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_HAWB_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_HAWB_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_HAWB_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_HAWB_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_HAWB_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
TimeStamp                timestamp NULL
)
GO

IF OBJECT_ID('HOUSEAIRWAYBILL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE HOUSEAIRWAYBILL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE HOUSEAIRWAYBILL>>>'
GRANT INSERT ON HOUSEAIRWAYBILL TO nsql
GRANT UPDATE ON HOUSEAIRWAYBILL TO nsql
GRANT DELETE ON HOUSEAIRWAYBILL TO nsql
GRANT SELECT ON HOUSEAIRWAYBILL TO nsql
END
GO

-- =============================================
-- HOUSEAIRWAYBILLDETAIL 表 (分运单明细表)
-- =============================================
CREATE TABLE HOUSEAIRWAYBILLDETAIL
(HAWBKEY                  char(15) NOT NULL,
HAWBLineNumber           char(5) NOT NULL
CONSTRAINT DF_HAWBDET_HAWBLineNumber DEFAULT "" ,
NumberOfPieces           int NOT NULL
CONSTRAINT DF_HAWBDET_NumberOfPieces DEFAULT 1 ,
GrossWeight              float NOT NULL
CONSTRAINT DF_HAWBDET_GrossWeight DEFAULT 0 ,
UOMWeight                char(10) NOT NULL
CONSTRAINT DF_HAWBDET_UOMWeight DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_HAWBDET_Sku DEFAULT "" ,
SkuDescription           char(45) NOT NULL
CONSTRAINT DF_HAWBDET_SkuDesc DEFAULT "" ,
Rate                     float NOT NULL
CONSTRAINT DF_HAWBDET_Rate DEFAULT 0 ,
Extension                float NOT NULL   
CONSTRAINT DF_HAWBDET_Extension DEFAULT 0 ,
Notes                    text NULL ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_HAWBDET_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_HAWBDET_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_HAWBDET_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_HAWBDET_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
TimeStamp                timestamp NULL
)
GO

IF OBJECT_ID('HOUSEAIRWAYBILLDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE HOUSEAIRWAYBILLDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE HOUSEAIRWAYBILLDETAIL>>>'
GRANT INSERT ON HOUSEAIRWAYBILLDETAIL TO nsql
GRANT UPDATE ON HOUSEAIRWAYBILLDETAIL TO nsql
GRANT DELETE ON HOUSEAIRWAYBILLDETAIL TO nsql
GRANT SELECT ON HOUSEAIRWAYBILLDETAIL TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- MBOL 表 (海运提单表)
-- =============================================
CREATE TABLE MBOL
(MbolKey                  char(10) NOT NULL,
Status                   char(10)
CONSTRAINT DF_MBOL_Status DEFAULT "0"
CONSTRAINT CK_MBOL_Status CHECK (Status IN ("0", "9")),
ExternMbolKey            char(30) NOT NULL
CONSTRAINT DF_MBOL_XMbolKey DEFAULT ""     ,
OriginCountry            char(30) NOT NULL
CONSTRAINT DF_MBOL_origincountry DEFAULT " ",
DestinationCountry       char(30) NOT NULL
CONSTRAINT DF_MBOL_destcountry DEFAULT " ",
Vessel                   char(30) NOT NULL
CONSTRAINT DF_MBOL_Vessel DEFAULT " ",
VoyageNumber             char(30) NOT NULL
CONSTRAINT DF_MBOL_VoyageNumber DEFAULT " " ,
DepartureDate            DateTime NOT NULL
CONSTRAINT DF_MBOL_Departure DEFAULT CURRENT_TIMESTAMP ,
ArrivalDate              DateTime NOT NULL
CONSTRAINT DF_MBOL_Arrival DEFAULT CURRENT_TIMESTAMP ,
CarrierKey               char(10) NOT NULL
CONSTRAINT DF_MBOL_CarrierKey DEFAULT " " ,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_MBOL_EffDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_MBOL_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_MBOL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_MBOL_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_MBOL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
TimeStamp                char(18) NULL
)
GO

IF OBJECT_ID('MBOL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE MBOL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE MBOL>>>'
GRANT INSERT ON MBOL TO nsql
GRANT UPDATE ON MBOL TO nsql
GRANT DELETE ON MBOL TO nsql
GRANT SELECT ON MBOL TO nsql
END
GO

-- =============================================
-- MBOLDETAIL 表 (海运提单明细表)
-- =============================================
CREATE TABLE MBOLDETAIL
(MbolKey                  char(10) NOT NULL,
MbolLineNumber           char(5) NOT NULL ,
ContainerKey             char(20) NOT NULL
CONSTRAINT DF_MBOLD_ContainerKey DEFAULT " " ,
OrderKey                 char(10) NOT NULL
CONSTRAINT DF_MBOLD_OrderKey DEFAULT " " ,
PalletKey                char(10) NOT NULL
CONSTRAINT DF_MBOLD_PalletKey DEFAULT " " ,
Description              char(30) NOT NULL
CONSTRAINT DF_MBOLD_Descript DEFAULT " " ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_MBOLD_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_MBOLD_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_MBOLD_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_MBOLD_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
TimeStamp                char(18) NULL
)
GO

IF OBJECT_ID('MBOLDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE MBOLDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE MBOLDETAIL>>>'
GRANT INSERT ON MBOLDETAIL TO nsql
GRANT UPDATE ON MBOLDETAIL TO nsql
GRANT DELETE ON MBOLDETAIL TO nsql
GRANT SELECT ON MBOLDETAIL TO nsql
END
GO

-- =============================================
-- TRANSMITLOG 表 (传输日志表)
-- =============================================
CREATE TABLE TRANSMITLOG
(transmitlogkey           char(10) NOT NULL ,
tablename                char(30) NOT NULL
CONSTRAINT DF_XMITLOG_Tablename DEFAULT "" ,
key1                     char(10) NOT NULL
CONSTRAINT DF_XMITLOG_Key1 DEFAULT "" ,
key2                     char(5) NOT NULL
CONSTRAINT DF_XMITLOG_Key2 DEFAULT "" ,
key3                     char(20) NOT NULL
CONSTRAINT DF_XMITLOG_Key3 DEFAULT "" ,
transmitflag             char(5) NOT NULL
CONSTRAINT DF_XMITLOG_transmitflag DEFAULT "0" ,
transmitbatch            char(10)NOT NULL
CONSTRAINT DF_XMITLOG_transmitbatch DEFAULT "" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_XMITLOG_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_XMITLOG_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_XMITLOG_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_XMITLOG_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL
)
GO

IF OBJECT_ID('TRANSMITLOG') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TRANSMITLOG FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TRANSMITLOG>>>'
GRANT INSERT ON TRANSMITLOG TO nsql
GRANT UPDATE ON TRANSMITLOG TO nsql
GRANT DELETE ON TRANSMITLOG TO nsql
GRANT SELECT ON TRANSMITLOG TO nsql
END
GO

-- =============================================
-- ARCHIVEPARAMETERS 表 (归档参数表)
-- =============================================
CREATE TABLE ARCHIVEPARAMETERS
(Archivekey              char(10)  NOT NULL ,
CopyRowsToArchiveDatabase char(1)   NOT NULL
CONSTRAINT DF_ARCTBL_c_row_to_arc_db DEFAULT "Y",
ArchiveDataBaseName      char(30)
CONSTRAINT DF_ARCTBL_arc_db_name DEFAULT "",
LiveDataBaseName         char(30)
CONSTRAINT DF_ARCTBL_live_db_name DEFAULT "",
ShipNumberofDaysToRetain int NOT NULL
CONSTRAINT DF_ARCTBL_SON_D_R DEFAULT 0,
ShipActive               char(2) NOT NULL
CONSTRAINT DF_ARCTBL_SOactive DEFAULT "Y" ,
ItrnNumberofDaysToRetain int NOT NULL
CONSTRAINT DF_ARCTBL_ItrnN_D_R DEFAULT 0,
ItrnActive               char(2) NOT NULL
CONSTRAINT DF_ARCTBL_Itrnactive DEFAULT "Y" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_ARCTBL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_ARCTBL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_ARCTBL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_ARCTBL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
Timestamp                Timestamp NOT NULL
)
GO

IF OBJECT_ID('ARCHIVEPARAMETERS') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ARCHIVEPARAMETERS FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ARCHIVEPARAMETERS>>>'
GRANT INSERT ON ARCHIVEPARAMETERS TO nsql
GRANT UPDATE ON ARCHIVEPARAMETERS TO nsql
GRANT DELETE ON ARCHIVEPARAMETERS TO nsql
GRANT SELECT ON ARCHIVEPARAMETERS TO nsql
END
GO

-- 注意：此脚本包含专业化表结构
-- 更多表结构将在后续部分添加
