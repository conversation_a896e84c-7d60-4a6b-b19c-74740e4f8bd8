# 仓库管理系统数据库脚本说明

## 概述

本文档说明了从 `NEPISQL.sql` 文件拆分出来的数据库脚本的功能和使用方法。这些脚本用于仓库管理系统 (WMS - Warehouse Management System) 的数据库重构。

## 脚本文件列表

### 1. 主控制脚本
- **00_master_deployment_script.sql** - 主部署脚本，协调执行所有子脚本

### 2. 功能脚本
- **01_cleanup_and_drop_objects.sql** - 清理和删除相关对象脚本
- **02_create_table_structures.sql** - 创建核心表结构脚本
- **02b_create_additional_tables.sql** - 创建附加表结构脚本
- **02c_create_specialized_tables.sql** - 创建专业化表结构脚本
- **02d_create_physical_and_report_tables.sql** - 创建盘点和报表表结构脚本
- **02e_create_missing_important_tables.sql** - 创建遗漏重要表结构脚本
- **02f_create_billing_and_financial_tables.sql** - 创建计费财务表结构脚本
- **02g_create_strategy_and_task_tables.sql** - 创建策略任务表结构脚本
- **02h_create_additional_specialized_tables.sql** - 创建附加专业表结构脚本
- **03_create_stored_procedures.sql** - 创建存储过程脚本
- **04_create_triggers.sql** - 创建触发器脚本
- **05_create_views_and_indexes.sql** - 创建视图和索引脚本
- **06_database_objects_statistics.sql** - 数据库对象统计脚本

## 脚本功能详解

### 01_cleanup_and_drop_objects.sql
**功能：** 清理和删除数据库对象
- 清理事务日志
- 删除外键约束
- 删除主键约束
- 删除索引
- 删除视图
- 删除表结构

**注意：** 这是破坏性操作，会删除所有相关数据！

### 02_create_table_structures.sql
**功能：** 创建核心表结构 (22张核心表)
- ITRN (库存事务表)
- SKU (库存单位表)
- ID (标识符表)
- LOTATTRIBUTE (批次属性表)
- LOT (批次表)
- LOC (位置表)
- LOTxLOCxID (批次位置标识关联表)
- STORER (存储商表)
- SKUxLOC (SKU位置关联表)
- ORDERS (订单表)
- ORDERDETAIL (订单明细表)
- PICKHEADER (拣选头表)
- PICKDETAIL (拣选明细表)
- CARTONIZATION (装箱表)
- WAVE (波次表)
- RECEIPT (收货表)
- RECEIPTDETAIL (收货明细表)
- PO (采购订单表)
- PODETAIL (采购订单明细表)
- PALLET (托盘表)
- PALLETDETAIL (托盘明细表)
- CONTAINER (容器表)
- CONTAINERDETAIL (容器明细表)
- ADJUSTMENT (调整表)
- ADJUSTMENTDETAIL (调整明细表)
- REPLENISHMENT (补货表)

### 02b_create_additional_tables.sql
**功能：** 创建附加表结构 (15张附加表)
- CASEMANIFEST (箱单表)
- PHYSICAL (盘点表)
- HOSTINTERFACE (主机接口表)
- CLPORDER (CLP订单表)
- CLPDETAIL (CLP订单明细表)
- STORERBILLING (存储商计费表)
- AccumulatedCharges (累计费用表)
- TaskDetail (任务明细表)
- TaskManagerUser (任务管理用户表)

### 02c_create_specialized_tables.sql
**功能：** 创建专业化表结构 (20张专业化表)
- MASTERAIRWAYBILL (主运单表)
- MASTERAIRWAYBILLDETAIL (主运单明细表)
- HOUSEAIRWAYBILL (分运单表)
- HOUSEAIRWAYBILLDETAIL (分运单明细表)
- MBOL (海运提单表)
- MBOLDETAIL (海运提单明细表)
- TRANSMITLOG (传输日志表)
- ARCHIVEPARAMETERS (归档参数表)

### 02d_create_physical_and_report_tables.sql
**功能：** 创建盘点和报表表结构 (25张专业表)
- PHY_A2B_ID (盘点A队B队ID对比表)
- PHY_A2B_LOT (盘点A队B队批次对比表)
- PHY_A2B_SKU (盘点A队B队SKU对比表)
- PHY_A2B_TAG (盘点A队B队标签对比表)
- PHY_missing_tag_a (盘点缺失标签A表)
- PHY_missing_tag_b (盘点缺失标签B表)
- PHY_POST_DETAIL (盘点过账明细表)
- PhysicalParameters (盘点参数表)
- pbsrpt_reports (报表定义表)
- pbsrpt_category (报表分类表)
- pbsrpt_parms (报表参数表)
- pbsrpt_sets (报表集合表)
- pbsrpt_set_reports (报表集合明细表)
- LOTxBILLDATE (批次计费日期表)
- FxRATE (汇率表)

### 03_create_stored_procedures.sql
**功能：** 创建业务逻辑存储过程
- nsp_logerror - 错误日志记录
- nspg_getkey - 键值生成
- nspLogAlert - 警报记录
- nspUOMCONV - UOM单位转换

### 04_create_triggers.sql
**功能：** 创建数据完整性触发器
- PALLET 表触发器 (插入/更新)
- PALLETDETAIL 表触发器 (插入/更新/删除)
- SKUxLOC 表触发器 (更新)

### 05_create_views_and_indexes.sql
**功能：** 创建视图和性能优化索引
- 主键约束创建
- 外键约束创建
- 性能优化索引
- 业务视图 (LOTxID, LOTxLOC, IDxLOC)

### 06_database_objects_statistics.sql
**功能：** 数据库对象统计和验证
- 统计所有表、视图、存储过程、触发器数量
- 验证核心业务表是否创建成功
- 生成详细的对象清单报告
- 检查数据库重构完成情况

## 执行顺序

**重要：** 必须按照以下顺序执行脚本：

1. `01_cleanup_and_drop_objects.sql` - 清理现有对象
2. `02_create_table_structures.sql` - 创建核心表结构
3. `02b_create_additional_tables.sql` - 创建附加表结构
4. `02c_create_specialized_tables.sql` - 创建专业化表结构
5. `02d_create_physical_and_report_tables.sql` - 创建盘点和报表表结构
6. `02e_create_missing_important_tables.sql` - 创建遗漏重要表结构
7. `02f_create_billing_and_financial_tables.sql` - 创建计费财务表结构
8. `02g_create_strategy_and_task_tables.sql` - 创建策略任务表结构
9. `02h_create_additional_specialized_tables.sql` - 创建附加专业表结构
10. `03_create_stored_procedures.sql` - 创建存储过程
11. `04_create_triggers.sql` - 创建触发器
12. `05_create_views_and_indexes.sql` - 创建视图和索引
13. `06_database_objects_statistics.sql` - 统计验证对象

## 使用方法

### 方法一：使用主控制脚本
```sql
-- 在 SQL Server Management Studio 中执行
:r 00_master_deployment_script.sql
```

### 方法二：逐个执行脚本
```sql
-- 1. 清理对象
:r 01_cleanup_and_drop_objects.sql

-- 2. 创建表结构
:r 02_create_table_structures.sql

-- 3. 创建存储过程
:r 03_create_stored_procedures.sql

-- 4. 创建触发器
:r 04_create_triggers.sql

-- 5. 创建视图和索引
:r 05_create_views_and_indexes.sql
```

### 方法三：使用 SQLCMD
```bash
sqlcmd -S server_name -d database_name -i 01_cleanup_and_drop_objects.sql
sqlcmd -S server_name -d database_name -i 02_create_table_structures.sql
sqlcmd -S server_name -d database_name -i 03_create_stored_procedures.sql
sqlcmd -S server_name -d database_name -i 04_create_triggers.sql
sqlcmd -S server_name -d database_name -i 05_create_views_and_indexes.sql
```

## 注意事项

### 执行前准备
1. **备份数据库** - 清理脚本会删除所有相关数据
2. **确认权限** - 确保有足够的数据库权限
3. **测试环境验证** - 建议先在测试环境执行
4. **检查依赖** - 确保没有其他应用正在使用数据库

### 执行过程中
1. **监控执行** - 观察脚本执行过程中的输出信息
2. **错误处理** - 如果出现错误，停止执行并检查问题
3. **事务管理** - 脚本包含事务管理，但建议在事务中执行整个过程

### 执行后验证
1. **检查对象** - 验证所有表、视图、存储过程、触发器是否创建成功
2. **数据完整性** - 检查约束和触发器是否正常工作
3. **性能测试** - 验证索引是否提升查询性能

## 故障排除

### 常见问题
1. **权限不足** - 确保用户有 DDL 权限
2. **对象已存在** - 检查是否有残留对象未清理
3. **依赖关系** - 确保按正确顺序执行脚本
4. **语法错误** - 检查 SQL Server 版本兼容性

### 调试方法
1. **单独执行** - 可以单独执行失败的脚本进行调试
2. **分段执行** - 将大脚本分段执行以定位问题
3. **查看日志** - 检查 SQL Server 错误日志

## 系统要求

- **数据库：** Microsoft SQL Server 2008 或更高版本
- **权限：** db_ddladmin 或 sysadmin 角色
- **空间：** 确保有足够的数据库空间

## 联系信息

如有问题或需要支持，请联系数据库管理员或开发团队。

---
**最后更新：** 2025-06-18  
**版本：** 1.0  
**来源：** 从 NEPISQL.sql 拆分
