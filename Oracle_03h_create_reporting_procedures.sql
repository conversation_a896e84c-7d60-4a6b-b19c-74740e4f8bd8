-- =============================================
-- Oracle版本 - 创建报表和统计存储过程脚本
-- 功能：创建仓库管理系统的报表和统计相关存储过程 (Oracle版本)
-- 用途：库存报表、活动报表、性能统计、数据分析等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中提取的报表和统计存储过程转换为Oracle语法
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 库存报表存储过程
-- =============================================

-- nspInventoryReport 存储过程 (库存报表)
CREATE OR REPLACE PROCEDURE nspInventoryReport (
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_loc               IN CHAR,
    p_asofdate          IN DATE,
    p_reporttype        IN CHAR
) IS
    v_asofdate DATE;
    v_totalqty NUMBER := 0;
    v_totalvalue NUMBER := 0;
    v_cnt NUMBER := 0;
BEGIN
    v_asofdate := NVL(p_asofdate, SYSDATE);
    
    DBMS_OUTPUT.PUT_LINE('=== INVENTORY REPORT ===');
    DBMS_OUTPUT.PUT_LINE('Report Date: ' || TO_CHAR(v_asofdate, 'YYYY-MM-DD HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('Report Type: ' || p_reporttype);
    DBMS_OUTPUT.PUT_LINE('');
    
    IF p_reporttype = 'SUMMARY' THEN
        -- 汇总报表
        DBMS_OUTPUT.PUT_LINE('StorerKey|Sku|TotalQty|AvailableQty|AllocatedQty|PickedQty');
        DBMS_OUTPUT.PUT_LINE('---------|---|--------|-----------|-----------|--------');
        
        FOR inv_rec IN (
            SELECT lli.StorerKey, lli.Sku, 
                   SUM(lli.Qty) AS TotalQty,
                   SUM(lli.Qty - lli.QtyAllocated - lli.QtyPicked) AS AvailableQty,
                   SUM(lli.QtyAllocated) AS AllocatedQty,
                   SUM(lli.QtyPicked) AS PickedQty
            FROM LOTxLOCxID lli
            WHERE (p_storerkey = ' ' OR lli.StorerKey = p_storerkey)
            AND (p_sku = ' ' OR lli.Sku = p_sku)
            AND (p_loc = ' ' OR lli.Loc = p_loc)
            AND lli.Qty > 0
            GROUP BY lli.StorerKey, lli.Sku
            ORDER BY lli.StorerKey, lli.Sku
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                inv_rec.StorerKey || '|' || inv_rec.Sku || '|' ||
                inv_rec.TotalQty || '|' || inv_rec.AvailableQty || '|' ||
                inv_rec.AllocatedQty || '|' || inv_rec.PickedQty
            );
            v_totalqty := v_totalqty + inv_rec.TotalQty;
            v_cnt := v_cnt + 1;
        END LOOP;
        
    ELSIF p_reporttype = 'DETAIL' THEN
        -- 详细报表
        DBMS_OUTPUT.PUT_LINE('StorerKey|Sku|Lot|Loc|ID|Qty|Available|Allocated|Picked');
        DBMS_OUTPUT.PUT_LINE('---------|---|---|---|--|---|---------|---------|------');
        
        FOR inv_rec IN (
            SELECT StorerKey, Sku, Lot, Loc, ID, Qty,
                   (Qty - QtyAllocated - QtyPicked) AS AvailableQty,
                   QtyAllocated, QtyPicked
            FROM LOTxLOCxID
            WHERE (p_storerkey = ' ' OR StorerKey = p_storerkey)
            AND (p_sku = ' ' OR Sku = p_sku)
            AND (p_loc = ' ' OR Loc = p_loc)
            AND Qty > 0
            ORDER BY StorerKey, Sku, Lot, Loc, ID
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                inv_rec.StorerKey || '|' || inv_rec.Sku || '|' || inv_rec.Lot || '|' ||
                inv_rec.Loc || '|' || inv_rec.ID || '|' || inv_rec.Qty || '|' ||
                inv_rec.AvailableQty || '|' || inv_rec.QtyAllocated || '|' || inv_rec.QtyPicked
            );
            v_totalqty := v_totalqty + inv_rec.Qty;
            v_cnt := v_cnt + 1;
        END LOOP;
        
    ELSIF p_reporttype = 'LOCATION' THEN
        -- 位置报表
        DBMS_OUTPUT.PUT_LINE('Loc|LocationType|TotalQty|SKUCount|Status');
        DBMS_OUTPUT.PUT_LINE('---|------------|--------|--------|------');
        
        FOR loc_rec IN (
            SELECT l.Loc, l.LocationType, l.Status,
                   NVL(SUM(lli.Qty), 0) AS TotalQty,
                   COUNT(DISTINCT lli.Sku) AS SKUCount
            FROM LOC l
            LEFT JOIN LOTxLOCxID lli ON l.Loc = lli.Loc
            WHERE (p_loc = ' ' OR l.Loc = p_loc)
            GROUP BY l.Loc, l.LocationType, l.Status
            ORDER BY l.Loc
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                loc_rec.Loc || '|' || loc_rec.LocationType || '|' ||
                loc_rec.TotalQty || '|' || loc_rec.SKUCount || '|' || loc_rec.Status
            );
            v_totalqty := v_totalqty + loc_rec.TotalQty;
            v_cnt := v_cnt + 1;
        END LOOP;
    END IF;
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== SUMMARY ===');
    DBMS_OUTPUT.PUT_LINE('Total Records: ' || v_cnt);
    DBMS_OUTPUT.PUT_LINE('Total Quantity: ' || v_totalqty);
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error in inventory report: ' || SQLERRM);
END nspInventoryReport;
/

-- nspActivityReport 存储过程 (活动报表)
CREATE OR REPLACE PROCEDURE nspActivityReport (
    p_storerkey         IN CHAR,
    p_fromdate          IN DATE,
    p_todate            IN DATE,
    p_trantype          IN CHAR
) IS
    v_fromdate DATE;
    v_todate DATE;
    v_totalqty NUMBER := 0;
    v_cnt NUMBER := 0;
BEGIN
    v_fromdate := NVL(p_fromdate, SYSDATE - 7);
    v_todate := NVL(p_todate, SYSDATE);
    
    DBMS_OUTPUT.PUT_LINE('=== ACTIVITY REPORT ===');
    DBMS_OUTPUT.PUT_LINE('From Date: ' || TO_CHAR(v_fromdate, 'YYYY-MM-DD'));
    DBMS_OUTPUT.PUT_LINE('To Date: ' || TO_CHAR(v_todate, 'YYYY-MM-DD'));
    DBMS_OUTPUT.PUT_LINE('Transaction Type: ' || NVL(p_trantype, 'ALL'));
    DBMS_OUTPUT.PUT_LINE('');
    
    DBMS_OUTPUT.PUT_LINE('Date|StorerKey|Sku|TranType|Qty|FromLoc|ToLoc|User');
    DBMS_OUTPUT.PUT_LINE('----|---------|---|--------|---|-------|-----|----');
    
    FOR activity_rec IN (
        SELECT TO_CHAR(EffectiveDate, 'YYYY-MM-DD') AS TranDate,
               StorerKey, Sku, TranType, Qty, FromLoc, ToLoc, AddWho
        FROM ITRN
        WHERE EffectiveDate BETWEEN v_fromdate AND v_todate
        AND (p_storerkey = ' ' OR StorerKey = p_storerkey)
        AND (p_trantype = ' ' OR TranType = p_trantype)
        ORDER BY EffectiveDate DESC, ItrnKey
    ) LOOP
        DBMS_OUTPUT.PUT_LINE(
            activity_rec.TranDate || '|' || activity_rec.StorerKey || '|' ||
            activity_rec.Sku || '|' || activity_rec.TranType || '|' ||
            activity_rec.Qty || '|' || NVL(activity_rec.FromLoc, ' ') || '|' ||
            NVL(activity_rec.ToLoc, ' ') || '|' || activity_rec.AddWho
        );
        v_totalqty := v_totalqty + activity_rec.Qty;
        v_cnt := v_cnt + 1;
    END LOOP;
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== SUMMARY ===');
    DBMS_OUTPUT.PUT_LINE('Total Transactions: ' || v_cnt);
    DBMS_OUTPUT.PUT_LINE('Total Quantity: ' || v_totalqty);
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error in activity report: ' || SQLERRM);
END nspActivityReport;
/

-- nspPerformanceReport 存储过程 (性能报表)
CREATE OR REPLACE PROCEDURE nspPerformanceReport (
    p_userkey           IN CHAR,
    p_fromdate          IN DATE,
    p_todate            IN DATE,
    p_reporttype        IN CHAR
) IS
    v_fromdate DATE;
    v_todate DATE;
    v_totalcount NUMBER := 0;
    v_cnt NUMBER := 0;
BEGIN
    v_fromdate := NVL(p_fromdate, SYSDATE - 7);
    v_todate := NVL(p_todate, SYSDATE);
    
    DBMS_OUTPUT.PUT_LINE('=== PERFORMANCE REPORT ===');
    DBMS_OUTPUT.PUT_LINE('From Date: ' || TO_CHAR(v_fromdate, 'YYYY-MM-DD'));
    DBMS_OUTPUT.PUT_LINE('To Date: ' || TO_CHAR(v_todate, 'YYYY-MM-DD'));
    DBMS_OUTPUT.PUT_LINE('Report Type: ' || p_reporttype);
    DBMS_OUTPUT.PUT_LINE('');
    
    IF p_reporttype = 'PICKING' THEN
        -- 拣选性能报表
        DBMS_OUTPUT.PUT_LINE('User|Date|PicksCompleted|TotalQty|AvgQtyPerPick');
        DBMS_OUTPUT.PUT_LINE('----|----|--------------|---------|-----------');
        
        FOR perf_rec IN (
            SELECT pd.EditWho AS UserKey,
                   TO_CHAR(pd.EditDate, 'YYYY-MM-DD') AS PickDate,
                   COUNT(*) AS PicksCompleted,
                   SUM(pd.QtyPicked) AS TotalQty,
                   ROUND(AVG(pd.QtyPicked), 2) AS AvgQtyPerPick
            FROM PICKDETAIL pd
            WHERE pd.EditDate BETWEEN v_fromdate AND v_todate
            AND pd.Status = '9' -- 完成
            AND (p_userkey = ' ' OR pd.EditWho = p_userkey)
            GROUP BY pd.EditWho, TO_CHAR(pd.EditDate, 'YYYY-MM-DD')
            ORDER BY pd.EditWho, TO_CHAR(pd.EditDate, 'YYYY-MM-DD')
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                perf_rec.UserKey || '|' || perf_rec.PickDate || '|' ||
                perf_rec.PicksCompleted || '|' || perf_rec.TotalQty || '|' ||
                perf_rec.AvgQtyPerPick
            );
            v_totalcount := v_totalcount + perf_rec.PicksCompleted;
            v_cnt := v_cnt + 1;
        END LOOP;
        
    ELSIF p_reporttype = 'RECEIVING' THEN
        -- 收货性能报表
        DBMS_OUTPUT.PUT_LINE('User|Date|ReceiptsCompleted|TotalQty|AvgQtyPerReceipt');
        DBMS_OUTPUT.PUT_LINE('----|----|-----------------|---------|--------------');
        
        FOR perf_rec IN (
            SELECT rd.EditWho AS UserKey,
                   TO_CHAR(rd.EditDate, 'YYYY-MM-DD') AS ReceiptDate,
                   COUNT(*) AS ReceiptsCompleted,
                   SUM(rd.QtyReceived) AS TotalQty,
                   ROUND(AVG(rd.QtyReceived), 2) AS AvgQtyPerReceipt
            FROM RECEIPTDETAIL rd
            WHERE rd.EditDate BETWEEN v_fromdate AND v_todate
            AND rd.Status = '9' -- 完成
            AND (p_userkey = ' ' OR rd.EditWho = p_userkey)
            GROUP BY rd.EditWho, TO_CHAR(rd.EditDate, 'YYYY-MM-DD')
            ORDER BY rd.EditWho, TO_CHAR(rd.EditDate, 'YYYY-MM-DD')
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                perf_rec.UserKey || '|' || perf_rec.ReceiptDate || '|' ||
                perf_rec.ReceiptsCompleted || '|' || perf_rec.TotalQty || '|' ||
                perf_rec.AvgQtyPerReceipt
            );
            v_totalcount := v_totalcount + perf_rec.ReceiptsCompleted;
            v_cnt := v_cnt + 1;
        END LOOP;
    END IF;
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== SUMMARY ===');
    DBMS_OUTPUT.PUT_LINE('Total Records: ' || v_cnt);
    DBMS_OUTPUT.PUT_LINE('Total Operations: ' || v_totalcount);
    
EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error in performance report: ' || SQLERRM);
END nspPerformanceReport;
/

-- =============================================
-- 统计分析存储过程
-- =============================================

-- nspInventoryAnalysis 存储过程 (库存分析)
CREATE OR REPLACE PROCEDURE nspInventoryAnalysis (
    p_storerkey         IN CHAR,
    p_analysistype      IN CHAR
) IS
    v_totalvalue NUMBER := 0;
    v_totalqty NUMBER := 0;
    v_cnt NUMBER := 0;
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== INVENTORY ANALYSIS ===');
    DBMS_OUTPUT.PUT_LINE('Analysis Type: ' || p_analysistype);
    DBMS_OUTPUT.PUT_LINE('StorerKey: ' || NVL(p_storerkey, 'ALL'));
    DBMS_OUTPUT.PUT_LINE('');

    IF p_analysistype = 'ABC' THEN
        -- ABC分析
        DBMS_OUTPUT.PUT_LINE('SKU|TotalQty|TotalValue|Percentage|Category');
        DBMS_OUTPUT.PUT_LINE('---|--------|----------|----------|--------');

        FOR abc_rec IN (
            SELECT lli.Sku,
                   SUM(lli.Qty) AS TotalQty,
                   SUM(lli.Qty * NVL(s.StandardCost, 0)) AS TotalValue,
                   CASE
                       WHEN ROW_NUMBER() OVER (ORDER BY SUM(lli.Qty * NVL(s.StandardCost, 0)) DESC) <=
                            ROUND(COUNT(*) OVER () * 0.2) THEN 'A'
                       WHEN ROW_NUMBER() OVER (ORDER BY SUM(lli.Qty * NVL(s.StandardCost, 0)) DESC) <=
                            ROUND(COUNT(*) OVER () * 0.5) THEN 'B'
                       ELSE 'C'
                   END AS Category
            FROM LOTxLOCxID lli
            JOIN SKU s ON lli.StorerKey = s.StorerKey AND lli.Sku = s.Sku
            WHERE (p_storerkey = ' ' OR lli.StorerKey = p_storerkey)
            AND lli.Qty > 0
            GROUP BY lli.Sku
            ORDER BY SUM(lli.Qty * NVL(s.StandardCost, 0)) DESC
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                abc_rec.Sku || '|' || abc_rec.TotalQty || '|' ||
                ROUND(abc_rec.TotalValue, 2) || '|' ||
                ROUND((abc_rec.TotalValue / NULLIF(v_totalvalue, 0)) * 100, 2) || '%|' ||
                abc_rec.Category
            );
            v_totalvalue := v_totalvalue + abc_rec.TotalValue;
            v_totalqty := v_totalqty + abc_rec.TotalQty;
            v_cnt := v_cnt + 1;
        END LOOP;

    ELSIF p_analysistype = 'AGING' THEN
        -- 库存老化分析
        DBMS_OUTPUT.PUT_LINE('Sku|Lot|DaysInStock|Qty|Status');
        DBMS_OUTPUT.PUT_LINE('---|---|-----------|---|------');

        FOR aging_rec IN (
            SELECT lli.Sku, lli.Lot, lli.Qty,
                   ROUND(SYSDATE - lli.AddDate) AS DaysInStock,
                   CASE
                       WHEN SYSDATE - lli.AddDate <= 30 THEN 'FRESH'
                       WHEN SYSDATE - lli.AddDate <= 90 THEN 'AGING'
                       ELSE 'OLD'
                   END AS Status
            FROM LOTxLOCxID lli
            WHERE (p_storerkey = ' ' OR lli.StorerKey = p_storerkey)
            AND lli.Qty > 0
            ORDER BY lli.AddDate
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                aging_rec.Sku || '|' || aging_rec.Lot || '|' ||
                aging_rec.DaysInStock || '|' || aging_rec.Qty || '|' ||
                aging_rec.Status
            );
            v_totalqty := v_totalqty + aging_rec.Qty;
            v_cnt := v_cnt + 1;
        END LOOP;

    ELSIF p_analysistype = 'TURNOVER' THEN
        -- 库存周转分析
        DBMS_OUTPUT.PUT_LINE('Sku|AvgInventory|MonthlyShipments|TurnoverRate');
        DBMS_OUTPUT.PUT_LINE('---|------------|---------------|------------');

        FOR turnover_rec IN (
            SELECT lli.Sku,
                   AVG(lli.Qty) AS AvgInventory,
                   NVL(shipped.MonthlyQty, 0) AS MonthlyShipments,
                   CASE
                       WHEN AVG(lli.Qty) > 0 THEN ROUND(NVL(shipped.MonthlyQty, 0) / AVG(lli.Qty), 2)
                       ELSE 0
                   END AS TurnoverRate
            FROM LOTxLOCxID lli
            LEFT JOIN (
                SELECT i.Sku, AVG(i.Qty) AS MonthlyQty
                FROM ITRN i
                WHERE i.TranType = 'AW'
                AND i.EffectiveDate >= ADD_MONTHS(SYSDATE, -12)
                GROUP BY i.Sku
            ) shipped ON lli.Sku = shipped.Sku
            WHERE (p_storerkey = ' ' OR lli.StorerKey = p_storerkey)
            AND lli.Qty > 0
            GROUP BY lli.Sku, shipped.MonthlyQty
            ORDER BY TurnoverRate DESC
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                turnover_rec.Sku || '|' || ROUND(turnover_rec.AvgInventory, 2) || '|' ||
                turnover_rec.MonthlyShipments || '|' || turnover_rec.TurnoverRate
            );
            v_cnt := v_cnt + 1;
        END LOOP;
    END IF;

    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== SUMMARY ===');
    DBMS_OUTPUT.PUT_LINE('Total Records: ' || v_cnt);
    IF p_analysistype = 'ABC' THEN
        DBMS_OUTPUT.PUT_LINE('Total Value: ' || ROUND(v_totalvalue, 2));
        DBMS_OUTPUT.PUT_LINE('Total Quantity: ' || v_totalqty);
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error in inventory analysis: ' || SQLERRM);
END nspInventoryAnalysis;
/

-- nspOrderAnalysis 存储过程 (订单分析)
CREATE OR REPLACE PROCEDURE nspOrderAnalysis (
    p_storerkey         IN CHAR,
    p_fromdate          IN DATE,
    p_todate            IN DATE,
    p_analysistype      IN CHAR
) IS
    v_fromdate DATE;
    v_todate DATE;
    v_totalorders NUMBER := 0;
    v_totalvalue NUMBER := 0;
    v_cnt NUMBER := 0;
BEGIN
    v_fromdate := NVL(p_fromdate, SYSDATE - 30);
    v_todate := NVL(p_todate, SYSDATE);

    DBMS_OUTPUT.PUT_LINE('=== ORDER ANALYSIS ===');
    DBMS_OUTPUT.PUT_LINE('From Date: ' || TO_CHAR(v_fromdate, 'YYYY-MM-DD'));
    DBMS_OUTPUT.PUT_LINE('To Date: ' || TO_CHAR(v_todate, 'YYYY-MM-DD'));
    DBMS_OUTPUT.PUT_LINE('Analysis Type: ' || p_analysistype);
    DBMS_OUTPUT.PUT_LINE('');

    IF p_analysistype = 'DAILY' THEN
        -- 每日订单分析
        DBMS_OUTPUT.PUT_LINE('Date|OrderCount|TotalLines|AvgLinesPerOrder');
        DBMS_OUTPUT.PUT_LINE('----|----------|----------|---------------');

        FOR daily_rec IN (
            SELECT TO_CHAR(o.OrderDate, 'YYYY-MM-DD') AS OrderDate,
                   COUNT(DISTINCT o.OrderKey) AS OrderCount,
                   COUNT(od.OrderDetailKey) AS TotalLines,
                   ROUND(COUNT(od.OrderDetailKey) / COUNT(DISTINCT o.OrderKey), 2) AS AvgLinesPerOrder
            FROM ORDERS o
            JOIN ORDERDETAIL od ON o.OrderKey = od.OrderKey
            WHERE o.OrderDate BETWEEN v_fromdate AND v_todate
            AND (p_storerkey = ' ' OR o.StorerKey = p_storerkey)
            GROUP BY TO_CHAR(o.OrderDate, 'YYYY-MM-DD')
            ORDER BY TO_CHAR(o.OrderDate, 'YYYY-MM-DD')
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                daily_rec.OrderDate || '|' || daily_rec.OrderCount || '|' ||
                daily_rec.TotalLines || '|' || daily_rec.AvgLinesPerOrder
            );
            v_totalorders := v_totalorders + daily_rec.OrderCount;
            v_cnt := v_cnt + 1;
        END LOOP;

    ELSIF p_analysistype = 'STATUS' THEN
        -- 订单状态分析
        DBMS_OUTPUT.PUT_LINE('Status|OrderCount|Percentage');
        DBMS_OUTPUT.PUT_LINE('------|----------|----------');

        FOR status_rec IN (
            SELECT o.Status,
                   COUNT(*) AS OrderCount,
                   ROUND((COUNT(*) * 100.0 / SUM(COUNT(*)) OVER ()), 2) AS Percentage
            FROM ORDERS o
            WHERE o.OrderDate BETWEEN v_fromdate AND v_todate
            AND (p_storerkey = ' ' OR o.StorerKey = p_storerkey)
            GROUP BY o.Status
            ORDER BY COUNT(*) DESC
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                status_rec.Status || '|' || status_rec.OrderCount || '|' ||
                status_rec.Percentage || '%'
            );
            v_totalorders := v_totalorders + status_rec.OrderCount;
            v_cnt := v_cnt + 1;
        END LOOP;

    ELSIF p_analysistype = 'CUSTOMER' THEN
        -- 客户订单分析
        DBMS_OUTPUT.PUT_LINE('ConsigneeKey|OrderCount|TotalQty|AvgQtyPerOrder');
        DBMS_OUTPUT.PUT_LINE('------------|----------|--------|-------------');

        FOR customer_rec IN (
            SELECT o.ConsigneeKey,
                   COUNT(DISTINCT o.OrderKey) AS OrderCount,
                   SUM(od.Qty) AS TotalQty,
                   ROUND(AVG(od.Qty), 2) AS AvgQtyPerOrder
            FROM ORDERS o
            JOIN ORDERDETAIL od ON o.OrderKey = od.OrderKey
            WHERE o.OrderDate BETWEEN v_fromdate AND v_todate
            AND (p_storerkey = ' ' OR o.StorerKey = p_storerkey)
            GROUP BY o.ConsigneeKey
            ORDER BY COUNT(DISTINCT o.OrderKey) DESC
        ) LOOP
            DBMS_OUTPUT.PUT_LINE(
                customer_rec.ConsigneeKey || '|' || customer_rec.OrderCount || '|' ||
                customer_rec.TotalQty || '|' || customer_rec.AvgQtyPerOrder
            );
            v_totalorders := v_totalorders + customer_rec.OrderCount;
            v_cnt := v_cnt + 1;
        END LOOP;
    END IF;

    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== SUMMARY ===');
    DBMS_OUTPUT.PUT_LINE('Total Records: ' || v_cnt);
    DBMS_OUTPUT.PUT_LINE('Total Orders: ' || v_totalorders);

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error in order analysis: ' || SQLERRM);
END nspOrderAnalysis;
/

-- nspSystemStatistics 存储过程 (系统统计)
CREATE OR REPLACE PROCEDURE nspSystemStatistics IS
    v_table_count NUMBER;
    v_record_count NUMBER;
    v_total_records NUMBER := 0;
BEGIN
    DBMS_OUTPUT.PUT_LINE('=== SYSTEM STATISTICS ===');
    DBMS_OUTPUT.PUT_LINE('Generated: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('');

    DBMS_OUTPUT.PUT_LINE('Table|RecordCount');
    DBMS_OUTPUT.PUT_LINE('-----|----------');

    -- 主要表的记录统计
    FOR table_rec IN (
        SELECT table_name FROM user_tables
        WHERE table_name IN ('ORDERS', 'ORDERDETAIL', 'RECEIPT', 'RECEIPTDETAIL',
                             'LOTxLOCxID', 'ITRN', 'PICKHEADER', 'PICKDETAIL',
                             'ASN', 'ASNDETAIL', 'SKU', 'LOC', 'STORER')
        ORDER BY table_name
    ) LOOP
        EXECUTE IMMEDIATE 'SELECT COUNT(*) FROM ' || table_rec.table_name INTO v_record_count;
        DBMS_OUTPUT.PUT_LINE(table_rec.table_name || '|' || v_record_count);
        v_total_records := v_total_records + v_record_count;
    END LOOP;

    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== DATABASE SUMMARY ===');

    -- 表总数
    SELECT COUNT(*) INTO v_table_count FROM user_tables;
    DBMS_OUTPUT.PUT_LINE('Total Tables: ' || v_table_count);
    DBMS_OUTPUT.PUT_LINE('Total Records (Main Tables): ' || v_total_records);

    -- 存储商统计
    SELECT COUNT(*) INTO v_record_count FROM STORER;
    DBMS_OUTPUT.PUT_LINE('Total Storers: ' || v_record_count);

    -- SKU统计
    SELECT COUNT(*) INTO v_record_count FROM SKU;
    DBMS_OUTPUT.PUT_LINE('Total SKUs: ' || v_record_count);

    -- 位置统计
    SELECT COUNT(*) INTO v_record_count FROM LOC;
    DBMS_OUTPUT.PUT_LINE('Total Locations: ' || v_record_count);

    -- 当前库存统计
    SELECT COUNT(*) INTO v_record_count FROM LOTxLOCxID WHERE Qty > 0;
    DBMS_OUTPUT.PUT_LINE('Active Inventory Records: ' || v_record_count);

    -- 今日事务统计
    SELECT COUNT(*) INTO v_record_count FROM ITRN WHERE TRUNC(EffectiveDate) = TRUNC(SYSDATE);
    DBMS_OUTPUT.PUT_LINE('Today Transactions: ' || v_record_count);

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Error in system statistics: ' || SQLERRM);
END nspSystemStatistics;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle报表和统计存储过程脚本执行完成';
PROMPT '>>> 已创建完整的报表和统计存储过程集合 (6个)';
PROMPT '>>> 包含：库存报表、活动报表、性能报表、库存分析、订单分析、系统统计等功能';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 报表和统计存储过程创建完成！';
PROMPT '总报表统计存储过程数量：6个专业存储过程';
PROMPT '功能覆盖：';
PROMPT '- 库存报表 (汇总/详细/位置)';
PROMPT '- 活动报表 (事务历史)';
PROMPT '- 性能报表 (拣选/收货性能)';
PROMPT '- 库存分析 (ABC/老化/周转)';
PROMPT '- 订单分析 (每日/状态/客户)';
PROMPT '- 系统统计 (数据库概览)';
PROMPT '=============================================';
