-- =============================================
-- Oracle版本 - 创建订单管理表结构脚本 (第三部分)
-- 功能：创建仓库管理系统的订单管理表结构 (Oracle版本)
-- 用途：订单、拣选、波次管理等核心业务表
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- ORDERS 表 (订单表) - 订单头信息表
-- =============================================
CREATE TABLE ORDERS
(OrderKey                 CHAR(10) NOT NULL,
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_ORDERS_StorerKey DEFAULT ' ',
ExternOrderKey           VARCHAR2(30) NOT NULL
CONSTRAINT DF_ORDERS_ExternOrderKey DEFAULT ' ',
OrderDate                DATE NOT NULL
CONSTRAINT DF_ORDERS_OrderDate DEFAULT SYSDATE,
DeliveryDate             DATE NOT NULL
CONSTRAINT DF_ORDERS_DeliveryDate DEFAULT SYSDATE,
ConsigneeKey             CHAR(15) NOT NULL
CONSTRAINT DF_ORDERS_ConsigneeKey DEFAULT ' ',
C_Company                VARCHAR2(45) NULL,
C_Contact1               VARCHAR2(30) NULL,
C_Contact2               VARCHAR2(30) NULL,
C_Address1               VARCHAR2(45) NULL,
C_Address2               VARCHAR2(45) NULL,
C_Address3               VARCHAR2(45) NULL,
C_Address4               VARCHAR2(45) NULL,
C_City                   VARCHAR2(45) NULL,
C_State                  CHAR(2) NULL,
C_Zip                    VARCHAR2(18) NULL,
C_Country                VARCHAR2(30) NULL,
C_ISOCntryCode           CHAR(10) NULL,
C_Phone1                 VARCHAR2(18) NULL,
C_Phone2                 VARCHAR2(18) NULL,
C_Fax1                   VARCHAR2(18) NULL,
C_Fax2                   VARCHAR2(18) NULL,
C_Email1                 VARCHAR2(60) NULL,
C_Email2                 VARCHAR2(60) NULL,
CarrierKey               CHAR(15) NULL,
CarrierName              VARCHAR2(30) NULL,
CarrierAddress1          VARCHAR2(45) NULL,
CarrierAddress2          VARCHAR2(45) NULL,
CarrierCity              VARCHAR2(45) NULL,
CarrierState             CHAR(2) NULL,
CarrierZip               VARCHAR2(10) NULL,
CarrierReference         VARCHAR2(18) NULL,
WarehouseReference       VARCHAR2(18) NULL,
Route                    CHAR(10) NULL,
Stop                     CHAR(10) NULL,
Door                     CHAR(10) NULL,
Type                     CHAR(10) NOT NULL
CONSTRAINT DF_ORDERS_Type DEFAULT '0',
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_ORDERS_Status DEFAULT '0',
Priority                 CHAR(10) NOT NULL
CONSTRAINT DF_ORDERS_Priority DEFAULT '5',
Notes                    CLOB NULL,
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_ORDERS_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_ORDERS_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_ORDERS_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_ORDERS_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_ORDERS_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE ORDERS ADD CONSTRAINT PK_ORDERS PRIMARY KEY (OrderKey);

-- 添加检查约束
ALTER TABLE ORDERS ADD CONSTRAINT CK_ORDERS_Status 
CHECK (Status IN ('0', '1', '2', '3', '4', '5', '9'));
ALTER TABLE ORDERS ADD CONSTRAINT CK_ORDERS_Type 
CHECK (Type IN ('0', '1', '2', '3', '4', '5'));

-- 创建索引
CREATE INDEX IX_ORDERS_StorerKey ON ORDERS (StorerKey);
CREATE INDEX IX_ORDERS_ExternOrderKey ON ORDERS (ExternOrderKey);
CREATE INDEX IX_ORDERS_OrderDate ON ORDERS (OrderDate);
CREATE INDEX IX_ORDERS_DeliveryDate ON ORDERS (DeliveryDate);
CREATE INDEX IX_ORDERS_ConsigneeKey ON ORDERS (ConsigneeKey);
CREATE INDEX IX_ORDERS_Status ON ORDERS (Status);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON ORDERS TO nsql;

PROMPT '>>> 已创建表 ORDERS (订单表)';

-- =============================================
-- ORDERDETAIL 表 (订单明细表) - 订单行项目表
-- =============================================
CREATE TABLE ORDERDETAIL
(OrderKey                 CHAR(10) NOT NULL,
OrderLineNumber          CHAR(5) NOT NULL,
OrderDetailSysId         NUMBER(10) NULL,
ExternOrderKey           VARCHAR2(30) NOT NULL
CONSTRAINT DF_ORDERDETAIL_ExternOrderKey DEFAULT ' ',
ExternLineNo             CHAR(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_ExternLineNo DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_ORDERDETAIL_Sku DEFAULT ' ',
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_ORDERDETAIL_StorerKey DEFAULT ' ',
ManufacturerSku          CHAR(20) NOT NULL
CONSTRAINT DF_ORDERDETAIL_ManufacturerSku DEFAULT ' ',
RetailSku                CHAR(20) NOT NULL
CONSTRAINT DF_ORDERDETAIL_RetailSku DEFAULT ' ',
AltSku                   CHAR(20) NOT NULL
CONSTRAINT DF_ORDERDETAIL_AltSku DEFAULT ' ',
OriginalQty              NUMBER(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_OriginalQty DEFAULT 0,
OpenQty                  NUMBER(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_OpenQty DEFAULT 0,
ShippedQty               NUMBER(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_ShippedQty DEFAULT 0,
UOM                      CHAR(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_UOM DEFAULT ' ',
PackKey                  CHAR(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_PackKey DEFAULT 'STD',
Lot                      CHAR(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_Lot DEFAULT ' ',
ID                       CHAR(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_ID DEFAULT ' ',
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_Status DEFAULT '0',
UnitPrice                NUMBER(12,6)
CONSTRAINT DF_ORDERDETAIL_UnitPrice DEFAULT 0,
Tax01                    NUMBER(12,6)
CONSTRAINT DF_ORDERDETAIL_Tax01 DEFAULT 0,
Tax02                    NUMBER(12,6)
CONSTRAINT DF_ORDERDETAIL_Tax02 DEFAULT 0,
ExtendedPrice            NUMBER(12,6)
CONSTRAINT DF_ORDERDETAIL_ExtendedPrice DEFAULT 0,
Lottable01              CHAR(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_LOTTABLE01 DEFAULT ' ',
Lottable02              CHAR(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_LOTTABLE02 DEFAULT ' ',
Lottable03              CHAR(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_LOTTABLE03 DEFAULT ' ',
Lottable04              DATE NULL,
Lottable05              DATE NULL,
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_ORDERDETAIL_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_ORDERDETAIL_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_ORDERDETAIL_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL,
TariffKey               CHAR(10) NULL
);

-- 添加主键
ALTER TABLE ORDERDETAIL ADD CONSTRAINT PK_ORDERDETAIL PRIMARY KEY (OrderKey, OrderLineNumber);

-- 添加检查约束
ALTER TABLE ORDERDETAIL ADD CONSTRAINT CK_ORDERDETAIL_Status 
CHECK (Status IN ('0', '1', '2', '3', '4', '5', '9'));

-- 创建索引
CREATE INDEX IX_ORDERDETAIL_OrderKey ON ORDERDETAIL (OrderKey);
CREATE INDEX IX_ORDERDETAIL_Sku ON ORDERDETAIL (Sku);
CREATE INDEX IX_ORDERDETAIL_StorerKey ON ORDERDETAIL (StorerKey);
CREATE INDEX IX_ORDERDETAIL_Status ON ORDERDETAIL (Status);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON ORDERDETAIL TO nsql;

PROMPT '>>> 已创建表 ORDERDETAIL (订单明细表)';

-- =============================================
-- PICKHEADER 表 (拣选头表) - 拣选任务头表
-- =============================================
CREATE TABLE PICKHEADER
(PickHeaderKey            CHAR(18) NOT NULL,
OrderKey                 CHAR(10) NOT NULL
CONSTRAINT DF_PICKHEADER_OrderKey DEFAULT ' ',
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_PICKHEADER_StorerKey DEFAULT ' ',
WaveKey                  CHAR(10) NOT NULL
CONSTRAINT DF_PICKHEADER_WaveKey DEFAULT ' ',
PickMethod               CHAR(1) NOT NULL
CONSTRAINT DF_PICKHEADER_PickMethod DEFAULT ' ',
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_PICKHEADER_Status DEFAULT '0',
Priority                 CHAR(10) NOT NULL
CONSTRAINT DF_PICKHEADER_Priority DEFAULT '5',
AssignedUser             CHAR(18) NOT NULL
CONSTRAINT DF_PICKHEADER_AssignedUser DEFAULT ' ',
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_PICKHEADER_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_PICKHEADER_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PICKHEADER_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_PICKHEADER_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_PICKHEADER_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL,
OptimizeCop             CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PICKHEADER ADD CONSTRAINT PK_PICKHEADER PRIMARY KEY (PickHeaderKey);

-- 添加检查约束
ALTER TABLE PICKHEADER ADD CONSTRAINT CK_PICKHEADER_Status 
CHECK (Status IN ('0', '1', '2', '3', '4', '5', '9'));

-- 创建索引
CREATE INDEX IX_PICKHEADER_OrderKey ON PICKHEADER (OrderKey);
CREATE INDEX IX_PICKHEADER_StorerKey ON PICKHEADER (StorerKey);
CREATE INDEX IX_PICKHEADER_WaveKey ON PICKHEADER (WaveKey);
CREATE INDEX IX_PICKHEADER_Status ON PICKHEADER (Status);
CREATE INDEX IX_PICKHEADER_AssignedUser ON PICKHEADER (AssignedUser);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PICKHEADER TO nsql;

PROMPT '>>> 已创建表 PICKHEADER (拣选头表)';

-- 提交事务
COMMIT;

PROMPT '>>> 订单管理表结构脚本执行完成';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
