-- =============================================
-- 创建最终遗漏表结构脚本 (第十部分)
-- 功能：创建仓库管理系统中最终遗漏的重要表结构
-- 用途：消息管理、交叉转运、库存控制、RF设备等
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 消息管理表
-- =============================================

-- MESSAGE_ID 表 (消息ID表)
CREATE TABLE MESSAGE_ID
(MsgId                   char(40) NOT NULL
CONSTRAINT DF_MESSAGE_ID_MsgId DEFAULT "",
MsgType                  char(10) NOT NULL
CONSTRAINT DF_MESSAGE_ID_MsgType DEFAULT "",
MsgStatus                char(10) NOT NULL
CONSTRAINT DF_MESSAGE_ID_MsgStatus DEFAULT "0",
AddDate                  datetime NOT NULL
CONSTRAINT DF_MESSAGE_ID_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_MESSAGE_ID_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_MESSAGE_ID_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_MESSAGE_ID_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('MESSAGE_ID') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE MESSAGE_ID FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE MESSAGE_ID>>>'
GRANT INSERT ON MESSAGE_ID TO nsql
GRANT UPDATE ON MESSAGE_ID TO nsql
GRANT DELETE ON MESSAGE_ID TO nsql
GRANT SELECT ON MESSAGE_ID TO nsql
END
GO

-- MESSAGE_TEXT 表 (消息文本表)
CREATE TABLE MESSAGE_TEXT
(MsgId                   char(40) NOT NULL,
LineNumber               int NOT NULL,
MessageText              varchar(255) NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_MessageText DEFAULT "",
AddDate                  datetime NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('MESSAGE_TEXT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE MESSAGE_TEXT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE MESSAGE_TEXT>>>'
GRANT INSERT ON MESSAGE_TEXT TO nsql
GRANT UPDATE ON MESSAGE_TEXT TO nsql
GRANT DELETE ON MESSAGE_TEXT TO nsql
GRANT SELECT ON MESSAGE_TEXT TO nsql
END
GO

-- =============================================
-- 计费汇总表
-- =============================================

-- BILL_ACCUMULATEDCHARGES 表 (计费累计费用表)
CREATE TABLE BILL_ACCUMULATEDCHARGES (
Ident                    int IDENTITY (0, 1) NOT NULL , 
StorerKey                char(15) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_StorerKey DEFAULT "",
TariffKey                char(10) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_TariffKey DEFAULT "",
ChargeType               char(10) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_ChargeType DEFAULT "",
ChargeAmount             decimal(12,6) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_ChargeAmount DEFAULT 0.0,
BillingPeriod            char(10) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_BillingPeriod DEFAULT "",
AddDate                  datetime NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('BILL_ACCUMULATEDCHARGES') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE BILL_ACCUMULATEDCHARGES FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE BILL_ACCUMULATEDCHARGES>>>'
GRANT INSERT ON BILL_ACCUMULATEDCHARGES TO nsql
GRANT UPDATE ON BILL_ACCUMULATEDCHARGES TO nsql
GRANT DELETE ON BILL_ACCUMULATEDCHARGES TO nsql
GRANT SELECT ON BILL_ACCUMULATEDCHARGES TO nsql
END
GO

-- BILLING_DETAIL_CUT 表 (计费明细截止表)
CREATE TABLE BILLING_DETAIL_CUT (
StorerKey char (15) NOT NULL ,
BillThruDate datetime NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_BillThruDate DEFAULT CURRENT_Timestamp,
AddDate                  datetime NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('BILLING_DETAIL_CUT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE BILLING_DETAIL_CUT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE BILLING_DETAIL_CUT>>>'
GRANT INSERT ON BILLING_DETAIL_CUT TO nsql
GRANT UPDATE ON BILLING_DETAIL_CUT TO nsql
GRANT DELETE ON BILLING_DETAIL_CUT TO nsql
GRANT SELECT ON BILLING_DETAIL_CUT TO nsql
END
GO

-- BILLING_SUMMARY_CUT 表 (计费汇总截止表)
CREATE TABLE BILLING_SUMMARY_CUT(
StorerKey char (15) NOT NULL ,
BillThruDate datetime NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_BillThruDate DEFAULT CURRENT_Timestamp,
AddDate                  datetime NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('BILLING_SUMMARY_CUT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE BILLING_SUMMARY_CUT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE BILLING_SUMMARY_CUT>>>'
GRANT INSERT ON BILLING_SUMMARY_CUT TO nsql
GRANT UPDATE ON BILLING_SUMMARY_CUT TO nsql
GRANT DELETE ON BILLING_SUMMARY_CUT TO nsql
GRANT SELECT ON BILLING_SUMMARY_CUT TO nsql
END
GO

-- =============================================
-- 任务调度表
-- =============================================

-- TRIDENTSCHEDULER 表 (三叉戟调度器表)
CREATE TABLE TRIDENTSCHEDULER
(
SchedulerKey             char(10) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_SchedulerKey DEFAULT "",
TaskType                 char(20) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_TaskType DEFAULT "",
TaskStatus               char(10) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_TaskStatus DEFAULT "0",
ScheduledTime            datetime NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_ScheduledTime DEFAULT CURRENT_Timestamp,
ExecutedTime             datetime NULL,
TaskParameters           text NULL,
AddDate                  datetime NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('TRIDENTSCHEDULER') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TRIDENTSCHEDULER FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TRIDENTSCHEDULER>>>'
GRANT INSERT ON TRIDENTSCHEDULER TO nsql
GRANT UPDATE ON TRIDENTSCHEDULER TO nsql
GRANT DELETE ON TRIDENTSCHEDULER TO nsql
GRANT SELECT ON TRIDENTSCHEDULER TO nsql
END
GO

-- =============================================
-- 预分配拣选表
-- =============================================

-- PreAllocatePickDetail 表 (预分配拣选明细表)
CREATE TABLE PreAllocatePickDetail
(PreAllocatePickDetailKey char(10)     NOT NULL
CONSTRAINT DF_PAPD_Key         DEFAULT "" ,
OrderKey                     char(10)     NOT NULL
CONSTRAINT DF_PAPD_OrderKey    DEFAULT "" ,
OrderLineNumber              char(5)      NOT NULL
CONSTRAINT DF_PAPD_OrderLineNumber DEFAULT "" ,
Sku                          char(20)     NOT NULL
CONSTRAINT DF_PAPD_Sku         DEFAULT "" ,
StorerKey                    char(15)     NOT NULL
CONSTRAINT DF_PAPD_StorerKey   DEFAULT "" ,
Lot                          char(10)     NOT NULL
CONSTRAINT DF_PAPD_Lot         DEFAULT "" ,
Loc                          char(10)     NOT NULL
CONSTRAINT DF_PAPD_Loc         DEFAULT "" ,
ID                           char(18)     NOT NULL
CONSTRAINT DF_PAPD_ID          DEFAULT "" ,
Qty                          int          NOT NULL
CONSTRAINT DF_PAPD_Qty         DEFAULT 0 ,
Status                       char(10)     NOT NULL
CONSTRAINT DF_PAPD_Status      DEFAULT "0" ,
AddDate                      datetime     NOT NULL
CONSTRAINT DF_PAPD_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                       char(18)     NOT NULL
CONSTRAINT DF_PAPD_AddWho      DEFAULT USER ,
EditDate                     datetime     NOT NULL
CONSTRAINT DF_PAPD_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                      char(18)     NOT NULL
CONSTRAINT DF_PAPD_EditWho     DEFAULT USER ,
TrafficCop                   char(1)      NULL,
ArchiveCop                   char(1)      NULL
)
GO

IF OBJECT_ID('PreAllocatePickDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PreAllocatePickDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PreAllocatePickDetail>>>'
GRANT INSERT ON PreAllocatePickDetail TO nsql
GRANT UPDATE ON PreAllocatePickDetail TO nsql
GRANT DELETE ON PreAllocatePickDetail TO nsql
GRANT SELECT ON PreAllocatePickDetail TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 交叉转运表
-- =============================================

-- XDOCK 表 (交叉转运表)
CREATE TABLE XDOCK
(XDOCKKEY           char(10) NOT NULL ,
Status             char(10) NOT NULL
CONSTRAINT DF_XDOCK_Status DEFAULT "0"
CONSTRAINT CK_XDOCK_Status CHECK ( Status LIKE '[0-9]' ),
ExternXDOCKKey     char(30) NOT NULL
CONSTRAINT DF_XDOCK_ExternXDOCKKey DEFAULT "" ,
XDOCKDate          datetime NOT NULL
CONSTRAINT DF_XDOCK_XDOCKDate DEFAULT CURRENT_TIMESTAMP ,
Type               char(10) NOT NULL
CONSTRAINT DF_XDOCK_Type DEFAULT "0" ,
Notes              text NULL ,
EffectiveDate      datetime     NOT NULL
CONSTRAINT DF_XDOCK_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate            datetime NOT NULL
CONSTRAINT DF_XDOCK_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho             char(18) NOT NULL
CONSTRAINT DF_XDOCK_AddWho DEFAULT USER ,
EditDate           datetime NOT NULL
CONSTRAINT DF_XDOCK_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho            char(18) NOT NULL
CONSTRAINT DF_XDOCK_EditWho DEFAULT USER ,
TrafficCop         char(1)  NULL,
ArchiveCop         char(1)  NULL
)
GO

IF OBJECT_ID('XDOCK') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE XDOCK FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE XDOCK>>>'
GRANT INSERT ON XDOCK TO nsql
GRANT UPDATE ON XDOCK TO nsql
GRANT DELETE ON XDOCK TO nsql
GRANT SELECT ON XDOCK TO nsql
END
GO

-- XDOCKDETAIL 表 (交叉转运明细表)
CREATE TABLE XDOCKDETAIL
(XDOCKKEY            char(10) NOT NULL,
XDOCKLineNumber     char(5) NOT NULL,
XDOCKDetailSysId    int NULL ,
ExternXDOCKKey      char(30) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_ExternXDOCKKey DEFAULT "" ,
ExternLineNo        char(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_ExternLineNo DEFAULT "" ,
Sku                 char(20) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_Sku DEFAULT "" ,
StorerKey           char(15) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_StorerKey DEFAULT "" ,
Qty                 int NOT NULL
CONSTRAINT DF_XDOCKDETAIL_Qty DEFAULT 0 ,
UOM                 char(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_UOM DEFAULT "" ,
PackKey             char(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_PackKey DEFAULT "STD" ,
Lot                 char(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_Lot DEFAULT "" ,
ID                  char(18) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_ID DEFAULT "" ,
FromLoc             char(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_FromLoc DEFAULT "UNKNOWN" ,
ToLoc               char(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_ToLoc DEFAULT "UNKNOWN" ,
Status              char(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_Status DEFAULT "0"
CONSTRAINT CK_XDOCKDETAIL_Status CHECK ( Status LIKE '[0-9]' ),
EffectiveDate       datetime     NOT NULL
CONSTRAINT DF_XDOCKDETAIL_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate             datetime NOT NULL
CONSTRAINT DF_XDOCKDETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho              char(18) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_AddWho DEFAULT USER ,
EditDate            datetime NOT NULL
CONSTRAINT DF_XDOCKDETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho             char(18) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_EditWho DEFAULT USER ,
TrafficCop          char(1)  NULL,
ArchiveCop          char(1)  NULL
)
GO

IF OBJECT_ID('XDOCKDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE XDOCKDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE XDOCKDETAIL>>>'
GRANT INSERT ON XDOCKDETAIL TO nsql
GRANT UPDATE ON XDOCKDETAIL TO nsql
GRANT DELETE ON XDOCKDETAIL TO nsql
GRANT SELECT ON XDOCKDETAIL TO nsql
END
GO

-- =============================================
-- 库存控制表
-- =============================================

-- INVENTORYHOLD 表 (库存冻结表)
CREATE TABLE INVENTORYHOLD
(InventoryHoldKey         char(10)     NOT NULL
CONSTRAINT DF_IH_Key         DEFAULT "" ,
StorerKey                char(15)     NOT NULL
CONSTRAINT DF_IH_StorerKey   DEFAULT "" ,
Sku                      char(20)     NOT NULL
CONSTRAINT DF_IH_Sku         DEFAULT "" ,
Lot                      char(10)     NOT NULL
CONSTRAINT DF_IH_Lot         DEFAULT "" ,
Loc                      char(10)     NOT NULL
CONSTRAINT DF_IH_Loc         DEFAULT "" ,
ID                       char(18)     NOT NULL
CONSTRAINT DF_IH_ID          DEFAULT "" ,
HoldType                 char(10)     NOT NULL
CONSTRAINT DF_IH_HoldType    DEFAULT "" ,
HoldReason               char(60)     NOT NULL
CONSTRAINT DF_IH_HoldReason  DEFAULT "" ,
QtyHeld                  int          NOT NULL
CONSTRAINT DF_IH_QtyHeld     DEFAULT 0 ,
Status                   char(10)     NOT NULL
CONSTRAINT DF_IH_Status      DEFAULT "0" ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_IH_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_IH_AddWho      DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_IH_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_IH_EditWho     DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('INVENTORYHOLD') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE INVENTORYHOLD FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE INVENTORYHOLD>>>'
GRANT INSERT ON INVENTORYHOLD TO nsql
GRANT UPDATE ON INVENTORYHOLD TO nsql
GRANT DELETE ON INVENTORYHOLD TO nsql
GRANT SELECT ON INVENTORYHOLD TO nsql
END
GO

-- 注意：此脚本包含最终遗漏的重要表结构
-- 完整的表结构请参考原始 NEPISQL.sql 文件
