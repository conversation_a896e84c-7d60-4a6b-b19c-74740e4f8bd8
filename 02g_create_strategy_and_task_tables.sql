-- =============================================
-- 创建策略和任务管理表结构脚本 (第七部分)
-- 功能：创建仓库管理系统的策略和任务管理相关表结构
-- 用途：分配策略、上架策略、任务管理、设备管理等
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 策略管理表
-- =============================================

-- Strategy 表 (策略表)
CREATE TABLE Strategy
(StrategyKey               char(10)    NOT NULL
CONSTRAINT DF_STRATEGY_Key         DEFAULT "" ,
Descr                     char(60)    NOT NULL
CONSTRAINT DF_STRATEGY_Descr       DEFAULT "" ,
PreAllocateStrategyKey    char(10)    NOT NULL
CONSTRAINT DF_STRATEGY_PreAllocateStrategyKey DEFAULT "" ,
AllocateStrategyKey       char(10)    NOT NULL
CONSTRAINT DF_STRATEGY_AllocateStrategyKey DEFAULT "" ,
PutawayStrategyKey        char(10)    NOT NULL
CONSTRAINT DF_STRATEGY_PutawayStrategyKey DEFAULT "" ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_STRATEGY_AddDate       DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_STRATEGY_AddWho        DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_STRATEGY_EditDate      DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_STRATEGY_EditWho       DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('Strategy') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE Strategy FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE Strategy>>>'
GRANT INSERT ON Strategy TO nsql
GRANT UPDATE ON Strategy TO nsql
GRANT DELETE ON Strategy TO nsql
GRANT SELECT ON Strategy TO nsql
END
GO

-- PreAllocateStrategy 表 (预分配策略表)
CREATE TABLE PreAllocateStrategy
(PreAllocateStrategyKey   char(10)     NOT NULL
CONSTRAINT DF_PreAS_Key         DEFAULT "" ,
Descr                    char(60)     NOT NULL
CONSTRAINT DF_PreAS_Descr       DEFAULT "" ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_PreAS_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_PreAS_AddWho      DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_PreAS_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_PreAS_EditWho     DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('PreAllocateStrategy') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PreAllocateStrategy FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PreAllocateStrategy>>>'
GRANT INSERT ON PreAllocateStrategy TO nsql
GRANT UPDATE ON PreAllocateStrategy TO nsql
GRANT DELETE ON PreAllocateStrategy TO nsql
GRANT SELECT ON PreAllocateStrategy TO nsql
END
GO

-- PreAllocateStrategyDetail 表 (预分配策略明细表)
CREATE TABLE PreAllocateStrategyDetail
(PreAllocateStrategyKey         char(10)     NOT NULL
CONSTRAINT DF_PreAsd_Key         DEFAULT "" ,
PreAllocateStrategyLineNumber  char(5)      NOT NULL
CONSTRAINT DF_PreAsd_LineNumber  DEFAULT "" ,
DESCR                          char(60)     NOT NULL
CONSTRAINT DF_PreAsd_Descr       DEFAULT "" ,
StrategyType                   char(10)     NOT NULL
CONSTRAINT DF_PreAsd_StrategyType DEFAULT "" ,
FromLoc                        char(10)     NOT NULL
CONSTRAINT DF_PreAsd_FromLoc     DEFAULT "" ,
ToLoc                          char(10)     NOT NULL
CONSTRAINT DF_PreAsd_ToLoc       DEFAULT "" ,
AddDate                        datetime     NOT NULL
CONSTRAINT DF_PreAsd_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                         char(18)     NOT NULL
CONSTRAINT DF_PreAsd_AddWho      DEFAULT USER ,
EditDate                       datetime     NOT NULL
CONSTRAINT DF_PreAsd_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                        char(18)     NOT NULL
CONSTRAINT DF_PreAsd_EditWho     DEFAULT USER ,
TrafficCop                     char(1)      NULL,
ArchiveCop                     char(1)      NULL
)
GO

IF OBJECT_ID('PreAllocateStrategyDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PreAllocateStrategyDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PreAllocateStrategyDetail>>>'
GRANT INSERT ON PreAllocateStrategyDetail TO nsql
GRANT UPDATE ON PreAllocateStrategyDetail TO nsql
GRANT DELETE ON PreAllocateStrategyDetail TO nsql
GRANT SELECT ON PreAllocateStrategyDetail TO nsql
END
GO

-- AllocateStrategy 表 (分配策略表)
CREATE TABLE AllocateStrategy
(AllocateStrategyKey      char(10)     NOT NULL
CONSTRAINT DF_AS_Key         DEFAULT "" ,
Descr                    char(60)     NOT NULL
CONSTRAINT DF_AS_Descr       DEFAULT "" ,
RetryIfQtyRemain         int          NOT NULL
CONSTRAINT DF_AS_RetryIfQtyRemain DEFAULT 0 ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_AS_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_AS_AddWho      DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_AS_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_AS_EditWho     DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('AllocateStrategy') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE AllocateStrategy FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE AllocateStrategy>>>'
GRANT INSERT ON AllocateStrategy TO nsql
GRANT UPDATE ON AllocateStrategy TO nsql
GRANT DELETE ON AllocateStrategy TO nsql
GRANT SELECT ON AllocateStrategy TO nsql
END
GO

-- AllocateStrategyDetail 表 (分配策略明细表)
CREATE TABLE AllocateStrategyDetail
(AllocateStrategyKey            char(10)     NOT NULL
CONSTRAINT DF_Asd_Key         DEFAULT "" ,
AllocateStrategyLineNumber     char(5)      NOT NULL
CONSTRAINT DF_Asd_LineNumber  DEFAULT "" ,
DESCR                          char(60)     NOT NULL
CONSTRAINT DF_Asd_Descr       DEFAULT "" ,
StrategyType                   char(10)     NOT NULL
CONSTRAINT DF_Asd_StrategyType DEFAULT "" ,
FromLoc                        char(10)     NOT NULL
CONSTRAINT DF_Asd_FromLoc     DEFAULT "" ,
ToLoc                          char(10)     NOT NULL
CONSTRAINT DF_Asd_ToLoc       DEFAULT "" ,
SortOrder                      char(10)     NOT NULL
CONSTRAINT DF_Asd_SortOrder   DEFAULT "" ,
AddDate                        datetime     NOT NULL
CONSTRAINT DF_Asd_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                         char(18)     NOT NULL
CONSTRAINT DF_Asd_AddWho      DEFAULT USER ,
EditDate                       datetime     NOT NULL
CONSTRAINT DF_Asd_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                        char(18)     NOT NULL
CONSTRAINT DF_Asd_EditWho     DEFAULT USER ,
TrafficCop                     char(1)      NULL,
ArchiveCop                     char(1)      NULL
)
GO

IF OBJECT_ID('AllocateStrategyDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE AllocateStrategyDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE AllocateStrategyDetail>>>'
GRANT INSERT ON AllocateStrategyDetail TO nsql
GRANT UPDATE ON AllocateStrategyDetail TO nsql
GRANT DELETE ON AllocateStrategyDetail TO nsql
GRANT SELECT ON AllocateStrategyDetail TO nsql
END
GO

-- PutawayStrategy 表 (上架策略表)
CREATE TABLE PutawayStrategy
(PutawayStrategyKey       char(10) NOT NULL,
Descr                    char(60) NOT NULL
CONSTRAINT DF_PAS_Descr DEFAULT "" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_PAS_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PAS_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PAS_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PAS_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('PutawayStrategy') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PutawayStrategy FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PutawayStrategy>>>'
GRANT INSERT ON PutawayStrategy TO nsql
GRANT UPDATE ON PutawayStrategy TO nsql
GRANT DELETE ON PutawayStrategy TO nsql
GRANT SELECT ON PutawayStrategy TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- PutawayStrategyDetail 表 (上架策略明细表)
CREATE TABLE PutawayStrategyDetail
(PutawayStrategyKey             char(10) NOT NULL,
PutawayStrategyLineNumber      char(5) NOT NULL,
PAType                         char(5) NOT NULL
CONSTRAINT DF_PASD_Patype DEFAULT "",
FROMLOC                        char(10) NOT NULL
CONSTRAINT DF_PASD_FromLoc DEFAULT "",
TOLOC                          char(10) NOT NULL
CONSTRAINT DF_PASD_ToLoc DEFAULT "",
LOCTYPE                        char(10) NOT NULL
CONSTRAINT DF_PASD_LocType DEFAULT "",
PUTAWAYZONE                    char(10) NOT NULL
CONSTRAINT DF_PASD_PutawayZone DEFAULT "",
MAXQTY                         int NOT NULL
CONSTRAINT DF_PASD_MaxQty DEFAULT 0,
MAXWEIGHT                      float NOT NULL
CONSTRAINT DF_PASD_MaxWeight DEFAULT 0,
MAXCUBE                        float NOT NULL
CONSTRAINT DF_PASD_MaxCube DEFAULT 0,
MAXPALLETS                     int NOT NULL
CONSTRAINT DF_PASD_MaxPallets DEFAULT 0,
EQUIPMENTPROFILEKEY            char(10) NOT NULL
CONSTRAINT DF_PASD_EquipmentProfileKey DEFAULT "",
DESCR                          char(60) NOT NULL
CONSTRAINT DF_PASD_Descr DEFAULT "",
AddDate                        datetime NOT NULL
CONSTRAINT DF_PASD_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                         char(18) NOT NULL
CONSTRAINT DF_PASD_AddWho DEFAULT USER ,
EditDate                       datetime NOT NULL
CONSTRAINT DF_PASD_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                        char(18) NOT NULL
CONSTRAINT DF_PASD_EditWho DEFAULT USER ,
TrafficCop                     char(1)  NULL,
ArchiveCop                     char(1)  NULL
)
GO

IF OBJECT_ID('PutawayStrategyDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PutawayStrategyDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PutawayStrategyDetail>>>'
GRANT INSERT ON PutawayStrategyDetail TO nsql
GRANT UPDATE ON PutawayStrategyDetail TO nsql
GRANT DELETE ON PutawayStrategyDetail TO nsql
GRANT SELECT ON PutawayStrategyDetail TO nsql
END
GO

-- =============================================
-- 任务管理表
-- =============================================

-- TaskManagerUser 表 (任务管理用户表) - 扩展版本
CREATE TABLE TaskManagerUser
(UserKey                  char(18)     NOT NULL
CONSTRAINT DF_TMU_UserKey          DEFAULT "" ,
PriorityTaskType         char(10)     NOT NULL
CONSTRAINT DF_TMU_PriorityTaskType DEFAULT "1" ,
StrategyKey              char(10)     NOT NULL
CONSTRAINT DF_TMU_StrategyKey      DEFAULT "" ,
TTMStrategyKey           char(10)     NOT NULL
CONSTRAINT DF_TMU_TTMStrategyKey   DEFAULT "" ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_TMU_AddDate          DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_TMU_AddWho           DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_TMU_EditDate         DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_TMU_EditWho          DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('TaskManagerUser') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TaskManagerUser FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TaskManagerUser>>>'
GRANT INSERT ON TaskManagerUser TO nsql
GRANT UPDATE ON TaskManagerUser TO nsql
GRANT DELETE ON TaskManagerUser TO nsql
GRANT SELECT ON TaskManagerUser TO nsql
END
GO

-- TaskManagerUserDetail 表 (任务管理用户明细表)
CREATE TABLE TaskManagerUserDetail
(UserKey                  char(18)     NOT NULL
CONSTRAINT DF_TMUD_UserKey          DEFAULT "" ,
UserLineNumber           char(5)      NOT NULL
CONSTRAINT DF_TMUD_UKLineNumber     DEFAULT "" ,
PermissionType           char(10)     NOT NULL
CONSTRAINT DF_TMUD_PermissionType   DEFAULT "" ,
StorerKey                char(15)     NOT NULL
CONSTRAINT DF_TMUD_StorerKey        DEFAULT "" ,
Sku                      char(20)     NOT NULL
CONSTRAINT DF_TMUD_Sku              DEFAULT "" ,
Loc                      char(10)     NOT NULL
CONSTRAINT DF_TMUD_Loc              DEFAULT "" ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_TMUD_AddDate          DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_TMUD_AddWho           DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_TMUD_EditDate         DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_TMUD_EditWho          DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('TaskManagerUserDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TaskManagerUserDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TaskManagerUserDetail>>>'
GRANT INSERT ON TaskManagerUserDetail TO nsql
GRANT UPDATE ON TaskManagerUserDetail TO nsql
GRANT DELETE ON TaskManagerUserDetail TO nsql
GRANT SELECT ON TaskManagerUserDetail TO nsql
END
GO

-- TTMStrategy 表 (任务管理策略表)
CREATE TABLE TTMStrategy
(TTMStrategyKey           char(10)     NOT NULL
CONSTRAINT DF_TTMS_Key         DEFAULT "" ,
Descr                    char(60)     NOT NULL
CONSTRAINT DF_TTMS_Descr       DEFAULT "" ,
InterleaveTasks          char(10)     NOT NULL
CONSTRAINT DF_TTMS_InterleaveTasks DEFAULT "" ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_TTMS_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_TTMS_AddWho      DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_TTMS_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_TTMS_EditWho     DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('TTMStrategy') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TTMStrategy FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TTMStrategy>>>'
GRANT INSERT ON TTMStrategy TO nsql
GRANT UPDATE ON TTMStrategy TO nsql
GRANT DELETE ON TTMStrategy TO nsql
GRANT SELECT ON TTMStrategy TO nsql
END
GO

-- TTMStrategyDetail 表 (任务管理策略明细表)
CREATE TABLE TTMStrategyDetail
(TTMStrategyKey                 char(10)     NOT NULL
CONSTRAINT DF_TTMSD_Key         DEFAULT "" ,
TTMStrategyLineNumber          char(5)      NOT NULL
CONSTRAINT DF_TTMSD_LineNumber  DEFAULT "" ,
Descr                          char(60)     NOT NULL
CONSTRAINT DF_TTMSD_Descr       DEFAULT "" ,
TaskType                       char(10)     NOT NULL
CONSTRAINT DF_TTMSD_TaskType    DEFAULT "" ,
Priority                       char(10)     NOT NULL
CONSTRAINT DF_TTMSD_Priority    DEFAULT "" ,
AddDate                        datetime     NOT NULL
CONSTRAINT DF_TTMSD_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                         char(18)     NOT NULL
CONSTRAINT DF_TTMSD_AddWho      DEFAULT USER ,
EditDate                       datetime     NOT NULL
CONSTRAINT DF_TTMSD_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                        char(18)     NOT NULL
CONSTRAINT DF_TTMSD_EditWho     DEFAULT USER ,
TrafficCop                     char(1)      NULL,
ArchiveCop                     char(1)      NULL
)
GO

IF OBJECT_ID('TTMStrategyDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TTMStrategyDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TTMStrategyDetail>>>'
GRANT INSERT ON TTMStrategyDetail TO nsql
GRANT UPDATE ON TTMStrategyDetail TO nsql
GRANT DELETE ON TTMStrategyDetail TO nsql
GRANT SELECT ON TTMStrategyDetail TO nsql
END
GO

-- 注意：此脚本包含策略和任务管理相关表结构
-- 完整的表结构请参考原始 NEPISQL.sql 文件
