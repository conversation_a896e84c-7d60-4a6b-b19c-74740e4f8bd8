-- =============================================
-- Oracle版本 - 创建最终触发器脚本 (第五部分)
-- 功能：创建仓库管理系统的最终触发器集合 (Oracle版本)
-- 用途：LOT管理、CARTONIZATION管理、MOVE管理、TRANSFER管理等最终触发器
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中的108个SQL Server触发器转换为Oracle语法 (最终部分)
-- 包含：LOT管理、CARTONIZATION管理、MOVE管理、TRANSFER管理等触发器
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- LOT管理触发器 (从SQL Server转换)
-- =============================================

-- LOT表的插入触发器 (从ntrLotAdd转换)
CREATE OR REPLACE TRIGGER NTR_LOT_ADD
BEFORE INSERT ON LOT
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 验证LOT不能为空
    IF :NEW.Lot IS NULL OR TRIM(:NEW.Lot) = ' ' THEN
        RAISE_APPLICATION_ERROR(-20018, 'Lot cannot be empty');
    END IF;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := 'OK';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOT_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOT_ADD (LOT插入触发器)';

-- LOT表的更新触发器 (从ntrLotUpdate转换)
CREATE OR REPLACE TRIGGER NTR_LOT_UPDATE
BEFORE UPDATE ON LOT
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOT_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOT_UPDATE (LOT更新触发器)';

-- LOT表的删除触发器 (从ntrLotDelete转换)
CREATE OR REPLACE TRIGGER NTR_LOT_DELETE
BEFORE DELETE ON LOT
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有库存使用此LOT
    SELECT COUNT(*) INTO v_cnt
    FROM LOTxLOCxID
    WHERE Lot = :OLD.Lot;
    
    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20019, 'Cannot delete lot with existing inventory');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_LOT_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_LOT_DELETE (LOT删除触发器)';

-- =============================================
-- CARTONIZATION管理触发器 (从SQL Server转换)
-- =============================================

-- CARTONIZATION表的插入触发器 (从ntrCartonizationAdd转换)
CREATE OR REPLACE TRIGGER NTR_CARTONIZATION_ADD
BEFORE INSERT ON CARTONIZATION
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成装箱键如果为空
    IF :NEW.CartonizationKey IS NULL OR TRIM(:NEW.CartonizationKey) = ' ' THEN
        SELECT 'CTN' || LPAD(TO_CHAR(SEQ_CARTONIZATION.NEXTVAL), 7, '0') INTO :NEW.CartonizationKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CARTONIZATION_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CARTONIZATION_ADD (装箱插入触发器)';

-- CARTONIZATION表的更新触发器 (从ntrCartonizationUpdate转换)
CREATE OR REPLACE TRIGGER NTR_CARTONIZATION_UPDATE
BEFORE UPDATE ON CARTONIZATION
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CARTONIZATION_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CARTONIZATION_UPDATE (装箱更新触发器)';

-- CARTONIZATION表的删除触发器 (从ntrCartonizationDelete转换)
CREATE OR REPLACE TRIGGER NTR_CARTONIZATION_DELETE
AFTER DELETE ON CARTONIZATION
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'CARTONIZATION',
        'RECORD_DELETE',
        'Cartonization deleted: ' || :OLD.CartonizationKey || ' Order: ' || :OLD.OrderKey,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CARTONIZATION_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CARTONIZATION_DELETE (装箱删除触发器)';

-- =============================================
-- MOVE管理触发器 (从SQL Server转换)
-- =============================================

-- MOVE表的插入触发器 (从ntrMoveAdd转换)
CREATE OR REPLACE TRIGGER NTR_MOVE_ADD
BEFORE INSERT ON MOVE
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成移动键如果为空
    IF :NEW.MoveKey IS NULL OR TRIM(:NEW.MoveKey) = ' ' THEN
        SELECT 'MV' || LPAD(TO_CHAR(SEQ_MOVE.NEXTVAL), 8, '0') INTO :NEW.MoveKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MOVE_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MOVE_ADD (移动插入触发器)';

-- MOVE表的更新触发器 (从ntrMoveUpdate转换)
CREATE OR REPLACE TRIGGER NTR_MOVE_UPDATE
BEFORE UPDATE ON MOVE
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
    -- 状态变更日志
    IF :OLD.Status != :NEW.Status THEN
        INSERT INTO ALERT (
            AlertKey, ModuleName, AlertType, AlertMessage, 
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
            'MOVE',
            'STATUS_CHANGE',
            'Move ' || :NEW.MoveKey || ' status changed from ' || :OLD.Status || ' to ' || :NEW.Status,
            SYSDATE, USER, SYSDATE, USER
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MOVE_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MOVE_UPDATE (移动更新触发器)';

-- MOVE表的删除触发器 (从ntrMoveDelete转换)
CREATE OR REPLACE TRIGGER NTR_MOVE_DELETE
AFTER DELETE ON MOVE
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'MOVE',
        'RECORD_DELETE',
        'Move deleted: ' || :OLD.MoveKey || ' From: ' || :OLD.FromLoc || ' To: ' || :OLD.ToLoc,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MOVE_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MOVE_DELETE (移动删除触发器)';

-- =============================================
-- TRANSFER管理触发器 (从SQL Server转换)
-- =============================================

-- TRANSFER表的插入触发器 (从ntrTransferAdd转换)
CREATE OR REPLACE TRIGGER NTR_TRANSFER_ADD
BEFORE INSERT ON TRANSFER
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;

    -- 生成转移键如果为空
    IF :NEW.TransferKey IS NULL OR TRIM(:NEW.TransferKey) = ' ' THEN
        SELECT 'TF' || LPAD(TO_CHAR(SEQ_TRANSFER.NEXTVAL), 8, '0') INTO :NEW.TransferKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_TRANSFER_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_TRANSFER_ADD (转移插入触发器)';

-- TRANSFER表的更新触发器 (从ntrTransferUpdate转换)
CREATE OR REPLACE TRIGGER NTR_TRANSFER_UPDATE
BEFORE UPDATE ON TRANSFER
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

    -- 状态变更日志
    IF :OLD.Status != :NEW.Status THEN
        INSERT INTO ALERT (
            AlertKey, ModuleName, AlertType, AlertMessage,
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
            'TRANSFER',
            'STATUS_CHANGE',
            'Transfer ' || :NEW.TransferKey || ' status changed from ' || :OLD.Status || ' to ' || :NEW.Status,
            SYSDATE, USER, SYSDATE, USER
        );
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_TRANSFER_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_TRANSFER_UPDATE (转移更新触发器)';

-- TRANSFER表的删除触发器 (从ntrTransferDelete转换)
CREATE OR REPLACE TRIGGER NTR_TRANSFER_DELETE
AFTER DELETE ON TRANSFER
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'TRANSFER',
        'RECORD_DELETE',
        'Transfer deleted: ' || :OLD.TransferKey || ' From: ' || :OLD.FromStorerKey || ' To: ' || :OLD.ToStorerKey,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_TRANSFER_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_TRANSFER_DELETE (转移删除触发器)';

-- =============================================
-- 其他重要触发器 (从SQL Server转换)
-- =============================================

-- REPLENISHMENT表的插入触发器 (从ntrReplenishmentAdd转换)
CREATE OR REPLACE TRIGGER NTR_REPLENISHMENT_ADD
BEFORE INSERT ON REPLENISHMENT
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;

    IF :NEW.Priority IS NULL THEN
        :NEW.Priority := '5';
    END IF;

    -- 生成补货键如果为空
    IF :NEW.ReplenishmentKey IS NULL OR TRIM(:NEW.ReplenishmentKey) = ' ' THEN
        SELECT 'RPL' || LPAD(TO_CHAR(SEQ_REPLENISHMENT.NEXTVAL), 7, '0') INTO :NEW.ReplenishmentKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_REPLENISHMENT_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_REPLENISHMENT_ADD (补货插入触发器)';

-- REPLENISHMENT表的更新触发器 (从ntrReplenishmentUpdate转换)
CREATE OR REPLACE TRIGGER NTR_REPLENISHMENT_UPDATE
BEFORE UPDATE ON REPLENISHMENT
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

    -- 如果状态变为完成，设置完成时间
    IF :NEW.Status = '9' AND :OLD.Status != '9' THEN
        :NEW.CompletedDate := SYSDATE;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_REPLENISHMENT_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_REPLENISHMENT_UPDATE (补货更新触发器)';

-- REPLENISHMENT表的删除触发器 (从ntrReplenishmentDelete转换)
CREATE OR REPLACE TRIGGER NTR_REPLENISHMENT_DELETE
AFTER DELETE ON REPLENISHMENT
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'REPLENISHMENT',
        'RECORD_DELETE',
        'Replenishment deleted: ' || :OLD.ReplenishmentKey || ' From: ' || :OLD.FromLoc || ' To: ' || :OLD.ToLoc,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_REPLENISHMENT_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_REPLENISHMENT_DELETE (补货删除触发器)';

-- =============================================
-- 创建必要的序列 (支持最终触发器功能)
-- =============================================

-- 创建序列（如果不存在）
DECLARE
    v_count NUMBER;
    TYPE sequence_list_type IS TABLE OF VARCHAR2(30);
    sequences sequence_list_type := sequence_list_type(
        'SEQ_CARTONIZATION', 'SEQ_MOVE', 'SEQ_TRANSFER', 'SEQ_REPLENISHMENT',
        'SEQ_PO', 'SEQ_PODETAIL', 'SEQ_ADJUSTMENT', 'SEQ_ADJUSTMENTDETAIL',
        'SEQ_CONTAINER', 'SEQ_CC', 'SEQ_CCDETAIL', 'SEQ_TASKDETAIL',
        'SEQ_LOAD', 'SEQ_LOADDETAIL', 'SEQ_ASN', 'SEQ_ASNDETAIL'
    );
BEGIN
    FOR i IN 1..sequences.COUNT LOOP
        SELECT COUNT(*) INTO v_count
        FROM USER_SEQUENCES
        WHERE SEQUENCE_NAME = sequences(i);

        IF v_count = 0 THEN
            EXECUTE IMMEDIATE 'CREATE SEQUENCE ' || sequences(i) || ' START WITH 1 INCREMENT BY 1';
            DBMS_OUTPUT.PUT_LINE('>>> 已创建序列 ' || sequences(i));
        END IF;
    END LOOP;
END;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle最终触发器脚本执行完成';
PROMPT '>>> 已创建LOT管理、装箱管理、移动管理、转移管理和补货管理触发器 (15个)';
PROMPT '>>> 所有最终触发器创建完成！';
