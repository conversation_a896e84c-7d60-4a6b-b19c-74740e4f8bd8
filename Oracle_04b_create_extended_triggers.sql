-- =============================================
-- Oracle版本 - 创建扩展触发器脚本 (第二部分)
-- 功能：创建仓库管理系统的扩展触发器集合 (Oracle版本)
-- 用途：PO管理、调整管理、容器管理、盘点管理等高级业务触发器
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中的108个SQL Server触发器转换为Oracle语法 (扩展部分)
-- 包含：PO管理、调整管理、容器管理、盘点管理、任务管理等触发器
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- PO管理触发器 (从SQL Server转换)
-- =============================================

-- PO表的插入触发器 (从ntrPOHeaderAdd转换)
CREATE OR REPLACE TRIGGER NTR_PO_ADD
BEFORE INSERT ON PO
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    IF :NEW.Type IS NULL THEN
        :NEW.Type := 'PO';
    END IF;
    
    -- 生成PO键如果为空
    IF :NEW.POKey IS NULL OR TRIM(:NEW.POKey) = ' ' THEN
        SELECT 'PO' || LPAD(TO_CHAR(SEQ_PO.NEXTVAL), 8, '0') INTO :NEW.POKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PO_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PO_ADD (PO插入触发器)';

-- PO表的更新触发器 (从ntrPOHeaderUpdate转换)
CREATE OR REPLACE TRIGGER NTR_PO_UPDATE
BEFORE UPDATE ON PO
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
    -- 状态变更日志
    IF :OLD.Status != :NEW.Status THEN
        INSERT INTO ALERT (
            AlertKey, ModuleName, AlertType, AlertMessage, 
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
            'PO',
            'STATUS_CHANGE',
            'PO ' || :NEW.POKey || ' status changed from ' || :OLD.Status || ' to ' || :NEW.Status,
            SYSDATE, USER, SYSDATE, USER
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PO_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PO_UPDATE (PO更新触发器)';

-- PO表的删除触发器 (从ntrPOHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_PO_DELETE
BEFORE DELETE ON PO
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有PO明细
    SELECT COUNT(*) INTO v_cnt
    FROM PODETAIL
    WHERE POKey = :OLD.POKey;
    
    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20006, 'Cannot delete PO with existing PO details');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PO_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PO_DELETE (PO删除触发器)';

-- PODETAIL表的插入触发器 (从ntrPODetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_PODETAIL_ADD
BEFORE INSERT ON PODETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    IF :NEW.QtyReceived IS NULL THEN
        :NEW.QtyReceived := 0;
    END IF;
    
    -- 生成PO明细键如果为空
    IF :NEW.PODetailKey IS NULL OR TRIM(:NEW.PODetailKey) = ' ' THEN
        SELECT 'POD' || LPAD(TO_CHAR(SEQ_PODETAIL.NEXTVAL), 8, '0') INTO :NEW.PODetailKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PODETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PODETAIL_ADD (PO明细插入触发器)';

-- PODETAIL表的更新触发器 (从ntrPODetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_PODETAIL_UPDATE
BEFORE UPDATE ON PODETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
    -- 根据收货情况更新状态
    IF :OLD.QtyReceived != :NEW.QtyReceived THEN
        :NEW.Status := CASE 
            WHEN :NEW.QtyReceived >= :NEW.Qty THEN '9'
            ELSE '0'
        END;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PODETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PODETAIL_UPDATE (PO明细更新触发器)';

-- PODETAIL表的删除触发器 (从ntrPODetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_PODETAIL_DELETE
AFTER DELETE ON PODETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'PODETAIL',
        'RECORD_DELETE',
        'PO Detail deleted: ' || :OLD.POKey || '/' || :OLD.POLineNumber,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PODETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PODETAIL_DELETE (PO明细删除触发器)';

-- =============================================
-- 调整管理触发器 (从SQL Server转换)
-- =============================================

-- ADJUSTMENT表的插入触发器 (从ntrAdjustmentHeaderAdd转换)
CREATE OR REPLACE TRIGGER NTR_ADJUSTMENT_ADD
BEFORE INSERT ON ADJUSTMENT
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成调整键如果为空
    IF :NEW.AdjustmentKey IS NULL OR TRIM(:NEW.AdjustmentKey) = ' ' THEN
        SELECT 'ADJ' || LPAD(TO_CHAR(SEQ_ADJUSTMENT.NEXTVAL), 7, '0') INTO :NEW.AdjustmentKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ADJUSTMENT_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ADJUSTMENT_ADD (调整插入触发器)';

-- ADJUSTMENT表的更新触发器 (从ntrAdjustmentHeaderUpdate转换)
CREATE OR REPLACE TRIGGER NTR_ADJUSTMENT_UPDATE
BEFORE UPDATE ON ADJUSTMENT
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ADJUSTMENT_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ADJUSTMENT_UPDATE (调整更新触发器)';

-- ADJUSTMENT表的删除触发器 (从ntrAdjustmentHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_ADJUSTMENT_DELETE
BEFORE DELETE ON ADJUSTMENT
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有调整明细
    SELECT COUNT(*) INTO v_cnt
    FROM ADJUSTMENTDETAIL
    WHERE AdjustmentKey = :OLD.AdjustmentKey;
    
    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20007, 'Cannot delete adjustment with existing adjustment details');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ADJUSTMENT_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ADJUSTMENT_DELETE (调整删除触发器)';

-- ADJUSTMENTDETAIL表的插入触发器 (从ntrAdjustmentDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_ADJUSTMENTDETAIL_ADD
BEFORE INSERT ON ADJUSTMENTDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;

    -- 生成调整明细键如果为空
    IF :NEW.AdjustmentDetailKey IS NULL OR TRIM(:NEW.AdjustmentDetailKey) = ' ' THEN
        SELECT 'ADJD' || LPAD(TO_CHAR(SEQ_ADJUSTMENTDETAIL.NEXTVAL), 6, '0') INTO :NEW.AdjustmentDetailKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ADJUSTMENTDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ADJUSTMENTDETAIL_ADD (调整明细插入触发器)';

-- ADJUSTMENTDETAIL表的更新触发器 (从ntrAdjustmentDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_ADJUSTMENTDETAIL_UPDATE
BEFORE UPDATE ON ADJUSTMENTDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ADJUSTMENTDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ADJUSTMENTDETAIL_UPDATE (调整明细更新触发器)';

-- ADJUSTMENTDETAIL表的删除触发器 (从ntrAdjustmentDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_ADJUSTMENTDETAIL_DELETE
AFTER DELETE ON ADJUSTMENTDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'ADJUSTMENTDETAIL',
        'RECORD_DELETE',
        'Adjustment Detail deleted: ' || :OLD.AdjustmentKey || '/' || :OLD.AdjustmentLineNumber,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_ADJUSTMENTDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_ADJUSTMENTDETAIL_DELETE (调整明细删除触发器)';

-- =============================================
-- 容器管理触发器 (从SQL Server转换)
-- =============================================

-- CONTAINER表的插入触发器 (从ntrContainerHeaderAdd转换)
CREATE OR REPLACE TRIGGER NTR_CONTAINER_ADD
BEFORE INSERT ON CONTAINER
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;

    -- 生成容器键如果为空
    IF :NEW.ContainerKey IS NULL OR TRIM(:NEW.ContainerKey) = ' ' THEN
        SELECT 'CNT' || LPAD(TO_CHAR(SEQ_CONTAINER.NEXTVAL), 7, '0') INTO :NEW.ContainerKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CONTAINER_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CONTAINER_ADD (容器插入触发器)';

-- CONTAINER表的更新触发器 (从ntrContainerHeaderUpdate转换)
CREATE OR REPLACE TRIGGER NTR_CONTAINER_UPDATE
BEFORE UPDATE ON CONTAINER
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CONTAINER_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CONTAINER_UPDATE (容器更新触发器)';

-- CONTAINER表的删除触发器 (从ntrContainerHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_CONTAINER_DELETE
BEFORE DELETE ON CONTAINER
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有容器明细
    SELECT COUNT(*) INTO v_cnt
    FROM CONTAINERDETAIL
    WHERE ContainerKey = :OLD.ContainerKey;

    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20008, 'Cannot delete container with existing container details');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CONTAINER_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CONTAINER_DELETE (容器删除触发器)';

-- 提交事务
COMMIT;

PROMPT '>>> Oracle扩展触发器脚本第一部分执行完成';
PROMPT '>>> 已创建PO管理、调整管理和容器管理触发器 (15个)';
PROMPT '>>> 请继续执行后续脚本以创建更多触发器';
