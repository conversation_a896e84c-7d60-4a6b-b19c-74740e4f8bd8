-- =============================================
-- Oracle版本 - 创建订单和发货管理存储过程脚本
-- 功能：创建仓库管理系统的订单和发货管理相关存储过程 (Oracle版本)
-- 用途：订单处理、发货确认、装载管理、运输管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中提取的订单和发货管理存储过程转换为Oracle语法
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 订单处理存储过程
-- =============================================

-- nspOrderCreate 存储过程 (创建订单)
CREATE OR REPLACE PROCEDURE nspOrderCreate (
    p_externorderkey    IN CHAR,
    p_storerkey         IN CHAR,
    p_consigneekey      IN CHAR,
    p_carrierkey        IN CHAR,
    p_orderdate         IN DATE,
    p_requestedshipdate IN DATE,
    p_orderkey          OUT CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 检查外部订单键是否已存在
    SELECT COUNT(*) INTO v_cnt
    FROM ORDERS
    WHERE ExternOrderKey = p_externorderkey AND StorerKey = p_storerkey;
    
    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 79001;
        p_errmsg := 'NSQL79001: External order key already exists';
        RETURN;
    END IF;
    
    -- 获取订单键
    nspg_getkey('OrderKey', 10, p_orderkey, p_success, p_err, p_errmsg);
    
    IF p_success = 1 THEN
        -- 创建订单头
        INSERT INTO ORDERS (
            OrderKey, ExternOrderKey, StorerKey, ConsigneeKey, CarrierKey,
            OrderDate, RequestedShipDate, Status, Type, Priority,
            EffectiveDate, AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            p_orderkey, p_externorderkey, p_storerkey, p_consigneekey, p_carrierkey,
            p_orderdate, p_requestedshipdate, '0', 'SO', '5',
            v_currentdatetime, v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
        );
        
        COMMIT;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspOrderCreate');
        ROLLBACK;
END nspOrderCreate;
/

-- nspOrderAddDetail 存储过程 (添加订单明细)
CREATE OR REPLACE PROCEDURE nspOrderAddDetail (
    p_orderkey          IN CHAR,
    p_orderlinenumber   IN CHAR,
    p_externlinenum     IN CHAR,
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_qty               IN NUMBER,
    p_uom               IN CHAR,
    p_packkey           IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
    v_orderdetailkey CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 检查订单是否存在
    SELECT COUNT(*) INTO v_cnt
    FROM ORDERS
    WHERE OrderKey = p_orderkey;
    
    IF v_cnt = 0 THEN
        p_success := 0;
        p_err := 79002;
        p_errmsg := 'NSQL79002: Order not found';
        RETURN;
    END IF;
    
    -- 检查订单行是否已存在
    SELECT COUNT(*) INTO v_cnt
    FROM ORDERDETAIL
    WHERE OrderKey = p_orderkey AND OrderLineNumber = p_orderlinenumber;
    
    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 79003;
        p_errmsg := 'NSQL79003: Order line already exists';
        RETURN;
    END IF;
    
    -- 获取订单明细键
    nspg_getkey('OrderDetailKey', 10, v_orderdetailkey, p_success, p_err, p_errmsg);
    
    IF p_success = 1 THEN
        -- 创建订单明细
        INSERT INTO ORDERDETAIL (
            OrderDetailKey, OrderKey, OrderLineNumber, ExternLineNumber,
            StorerKey, Sku, Qty, OpenQty, ShippedQty, UOM, PackKey,
            Status, EffectiveDate, AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            v_orderdetailkey, p_orderkey, p_orderlinenumber, p_externlinenum,
            p_storerkey, p_sku, p_qty, p_qty, 0, p_uom, p_packkey,
            '0', v_currentdatetime, v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
        );
        
        COMMIT;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspOrderAddDetail');
        ROLLBACK;
END nspOrderAddDetail;
/

-- nspOrderCancel 存储过程 (取消订单)
CREATE OR REPLACE PROCEDURE nspOrderCancel (
    p_orderkey          IN CHAR,
    p_cancelreason      IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
    v_status CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 检查订单是否存在并获取状态
    BEGIN
        SELECT Status INTO v_status
        FROM ORDERS
        WHERE OrderKey = p_orderkey;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 79004;
            p_errmsg := 'NSQL79004: Order not found';
            RETURN;
    END;
    
    -- 检查订单是否可以取消
    IF v_status IN ('9', 'C') THEN
        p_success := 0;
        p_err := 79005;
        p_errmsg := 'NSQL79005: Order cannot be cancelled (already shipped or cancelled)';
        RETURN;
    END IF;
    
    -- 释放已分配的库存
    UPDATE LOTxLOCxID
    SET QtyAllocated = QtyAllocated - (
        SELECT NVL(SUM(pd.Qty), 0)
        FROM PICKDETAIL pd
        JOIN PICKHEADER ph ON pd.PickHeaderKey = ph.PickHeaderKey
        WHERE ph.OrderKey = p_orderkey
        AND pd.StorerKey = LOTxLOCxID.StorerKey
        AND pd.Sku = LOTxLOCxID.Sku
        AND pd.Lot = LOTxLOCxID.Lot
        AND pd.Loc = LOTxLOCxID.Loc
        AND pd.ID = LOTxLOCxID.ID
        AND pd.Status != '9'
    ),
    EditDate = v_currentdatetime,
    EditWho = v_currentuser
    WHERE EXISTS (
        SELECT 1 FROM PICKDETAIL pd
        JOIN PICKHEADER ph ON pd.PickHeaderKey = ph.PickHeaderKey
        WHERE ph.OrderKey = p_orderkey
        AND pd.StorerKey = LOTxLOCxID.StorerKey
        AND pd.Sku = LOTxLOCxID.Sku
        AND pd.Lot = LOTxLOCxID.Lot
        AND pd.Loc = LOTxLOCxID.Loc
        AND pd.ID = LOTxLOCxID.ID
        AND pd.Status != '9'
    );
    
    -- 取消拣选明细
    UPDATE PICKDETAIL
    SET Status = 'C', -- 取消
        EditDate = v_currentdatetime,
        EditWho = v_currentuser
    WHERE PickHeaderKey IN (
        SELECT PickHeaderKey FROM PICKHEADER WHERE OrderKey = p_orderkey
    )
    AND Status != '9';
    
    -- 取消拣选头
    UPDATE PICKHEADER
    SET Status = 'C', -- 取消
        EditDate = v_currentdatetime,
        EditWho = v_currentuser
    WHERE OrderKey = p_orderkey
    AND Status != '9';
    
    -- 取消订单明细
    UPDATE ORDERDETAIL
    SET Status = 'C', -- 取消
        CancelReason = p_cancelreason,
        EditDate = v_currentdatetime,
        EditWho = v_currentuser
    WHERE OrderKey = p_orderkey
    AND Status != '9';
    
    -- 取消订单头
    UPDATE ORDERS
    SET Status = 'C', -- 取消
        CancelReason = p_cancelreason,
        CancelDate = v_currentdatetime,
        CancelWho = v_currentuser,
        EditDate = v_currentdatetime,
        EditWho = v_currentuser
    WHERE OrderKey = p_orderkey;
    
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspOrderCancel');
        ROLLBACK;
END nspOrderCancel;
/

-- =============================================
-- 发货处理存储过程
-- =============================================

-- nspShipConfirm 存储过程 (发货确认)
CREATE OR REPLACE PROCEDURE nspShipConfirm (
    p_orderkey          IN CHAR,
    p_carrierkey        IN CHAR,
    p_trailerkey        IN CHAR,
    p_shipdate          IN DATE,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
    v_status CHAR(10);
    v_totalqty NUMBER;
    v_shippedqty NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查订单是否存在并获取状态
    BEGIN
        SELECT Status INTO v_status
        FROM ORDERS
        WHERE OrderKey = p_orderkey;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 80001;
            p_errmsg := 'NSQL80001: Order not found';
            RETURN;
    END;

    -- 检查订单是否可以发货
    IF v_status NOT IN ('1', '2') THEN -- 已分配或部分拣选
        p_success := 0;
        p_err := 80002;
        p_errmsg := 'NSQL80002: Order not ready for shipping';
        RETURN;
    END IF;

    -- 检查是否所有拣选都已完成
    SELECT COUNT(*) INTO v_cnt
    FROM PICKDETAIL pd
    JOIN PICKHEADER ph ON pd.PickHeaderKey = ph.PickHeaderKey
    WHERE ph.OrderKey = p_orderkey
    AND pd.Status != '9'; -- 未完成

    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 80003;
        p_errmsg := 'NSQL80003: Not all picks are completed';
        RETURN;
    END IF;

    -- 更新订单明细的发货数量
    UPDATE ORDERDETAIL od
    SET ShippedQty = (
        SELECT NVL(SUM(pd.QtyPicked), 0)
        FROM PICKDETAIL pd
        JOIN PICKHEADER ph ON pd.PickHeaderKey = ph.PickHeaderKey
        WHERE ph.OrderKey = od.OrderKey
        AND pd.StorerKey = od.StorerKey
        AND pd.Sku = od.Sku
        AND pd.Status = '9'
    ),
    OpenQty = 0,
    Status = '9', -- 已发货
    EditDate = v_currentdatetime,
    EditWho = v_currentuser
    WHERE OrderKey = p_orderkey;

    -- 更新订单头
    UPDATE ORDERS
    SET Status = '9', -- 已发货
        CarrierKey = NVL(p_carrierkey, CarrierKey),
        TrailerKey = p_trailerkey,
        ShipDate = NVL(p_shipdate, v_currentdatetime),
        EditDate = v_currentdatetime,
        EditWho = v_currentuser
    WHERE OrderKey = p_orderkey;

    -- 从库存中减少已拣选的数量
    UPDATE LOTxLOCxID
    SET Qty = Qty - QtyPicked,
        QtyPicked = 0,
        EditDate = v_currentdatetime,
        EditWho = v_currentuser
    WHERE EXISTS (
        SELECT 1 FROM PICKDETAIL pd
        JOIN PICKHEADER ph ON pd.PickHeaderKey = ph.PickHeaderKey
        WHERE ph.OrderKey = p_orderkey
        AND pd.StorerKey = LOTxLOCxID.StorerKey
        AND pd.Sku = LOTxLOCxID.Sku
        AND pd.Lot = LOTxLOCxID.Lot
        AND pd.Loc = LOTxLOCxID.Loc
        AND pd.ID = LOTxLOCxID.ID
        AND pd.Status = '9'
    );

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspShipConfirm');
        ROLLBACK;
END nspShipConfirm;
/

-- nspLoadCreate 存储过程 (创建装载)
CREATE OR REPLACE PROCEDURE nspLoadCreate (
    p_externloadkey     IN CHAR,
    p_carrierkey        IN CHAR,
    p_trailerkey        IN CHAR,
    p_loaddate          IN DATE,
    p_loadkey           OUT CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查外部装载键是否已存在
    SELECT COUNT(*) INTO v_cnt
    FROM LOAD
    WHERE ExternLoadKey = p_externloadkey;

    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 80004;
        p_errmsg := 'NSQL80004: External load key already exists';
        RETURN;
    END IF;

    -- 获取装载键
    nspg_getkey('LoadKey', 10, p_loadkey, p_success, p_err, p_errmsg);

    IF p_success = 1 THEN
        -- 创建装载头
        INSERT INTO LOAD (
            LoadKey, ExternLoadKey, CarrierKey, TrailerKey,
            LoadDate, Status, EffectiveDate,
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            p_loadkey, p_externloadkey, p_carrierkey, p_trailerkey,
            p_loaddate, '0', v_currentdatetime,
            v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
        );

        COMMIT;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspLoadCreate');
        ROLLBACK;
END nspLoadCreate;
/

-- nspLoadAddOrder 存储过程 (添加订单到装载)
CREATE OR REPLACE PROCEDURE nspLoadAddOrder (
    p_loadkey           IN CHAR,
    p_orderkey          IN CHAR,
    p_stop              IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
    v_loaddetailkey CHAR(10);
    v_orderstatus CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查装载是否存在
    SELECT COUNT(*) INTO v_cnt
    FROM LOAD
    WHERE LoadKey = p_loadkey;

    IF v_cnt = 0 THEN
        p_success := 0;
        p_err := 80005;
        p_errmsg := 'NSQL80005: Load not found';
        RETURN;
    END IF;

    -- 检查订单是否存在并获取状态
    BEGIN
        SELECT Status INTO v_orderstatus
        FROM ORDERS
        WHERE OrderKey = p_orderkey;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 80006;
            p_errmsg := 'NSQL80006: Order not found';
            RETURN;
    END;

    -- 检查订单是否已发货
    IF v_orderstatus != '9' THEN
        p_success := 0;
        p_err := 80007;
        p_errmsg := 'NSQL80007: Order not shipped yet';
        RETURN;
    END IF;

    -- 检查订单是否已在装载中
    SELECT COUNT(*) INTO v_cnt
    FROM LOADDETAIL
    WHERE OrderKey = p_orderkey;

    IF v_cnt > 0 THEN
        p_success := 0;
        p_err := 80008;
        p_errmsg := 'NSQL80008: Order already in a load';
        RETURN;
    END IF;

    -- 获取装载明细键
    nspg_getkey('LoadDetailKey', 10, v_loaddetailkey, p_success, p_err, p_errmsg);

    IF p_success = 1 THEN
        -- 添加订单到装载
        INSERT INTO LOADDETAIL (
            LoadDetailKey, LoadKey, OrderKey, Stop,
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            v_loaddetailkey, p_loadkey, p_orderkey, p_stop,
            v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
        );

        -- 更新订单状态为已装载
        UPDATE ORDERS
        SET LoadKey = p_loadkey,
            Stop = p_stop,
            EditDate = v_currentdatetime,
            EditWho = v_currentuser
        WHERE OrderKey = p_orderkey;

        COMMIT;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspLoadAddOrder');
        ROLLBACK;
END nspLoadAddOrder;
/

-- nspLoadConfirm 存储过程 (确认装载)
CREATE OR REPLACE PROCEDURE nspLoadConfirm (
    p_loadkey           IN CHAR,
    p_departuredate     IN DATE,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
    v_cnt NUMBER;
    v_status CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查装载是否存在并获取状态
    BEGIN
        SELECT Status INTO v_status
        FROM LOAD
        WHERE LoadKey = p_loadkey;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 80009;
            p_errmsg := 'NSQL80009: Load not found';
            RETURN;
    END;

    -- 检查装载是否可以确认
    IF v_status != '0' THEN
        p_success := 0;
        p_err := 80010;
        p_errmsg := 'NSQL80010: Load already confirmed or cancelled';
        RETURN;
    END IF;

    -- 检查装载是否有订单
    SELECT COUNT(*) INTO v_cnt
    FROM LOADDETAIL
    WHERE LoadKey = p_loadkey;

    IF v_cnt = 0 THEN
        p_success := 0;
        p_err := 80011;
        p_errmsg := 'NSQL80011: Load has no orders';
        RETURN;
    END IF;

    -- 确认装载
    UPDATE LOAD
    SET Status = '9', -- 已确认
        DepartureDate = NVL(p_departuredate, v_currentdatetime),
        EditDate = v_currentdatetime,
        EditWho = v_currentuser
    WHERE LoadKey = p_loadkey;

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspLoadConfirm');
        ROLLBACK;
END nspLoadConfirm;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle订单和发货管理存储过程脚本执行完成';
PROMPT '>>> 已创建完整的订单和发货管理存储过程集合 (7个)';
PROMPT '>>> 包含：订单创建、订单明细、订单取消、发货确认、装载管理等功能';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 订单和发货管理存储过程创建完成！';
PROMPT '总订单发货存储过程数量：7个专业存储过程';
PROMPT '功能覆盖：';
PROMPT '- 订单创建和管理';
PROMPT '- 订单明细处理';
PROMPT '- 订单取消处理';
PROMPT '- 发货确认';
PROMPT '- 装载创建和管理';
PROMPT '- 装载订单分配';
PROMPT '- 装载确认';
PROMPT '=============================================';
