-- =============================================
-- 数据库对象统计脚本
-- 功能：统计仓库管理系统数据库中的所有对象
-- 用途：验证数据库重构完成情况
-- =============================================

PRINT '=========================================='
PRINT '数据库对象统计报告'
PRINT '=========================================='
PRINT ''

-- =============================================
-- 统计表数量
-- =============================================
DECLARE @table_count int
SELECT @table_count = COUNT(*)
FROM sysobjects 
WHERE type = 'U' 
AND name NOT LIKE 'sys%'
AND name NOT LIKE 'dt%'

PRINT '表 (Tables): ' + CONVERT(varchar(10), @table_count)

-- 列出所有表名
PRINT ''
PRINT '表列表:'
PRINT '----------------------------------------'
SELECT '  ' + name AS TableName
FROM sysobjects 
WHERE type = 'U' 
AND name NOT LIKE 'sys%'
AND name NOT LIKE 'dt%'
ORDER BY name

-- =============================================
-- 统计视图数量
-- =============================================
DECLARE @view_count int
SELECT @view_count = COUNT(*)
FROM sysobjects 
WHERE type = 'V'
AND name NOT LIKE 'sys%'

PRINT ''
PRINT '视图 (Views): ' + CONVERT(varchar(10), @view_count)

-- 列出所有视图名
IF @view_count > 0
BEGIN
PRINT ''
PRINT '视图列表:'
PRINT '----------------------------------------'
SELECT '  ' + name AS ViewName
FROM sysobjects 
WHERE type = 'V'
AND name NOT LIKE 'sys%'
ORDER BY name
END

-- =============================================
-- 统计存储过程数量
-- =============================================
DECLARE @proc_count int
SELECT @proc_count = COUNT(*)
FROM sysobjects 
WHERE type = 'P'
AND name NOT LIKE 'sys%'
AND name NOT LIKE 'dt%'
AND name NOT LIKE 'sp_%'

PRINT ''
PRINT '存储过程 (Stored Procedures): ' + CONVERT(varchar(10), @proc_count)

-- 列出所有存储过程名
IF @proc_count > 0
BEGIN
PRINT ''
PRINT '存储过程列表:'
PRINT '----------------------------------------'
SELECT '  ' + name AS ProcedureName
FROM sysobjects 
WHERE type = 'P'
AND name NOT LIKE 'sys%'
AND name NOT LIKE 'dt%'
AND name NOT LIKE 'sp_%'
ORDER BY name
END

-- =============================================
-- 统计触发器数量
-- =============================================
DECLARE @trigger_count int
SELECT @trigger_count = COUNT(*)
FROM sysobjects 
WHERE type = 'TR'

PRINT ''
PRINT '触发器 (Triggers): ' + CONVERT(varchar(10), @trigger_count)

-- 列出所有触发器名
IF @trigger_count > 0
BEGIN
PRINT ''
PRINT '触发器列表:'
PRINT '----------------------------------------'
SELECT '  ' + t.name + ' (on ' + o.name + ')' AS TriggerName
FROM sysobjects t
INNER JOIN sysobjects o ON t.parent_obj = o.id
WHERE t.type = 'TR'
ORDER BY t.name
END

-- =============================================
-- 统计索引数量
-- =============================================
DECLARE @index_count int
SELECT @index_count = COUNT(*)
FROM sysindexes i
INNER JOIN sysobjects o ON i.id = o.id
WHERE o.type = 'U'
AND i.indid > 0
AND i.indid < 255
AND o.name NOT LIKE 'sys%'

PRINT ''
PRINT '索引 (Indexes): ' + CONVERT(varchar(10), @index_count)

-- =============================================
-- 统计约束数量
-- =============================================
DECLARE @constraint_count int
SELECT @constraint_count = COUNT(*)
FROM sysobjects 
WHERE type IN ('C', 'F', 'PK', 'UQ')

PRINT ''
PRINT '约束 (Constraints): ' + CONVERT(varchar(10), @constraint_count)

-- 分类统计约束
DECLARE @pk_count int, @fk_count int, @check_count int, @unique_count int

SELECT @pk_count = COUNT(*) FROM sysobjects WHERE type = 'PK'
SELECT @fk_count = COUNT(*) FROM sysobjects WHERE type = 'F'
SELECT @check_count = COUNT(*) FROM sysobjects WHERE type = 'C'
SELECT @unique_count = COUNT(*) FROM sysobjects WHERE type = 'UQ'

PRINT '  主键约束 (Primary Keys): ' + CONVERT(varchar(10), @pk_count)
PRINT '  外键约束 (Foreign Keys): ' + CONVERT(varchar(10), @fk_count)
PRINT '  检查约束 (Check Constraints): ' + CONVERT(varchar(10), @check_count)
PRINT '  唯一约束 (Unique Constraints): ' + CONVERT(varchar(10), @unique_count)

-- =============================================
-- 核心业务表验证
-- =============================================
PRINT ''
PRINT '=========================================='
PRINT '核心业务表验证'
PRINT '=========================================='

DECLARE @core_tables TABLE (
    TableName varchar(50),
    Description varchar(100)
)

INSERT INTO @core_tables VALUES
('ITRN', '库存事务表'),
('SKU', '库存单位表'),
('LOT', '批次表'),
('LOC', '位置表'),
('LOTATTRIBUTE', '批次属性表'),
('LOTxLOCxID', '批次位置标识关联表'),
('STORER', '存储商表'),
('ORDERS', '订单表'),
('ORDERDETAIL', '订单明细表'),
('PICKHEADER', '拣选头表'),
('PICKDETAIL', '拣选明细表'),
('RECEIPT', '收货表'),
('RECEIPTDETAIL', '收货明细表'),
('PO', '采购订单表'),
('PODETAIL', '采购订单明细表'),
('SKUxLOC', 'SKU位置关联表'),
('PACK', '包装表'),
('WAVE', '波次表'),
('CARTONIZATION', '装箱表'),
('ADJUSTMENT', '调整表'),
('ADJUSTMENTDETAIL', '调整明细表'),
('PALLET', '托盘表'),
('PALLETDETAIL', '托盘明细表'),
('CONTAINER', '容器表'),
('CONTAINERDETAIL', '容器明细表'),
('REPLENISHMENT', '补货表'),
('ALERT', '警报表'),
('ERRLOG', '错误日志表'),
('CODELIST', '代码列表表'),
('CODELKUP', '代码查找表'),
('NSQLCONFIG', '系统配置表'),
('NCOUNTER', '计数器表'),
('NCOUNTERITRN', '事务计数器表'),
('NCOUNTERPICK', '拣选计数器表'),
('IDSTACK', 'ID堆栈表'),
('ID', '标识符表')

DECLARE @missing_count int
SELECT @missing_count = 0

DECLARE @table_name varchar(50), @description varchar(100)
DECLARE table_cursor CURSOR FOR
SELECT TableName, Description FROM @core_tables

OPEN table_cursor
FETCH NEXT FROM table_cursor INTO @table_name, @description

WHILE @@FETCH_STATUS = 0
BEGIN
    IF OBJECT_ID(@table_name) IS NOT NULL
        PRINT '✓ ' + @table_name + ' - ' + @description
    ELSE
    BEGIN
        PRINT '✗ ' + @table_name + ' - ' + @description + ' (缺失)'
        SELECT @missing_count = @missing_count + 1
    END
    
    FETCH NEXT FROM table_cursor INTO @table_name, @description
END

CLOSE table_cursor
DEALLOCATE table_cursor

-- =============================================
-- 总结报告
-- =============================================
PRINT ''
PRINT '=========================================='
PRINT '总结报告'
PRINT '=========================================='

DECLARE @total_objects int
SELECT @total_objects = @table_count + @view_count + @proc_count + @trigger_count + @constraint_count

PRINT '总对象数量: ' + CONVERT(varchar(10), @total_objects)
PRINT '核心表缺失数量: ' + CONVERT(varchar(10), @missing_count)

IF @missing_count = 0
    PRINT '✓ 所有核心业务表已成功创建'
ELSE
    PRINT '✗ 有 ' + CONVERT(varchar(10), @missing_count) + ' 个核心表缺失，请检查创建脚本'

PRINT ''
PRINT '统计完成时间: ' + CONVERT(varchar(20), GETDATE(), 120)
PRINT '=========================================='
