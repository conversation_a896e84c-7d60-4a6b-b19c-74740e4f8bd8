-- =============================================
-- 创建表结构脚本
-- 功能：创建仓库管理系统的核心表结构
-- 用途：数据库重构后的表结构重建
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 核心事务表 - ITRN (Inventory Transaction)
-- =============================================
CREATE TABLE ITRN
(ItrnKey                  char(10) NOT NULL,
ItrnSysId                int      NULL,
TranType                 char(10) NOT NULL,
StorerKey                char(15) NOT NULL,
Sku                      char(20) NOT NULL,
Lot                      char(10) NOT NULL,
FromLoc                  char(10) NOT NULL,
FromID                   char(18) NOT NULL,
ToLoc                    char(10) NOT NULL,
ToID                     char(18) NOT NULL,
SourceKey                char(20) NULL,
SourceType               char(30) NULL,
Status                   char(10) NULL,
LOTTABLE01               char(18) NOT NULL
CONSTRAINT DF_Itrn_LOTTABLE01 DEFAULT "" ,
LOTTABLE02               char(18) NOT NULL
CONSTRAINT DF_Itrn_LOTTABLE02 DEFAULT "" ,
LOTTABLE03               char(18) NOT NULL
CONSTRAINT DF_Itrn_LOTTABLE03 DEFAULT "" ,
LOTTABLE04               datetime NULL ,
LOTTABLE05               datetime NULL ,
CaseCnt                  int       NOT NULL
CONSTRAINT DF_Itrn_CaseCnt   DEFAULT 0 ,
InnerPack                int       NOT NULL
CONSTRAINT DF_Itrn_Innerpack DEFAULT 0 ,
Qty                      int  NOT NULL
CONSTRAINT DF_Itrn_QTY DEFAULT 0 ,
Pallet                   int       NOT NULL
CONSTRAINT DF_Itrn_Pallet    DEFAULT 0 ,
Cube                     Float     NOT NULL
CONSTRAINT DF_Itrn_Cube      DEFAULT 0 ,
GrossWgt                 Float     NOT NULL
CONSTRAINT DF_Itrn_GrossWgt  DEFAULT 0 ,
NetWgt                   Float     NOT NULL
CONSTRAINT DF_Itrn_NetWgt    DEFAULT 0 ,
OtherUnit1               Float     NOT NULL
CONSTRAINT DF_Itrn_OtherUnit1    DEFAULT 0 ,
OtherUnit2               Float     NOT NULL
CONSTRAINT DF_Itrn_OtherUnit2    DEFAULT 0 ,
PackKey                  char(10)     NULL,
UOM                      char(10)     NULL,
UOMCalc                  int          NULL,
UOMQty                   int          NULL,
EffectiveDate            datetime NOT NULL
CONSTRAINT DF_ITRN_EffectiveDate DEFAULT CURRENT_Timestamp ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_ITRN_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_ITRN_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_ITRN_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_ITRN_EditWho DEFAULT USER ,
TrafficCop     char(1)        NULL,
ArchiveCop     char(1)        NULL
)
GO

IF OBJECT_ID('ITRN') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ITRN FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ITRN>>>'
GRANT INSERT ON ITRN TO nsql
GRANT UPDATE ON ITRN TO nsql
GRANT DELETE ON ITRN TO nsql
GRANT SELECT ON ITRN TO nsql
END
GO

-- =============================================
-- SKU 表 (Stock Keeping Unit)
-- =============================================
CREATE TABLE SKU
(StorerKey                char(15) NOT NULL,
Sku                      char(20) NOT NULL,
DESCR                    char(60) NULL,
SUSR1                    char(18) NULL,
SUSR2                    char(18) NULL,
SUSR3                    char(18) NULL,
SUSR4                    char(18) NULL,
SUSR5                    char(18) NULL,
MANUFACTURERSKU          char(20) NULL,
RETAILSKU                char(20) NULL,
ALTSKU                   char(20) NULL,
PACKKey                  char(10) NOT NULL
CONSTRAINT DF_SKU_Packkey DEFAULT "STD",
STDGROSSWGT              float NOT NULL
CONSTRAINT DF_SKU_StdGrossWgt DEFAULT 0 ,
STDNETWGT                float NOT NULL
CONSTRAINT DF_SKU_StdNetWgt DEFAULT 0 ,
STDCUBE                  float NOT NULL
CONSTRAINT DF_SKU_StdCube DEFAULT 0 ,
TARE                     float NOT NULL
CONSTRAINT DF_SKU_Tare DEFAULT 0 ,
CLASS                    char(10) NOT NULL
CONSTRAINT DF_SKU_Class DEFAULT "STD" ,
ACTIVE                   char(10) NOT NULL
CONSTRAINT DF_SKU_ACTIVE DEFAULT "1"  ,
SKUGROUP                 char(10) NOT NULL
CONSTRAINT DF_SKU_SKUGROUP DEFAULT "STD" ,
Tariffkey                char(10) NULL
CONSTRAINT DF_SKU_TARIFF DEFAULT "XXXXXXXXXX",
BUSR1                    char(30) NULL,
BUSR2                    Char(30) NULL,
BUSR3                    char(30) NULL,
BUSR4                    char(30) NULL,
BUSR5                    char(30) NULL,
LOTTABLE01LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll01 DEFAULT "" ,
LOTTABLE02LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll02 DEFAULT "" ,
LOTTABLE03LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll03 DEFAULT "" ,
LOTTABLE04LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll04 DEFAULT "" ,
LOTTABLE05LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll05 DEFAULT "" ,
NOTES1                   text NULL,
NOTES2                   text NULL,
PickCode                 char(10) NOT NULL
CONSTRAINT DF_SKU_PickCode DEFAULT "NSPFIFO",
StrategyKey              char(10) NOT NULL
CONSTRAINT DF_SKU_StrategyKey DEFAULT "STD",
CartonGroup              char(10) NOT NULL
CONSTRAINT DF_SKU_CartonGroup DEFAULT "STD" ,
PutCode                  char(10) NOT NULL
CONSTRAINT DF_SKU_PutCode DEFAULT "NSPPASTD" ,
PutawayLoc             char(10) NULL
CONSTRAINT DF_SKU_PutawayLoc DEFAULT "UNKNOWN" ,
PutawayZone               char(10) NULL
CONSTRAINT DF_SKU_Putawayzone DEFAULT "BULK" ,
InnerPack                int NOT NULL
CONSTRAINT DF_SKU_InnerPack DEFAULT 0,
Cube                     float NOT NULL
CONSTRAINT DF_SKU_Cube DEFAULT 0,
GrossWgt                 float NOT NULL
CONSTRAINT DF_SKU_GrossWgt DEFAULT 0,
NetWgt                   float NOT NULL
CONSTRAINT DF_SKU_NetWgt DEFAULT 0,
ABC                      char(5) NULL ,
CycleCountFrequency      int NULL,
LastCycleCount           datetime NULL,
ReorderPoint             int NULL,
ReorderQty               int NULL,
StdOrderCost             float NULL,
CarryCost                float NULL,
Price                    money NULL,
Cost                     money NULL,
ReceiptHoldCode          char(10) NOT NULL
CONSTRAINT DF_SKU_RHC DEFAULT "" ,
ReceiptInspectionLoc     char(10) NOT NULL
CONSTRAINT DF_SKU_RIL DEFAULT "QC" ,
OnReceiptCopyPackkey     char(10) NOT NULL
CONSTRAINT DF_SKU_ORCP DEFAULT "0" , -- "0" = no action, "1" = copy packkey to lottable01 on receipt
TrafficCop               char(1) NULL,
ArchiveCop               char(1) NULL,
IOFlag                   char (1) NULL,
TareWeight               float    NULL
CONSTRAINT DF_SKU_TareWeight DEFAULT 0 ,
LotxIdDetailOtherlabel1  char(30) NULL
CONSTRAINT DF_SKU_Other1 DEFAULT "Ser#" ,
LotxIdDetailOtherlabel2  char(30) NULL
CONSTRAINT DF_SKU_Other2 DEFAULT "CSID" ,
LotxIdDetailOtherlabel3  char(30) NULL
CONSTRAINT DF_SKU_Other3 DEFAULT "Other" ,
AvgCaseWeight            float    NULL
CONSTRAINT DF_SKU_AvgCaseWeght DEFAULT 0 ,
TolerancePct             float    NULL
CONSTRAINT DF_SKU_TolerancePct DEFAULT 0
)
GO

IF OBJECT_ID('SKU') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE SKU FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE SKU>>>'
GRANT INSERT ON SKU TO nsql
GRANT UPDATE ON SKU TO nsql
GRANT DELETE ON SKU TO nsql
GRANT SELECT ON SKU TO nsql
END
GO

-- =============================================
-- ID 表 (标识符表)
-- =============================================
CREATE TABLE ID
(Id                       char(18) NOT NULL
CONSTRAINT DF_ID_Id DEFAULT "" ,
Qty                      int NOT NULL
CONSTRAINT DF_ID_Qty DEFAULT 0 ,
Status                   char(10) NOT NULL
CONSTRAINT DF_ID_Status DEFAULT "OK" ,
Packkey                  char(10) NOT NULL
CONSTRAINT DF_ID_Packkey DEFAULT "STD" ,
PutAwayTI                int NOT NULL
CONSTRAINT DF_ID_PutAwayTi DEFAULT 0 ,
PutAwayHI                int NOT NULL
CONSTRAINT DF_ID_PutAwayHi DEFAULT 0 ,
TrafficCop               char(1) NULL,
ArchiveCop               char(1) NULL
)
GO

IF OBJECT_ID('ID') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ID FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ID>>>'
GRANT INSERT ON ID TO nsql
GRANT UPDATE ON ID TO nsql
GRANT DELETE ON ID TO nsql
GRANT SELECT ON ID TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- LOTATTRIBUTE 表 (批次属性表)
-- =============================================
CREATE TABLE LOTATTRIBUTE
(StorerKey                char(15) NOT NULL,
Sku                      char(20) NOT NULL,
Lot                      char(10) NOT NULL,
Lottable01               char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_LOTTABLE01 DEFAULT "" ,
Lottable02               char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_LOTTABLE02 DEFAULT "" ,
Lottable03               char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_LOTTABLE03 DEFAULT "" ,
Lottable04               datetime NULL ,
Lottable05               datetime NULL ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_EditWho DEFAULT USER ,
TrafficCop               char(1) NULL,
ArchiveCop               char(1) NULL
)
GO

IF OBJECT_ID('LOTATTRIBUTE') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LOTATTRIBUTE FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LOTATTRIBUTE>>>'
GRANT INSERT ON LOTATTRIBUTE TO nsql
GRANT UPDATE ON LOTATTRIBUTE TO nsql
GRANT DELETE ON LOTATTRIBUTE TO nsql
GRANT SELECT ON LOTATTRIBUTE TO nsql
END
GO

-- =============================================
-- LOT 表 (批次表)
-- =============================================
CREATE TABLE LOT
(Lot                      char(10)  NOT NULL,
StorerKey                char(15)  NOT NULL,
Sku                      char(20)  NOT NULL,
CaseCnt                  int       NOT NULL
CONSTRAINT DF_Lot_CaseCnt   DEFAULT 0 ,
InnerPack                int       NOT NULL
CONSTRAINT DF_Lot_Innerpack DEFAULT 0 ,
Qty                      int       NOT NULL
CONSTRAINT DF_LOT_QTY DEFAULT 0
CONSTRAINT CK_LOT_QTY CHECK (Qty >= 0),
Pallet                   int       NOT NULL
CONSTRAINT DF_Lot_Pallet    DEFAULT 0 ,
Cube                     Float     NOT NULL
CONSTRAINT DF_Lot_Cube      DEFAULT 0 ,
GrossWgt                 Float     NOT NULL
CONSTRAINT DF_Lot_GrossWgt  DEFAULT 0 ,
NetWgt                   Float     NOT NULL
CONSTRAINT DF_Lot_NetWgt    DEFAULT 0 ,
OtherUnit1               Float     NOT NULL
CONSTRAINT DF_Lot_OtherUnit1    DEFAULT 0 ,
OtherUnit2               Float     NOT NULL
CONSTRAINT DF_Lot_OtherUnit2    DEFAULT 0 ,
QtyPreAllocated          int       NOT NULL
CONSTRAINT DF_LOT_QtyPreAllocated  DEFAULT 0 ,
CONSTRAINT CK_LOT_QtyPreAllocated  CHECK( QtyPreAllocated >= 0),
GrossWgtpreAllocated     float       NOT NULL
CONSTRAINT DF_LOT_GWpreAllocated DEFAULT 0,
NetWgtpreAllocated       float       NOT NULL
CONSTRAINT DF_LOT_NWpreAllocated DEFAULT 0,
QtyAllocated             int       NOT NULL
CONSTRAINT DF_LOT_QtyAllocated DEFAULT 0 ,
GrossWgtAllocated        float       NOT NULL
CONSTRAINT DF_LOT_GWAllocated DEFAULT 0,
NetWgtAllocated          float       NOT NULL
CONSTRAINT DF_LOT_NWAllocated DEFAULT 0,
QtyPicked                int       NOT NULL
CONSTRAINT DF_LOT_QtyPicked DEFAULT 0 ,
GrossWgtPicked           float       NOT NULL
CONSTRAINT DF_LOT_GWPicked DEFAULT 0,
NetWgtPicked             float       NOT NULL
CONSTRAINT DF_LOT_NWPicked DEFAULT 0,
QtyOnHold                int       NOT NULL
CONSTRAINT DF_LOT_QtyOnHold DEFAULT 0 ,
Status                   char(10)  NOT NULL
CONSTRAINT DF_LOT_Status DEFAULT "OK" ,
ArchiveQty               int       NOT NULL
CONSTRAINT DF_LOT_ArchiveQty DEFAULT 0 ,
ArchiveDate              DateTime  NOT NULL
CONSTRAINT DF_LOT_ArchiveDate DEFAULT '01/01/1901' ,
TrafficCop               char(1)   NULL,
ArchiveCop               char(1)   NULL,
CONSTRAINT CK_LOT_01 CHECK (Qty >= QtyPreAllocated + QtyAllocated + QtyPicked)
)
GO

IF OBJECT_ID('LOT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LOT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LOT>>>'
GRANT INSERT ON LOT TO nsql
GRANT UPDATE ON LOT TO nsql
GRANT DELETE ON LOT TO nsql
GRANT SELECT ON LOT TO nsql
END
GO

-- =============================================
-- LOC 表 (位置表)
-- =============================================
CREATE TABLE LOC
(Loc                      char(10) NOT NULL
CONSTRAINT DF_LOC_Loc DEFAULT "UNKNOWN"
CONSTRAINT CK_LOC_Loc_01 CHECK (NOT Loc = ""),
Cube                     float
CONSTRAINT DF_LOC_cube DEFAULT 0 ,
Length                   float
CONSTRAINT DF_LOC_length DEFAULT 0 ,
Width                    float
CONSTRAINT DF_LOC_width DEFAULT 0 ,
Height                   float
CONSTRAINT DF_LOC_height DEFAULT 0 ,
LocationType                char(10) NOT NULL
CONSTRAINT DF_LOC_LocationType DEFAULT "OTHER" ,
LocationFlag                char(10) NOT NULL  -- Hold/Damage
CONSTRAINT DF_LOC_LocationFlag DEFAULT "NONE" ,
LocationHandling            char(10) NOT NULL -- Case/Pallet/Other
CONSTRAINT DF_LOC_LocationHdl DEFAULT "1" ,
LocationCategory            char(10) NOT NULL -- DOUBLEDEEP, ASRS etc
CONSTRAINT DF_LOC_LocationCategory DEFAULT "OTHER" ,
LogicalLocation             char(18)
CONSTRAINT DF_LOC_LogicalLocation DEFAULT "" ,
CubicCapacity               float
CONSTRAINT DF_LOC_CubicCapacity DEFAULT 0 ,
WeightCapacity               float
CONSTRAINT DF_LOC_WeightCapacity DEFAULT 0 ,
Status                      char(10)
CONSTRAINT DF_LOC_Status DEFAULT "OK" ,
LoseId                      char(1) NOT NULL
CONSTRAINT DF_LOC_Loseid DEFAULT "0" ,
Facility                         char(5) NOT NULL
CONSTRAINT DF_LOC_Facility DEFAULT "F1" ,
ABC                              char(1) NOT NULL
CONSTRAINT DF_LOC_ABC DEFAULT "B" ,
PickZone                         char(10) NOT NULL
CONSTRAINT DF_LOC_PickZone DEFAULT "" ,
PutawayZone                 char(10) NOT NULL
CONSTRAINT DF_LOC_PutAwayZone DEFAULT "RACK" ,
SectionKey                  char(10) NOT NULL
CONSTRAINT DF_LOC_SectionKey DEFAULT "FACILITY" ,
PickMethod                  char(1)   NOT NULL
CONSTRAINT DF_LOC_PickMethod DEFAULT "" ,
CommingleSku                char(1)   NOT NULL
CONSTRAINT DF_LOC_CommingleSku DEFAULT "1",
CommingleLot                char(1)   NOT NULL
CONSTRAINT DF_LOC_CommingleLot DEFAULT "1",
LocLevel                    int NOT NULL
CONSTRAINT DF_LOC_LocLevel  DEFAULT 0,
Xcoord                      int NOT NULL
CONSTRAINT DF_LOC_Xcoord  DEFAULT 0,
Ycoord                      int NOT NULL
CONSTRAINT DF_LOC_Ycoord  DEFAULT 0,
Zcoord                      int NOT NULL
CONSTRAINT DF_LOC_Zcoord  DEFAULT 0,
TrafficCop     char(1)        NULL,
ArchiveCop     char(1)        NULL
)
GO

IF OBJECT_ID('LOC') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LOC FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LOC>>>'
GRANT INSERT ON LOC TO nsql
GRANT UPDATE ON LOC TO nsql
GRANT DELETE ON LOC TO nsql
GRANT SELECT ON LOC TO nsql
END
GO

-- =============================================
-- STORER 表 (存储商表)
-- =============================================
CREATE TABLE STORER
(StorerKey                char(15) NOT NULL,
type                     char(30) NULL
CONSTRAINT DF_STORER_Type DEFAULT "1" ,
Company                  char(45) NULL,
VAT                      char(18) NULL,
Address1                 char(45) NULL,
Address2                 char(45) NULL,
Address3                 char(45) NULL,
Address4                 char(45) NULL,
City                     char(45) NULL,
State                    char(2)  NULL,
Zip                      char(18) NULL,
Country                  char(30) NULL,
ISOCntryCode             char(10) NULL,
Contact1                 char(30) NULL,
Contact2                 char(30) NULL,
Phone1                   char(18) NULL,
Phone2                   char(18) NULL,
Fax1                     char(18) NULL,
Fax2                     char(18) NULL,
Email1                   char(60) NULL,
Email2                   char(60) NULL,
B_contact1               char(30) NULL,
B_Contact2               char(30) NULL,
B_Company                char(45) NULL,
B_Address1               char(45) NULL,
B_Address2               char(45) NULL,
B_Address3               char(45) NULL,
B_Address4               char(45) NULL,
B_City                   char(45) NULL,
B_State                  char(2)  NULL,
B_Zip                    char(18) NULL,
B_Country                char(30) NULL,
B_ISOCntryCode           char(10) NULL,
B_Phone1                 char(18) NULL,
B_Phone2                 char(18) NULL,
B_Fax1                   char(18) NULL,
B_Fax2                   char(18) NULL,
Notes1                   text NULL,
Notes2                   text NULL,
CreditLimit              char(18) NOT NULL
CONSTRAINT DF_STORER_CreditLimit DEFAULT "0" ,
CartonGroup              char(10) NOT NULL
CONSTRAINT DF_STORER_Cartongroup DEFAULT "STD" ,
PickCode                 CHAR(10) NOT NULL
CONSTRAINT DF_STORER_Pickcode DEFAULT "NSPFIFO" ,
CreatePATaskOnRFReceipt char(10) NOT NULL
CONSTRAINT DF_STORER_CPATOR DEFAULT "0" ,
CalculatePutAwayLocation char(10) NOT NULL
CONSTRAINT DF_STORER_CPAL DEFAULT "2" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_STORER_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_STORER_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_STORER_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_STORER_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('STORER') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE STORER FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE STORER>>>'
GRANT INSERT ON STORER TO nsql
GRANT UPDATE ON STORER TO nsql
GRANT DELETE ON STORER TO nsql
GRANT SELECT ON STORER TO nsql
END
GO

-- =============================================
-- NCOUNTER 表 (计数器表)
-- =============================================
CREATE TABLE NCOUNTER
(keyname       char(30) NOT NULL,
keycount       int      NOT NULL
)
GO

IF OBJECT_ID('NCOUNTER') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE NCOUNTER FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE NCOUNTER>>>'
GRANT INSERT ON NCOUNTER TO nsql
GRANT UPDATE ON NCOUNTER TO nsql
GRANT DELETE ON NCOUNTER TO nsql
GRANT SELECT ON NCOUNTER TO nsql
END
GO

-- =============================================
-- NCOUNTERITRN 表 (事务计数器表)
-- =============================================
CREATE TABLE NCOUNTERITRN
(keyname       char(30) NOT NULL,
keycount       int      NOT NULL
)
GO

IF OBJECT_ID('NCOUNTERITRN') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE NCOUNTERITRN FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE NCOUNTERITRN>>>'
GRANT INSERT ON NCOUNTERITRN TO nsql
GRANT UPDATE ON NCOUNTERITRN TO nsql
GRANT DELETE ON NCOUNTERITRN TO nsql
GRANT SELECT ON NCOUNTERITRN TO nsql
END
GO

-- =============================================
-- NCOUNTERPICK 表 (拣选计数器表)
-- =============================================
CREATE TABLE NCOUNTERPICK
(keyname       char(30) NOT NULL,
keycount       int      NOT NULL
)
GO

IF OBJECT_ID('NCOUNTERPICK') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE NCOUNTERPICK FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE NCOUNTERPICK>>>'
GRANT INSERT ON NCOUNTERPICK TO nsql
GRANT UPDATE ON NCOUNTERPICK TO nsql
GRANT DELETE ON NCOUNTERPICK TO nsql
GRANT SELECT ON NCOUNTERPICK TO nsql
END
GO

-- =============================================
-- CODELIST 表 (代码列表表)
-- =============================================
CREATE TABLE CODELIST
(LISTNAME                 char(10) NOT NULL,
DESCRIPTION              varchar(60) NULL,
AddDate                  datetime NOT NULL
CONSTRAINT DF_CODELIST_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_CODELIST_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_CODELIST_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_CODELIST_EditWho DEFAULT USER ,
TrafficCop     char(1)        NULL,
Timestamp             Timestamp NOT NULL
)
GO

IF OBJECT_ID('CODELIST') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CODELIST FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CODELIST>>>'
GRANT INSERT ON CODELIST TO nsql
GRANT UPDATE ON CODELIST TO nsql
GRANT DELETE ON CODELIST TO nsql
GRANT SELECT ON CODELIST TO nsql
END
GO

-- =============================================
-- CODELKUP 表 (代码查找表)
-- =============================================
CREATE TABLE CODELKUP
(LISTNAME                 char(10)     NOT NULL,
Code                     char(10)     NOT NULL,
Description              varchar(250) NULL,
Short                    char(10)     NULL,
Long                     varchar(250) NULL,
Notes                    text         NULL ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_CODELKUP_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_CODELKUP_AddWho DEFAULT USER,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_CODELKUP_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_CODELKUP_EditWho DEFAULT USER,
TrafficCop               char(1)      NULL,
Timestamp                timestamp    NOT NULL
)
GO

IF OBJECT_ID('CODELKUP') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CODELKUP FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CODELKUP>>>'
GRANT INSERT ON CODELKUP TO nsql
GRANT UPDATE ON CODELKUP TO nsql
GRANT DELETE ON CODELKUP TO nsql
GRANT SELECT ON CODELKUP TO nsql
END
GO

-- =============================================
-- NSQLCONFIG 表 (系统配置表)
-- =============================================
CREATE TABLE NSQLCONFIG
(ConfigKey                char(30) NOT NULL,
NSQLValue                char(30) NOT NULL
CONSTRAINT DF_NSQLCONFIG_NSQLValue DEFAULT "" ,
NSQLDefault              char(30) NOT NULL
CONSTRAINT DF_NSQLCONFIG_NSQLDEFAULT DEFAULT "" ,
NSQLDescrip              varchar (120) NULL ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_NSQLCONFIG_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_NSQLCONFIG_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_NSQLCONFIG_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_NSQLCONFIG_EditWho DEFAULT USER ,
TrafficCop     char(1)        NULL,
Timestamp             Timestamp NOT NULL
)
GO

IF OBJECT_ID('NSQLCONFIG') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE NSQLCONFIG FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE NSQLCONFIG>>>'
GRANT INSERT ON NSQLCONFIG TO nsql
GRANT UPDATE ON NSQLCONFIG TO nsql
GRANT DELETE ON NSQLCONFIG TO nsql
GRANT SELECT ON NSQLCONFIG TO nsql
END
GO

-- =============================================
-- ALERT 表 (警报表)
-- =============================================
CREATE TABLE ALERT
(
AlertKey       char (18)      NOT NULL ,
ModuleName     char (30)      NOT NULL ,
AlertMessage   char (255)     NOT NULL ,
Severity       int            NOT NULL CONSTRAINT DF_ALERT_Priority   DEFAULT (5),
LogDate        datetime       NOT NULL CONSTRAINT DF_ALERT_LogDate    DEFAULT CURRENT_TIMESTAMP,
UserId         char (18)      NOT NULL CONSTRAINT DF_ALERT_UserId     DEFAULT USER ,
NotifyId       char (18)      NOT NULL CONSTRAINT DF_ALERT_NotifyId   DEFAULT (' '),
Status         char (10)      NOT NULL CONSTRAINT DF_ALERT_Status     DEFAULT ('0'),
Resolution     text           NOT NULL CONSTRAINT DF_ALERT_Resolution DEFAULT (' '),
TrafficCop     char(1)        NULL,
Timestamp      Timestamp      NOT NULL
)
GO

IF OBJECT_ID('ALERT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ALERT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ALERT>>>'
GRANT INSERT ON ALERT TO nsql
GRANT UPDATE ON ALERT TO nsql
GRANT DELETE ON ALERT TO nsql
GRANT SELECT ON ALERT TO nsql
END
GO

-- =============================================
-- ERRLOG 表 (错误日志表)
-- =============================================
CREATE TABLE ERRLOG
(LogDate                  datetime NOT NULL
CONSTRAINT DF_Errlog_LogDate DEFAULT CURRENT_Timestamp ,
UserId                   char (18)
CONSTRAINT DF_Errlog_UserId DEFAULT USER ,
ErrorID                  int NOT NULL,
SystemState              char(18) NULL,
Module                   varchar(250) NULL ,
ErrorText                text NOT NULL,
TrafficCop        char(1)        NULL
)
GO

IF OBJECT_ID('ERRLOG') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ERRLOG FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ERRLOG>>>'
GRANT INSERT ON ERRLOG TO nsql
GRANT UPDATE ON ERRLOG TO nsql
GRANT DELETE ON ERRLOG TO nsql
GRANT SELECT ON ERRLOG TO nsql
END
GO

-- =============================================
-- IDSTACK 表 (ID堆栈表)
-- =============================================
CREATE TABLE IDSTACK
(Id                       char(18) NOT NULL
CONSTRAINT DF_IDSTACK_Id DEFAULT "" ,
IdType                   char(10) NOT NULL
CONSTRAINT DF_IDSTACK_idType DEFAULT "ID" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_IDSTACK_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_IDSTACK_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_IDSTACK_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_IDSTACK_EditWho DEFAULT USER ,
TrafficCop     char(1)        NULL,
Timestamp             Timestamp NOT NULL
)
GO

IF OBJECT_ID('IDSTACK') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE IDSTACK FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE IDSTACK>>>'
GRANT INSERT ON IDSTACK TO nsql
GRANT UPDATE ON IDSTACK TO nsql
GRANT DELETE ON IDSTACK TO nsql
GRANT SELECT ON IDSTACK TO nsql
END
GO

-- =============================================
-- PACK 表 (包装表)
-- =============================================
CREATE TABLE PACK
(PackKey                  char(10) NOT NULL,
PackDescr                char(45) NOT NULL,
PackUOM1                 char(10) NOT NULL
CONSTRAINT DF_PACK_PackUOM1 DEFAULT "" ,
CaseCnt                  float NOT NULL
CONSTRAINT DF_PACK_CaseCnt DEFAULT 0 ,
ISWHQty1                 char(1) NOT NULL
CONSTRAINT DF_PACK_ISWHQty1 DEFAULT "" ,
ReplenishUOM1            char(1) NOT NULL
CONSTRAINT DF_PACK_ReplenishUOM1 DEFAULT "N" ,
ReplenishZone1           char(10) NOT NULL
CONSTRAINT DF_PACK_ReplenishZone1 DEFAULT "N" ,
CartonizeUOM1            char(1) NOT NULL
CONSTRAINT DF_PACK_CartonizeUOM1 DEFAULT "N" ,
LengthUOM1               float NOT NULL
CONSTRAINT DF_PACK_lengthuom1 DEFAULT 0 ,
WidthUOM1               float NOT NULL
CONSTRAINT DF_PACK_widthuom1 DEFAULT 0 ,
HeightUOM1               float NOT NULL
CONSTRAINT DF_PACK_heightuom1 DEFAULT 0 ,
CubeUOM1                 float NOT NULL
CONSTRAINT DF_PACK_CubeUOM1 DEFAULT 0 ,
PackUOM2                 char(10) NOT NULL
CONSTRAINT DF_PACK_PackUOM2 DEFAULT "" ,
InnerPack                float NOT NULL
CONSTRAINT DF_PACK_InnerPack DEFAULT 0 ,
ISWHQty2                 char(1) NOT NULL
CONSTRAINT DF_PACK_ISWHQty2 DEFAULT "" ,
ReplenishUOM2            char(1) NOT NULL
CONSTRAINT DF_PACK_ReplenishUOM2 DEFAULT "N" ,
ReplenishZone2           char(10) NOT NULL
CONSTRAINT DF_PACK_ReplenishZone2 DEFAULT "N" ,
CartonizeUOM2            char(1) NOT NULL
CONSTRAINT DF_PACK_CartonizeUOM2 DEFAULT "N" ,
LengthUOM2               float NOT NULL
CONSTRAINT DF_PACK_lengthuom2 DEFAULT 0 ,
WidthUOM2               float NOT NULL
CONSTRAINT DF_PACK_widthuom2 DEFAULT 0 ,
HeightUOM2               float NOT NULL
CONSTRAINT DF_PACK_heightuom2 DEFAULT 0 ,
CubeUOM2                 float NOT NULL
CONSTRAINT DF_PACK_CubeUOM2 DEFAULT 0 ,
PackUOM3                 char(10) NOT NULL
CONSTRAINT DF_PACK_PackUOM3 DEFAULT "" ,
Qty                      float NOT NULL
CONSTRAINT DF_PACK_Qty DEFAULT 0 ,
ISWHQty3                 char(1) NOT NULL
CONSTRAINT DF_PACK_ISWHQty3 DEFAULT "" ,
ReplenishUOM3            char(1) NOT NULL
CONSTRAINT DF_PACK_ReplenishUOM3 DEFAULT "Y" ,
ReplenishZone3           char(10) NOT NULL
CONSTRAINT DF_PACK_ReplenishZone3 DEFAULT "Y" ,
CartonizeUOM3            char(1) NOT NULL
CONSTRAINT DF_PACK_CartonizeUOM3 DEFAULT "Y" ,
LengthUOM3               float NOT NULL
CONSTRAINT DF_PACK_lengthuom3 DEFAULT 0 ,
WidthUOM3               float NOT NULL
CONSTRAINT DF_PACK_widthuom3 DEFAULT 0 ,
HeightUOM3               float NOT NULL
CONSTRAINT DF_PACK_heightuom3 DEFAULT 0 ,
CubeUOM3                 float NOT NULL
CONSTRAINT DF_PACK_CubeUOM3 DEFAULT 0 ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_PACK_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PACK_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PACK_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PACK_EditWho DEFAULT USER ,
TrafficCop     char(1)        NULL,
ArchiveCop     char(1)        NULL,
Timestamp             Timestamp NOT NULL
)
GO

IF OBJECT_ID('PACK') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PACK FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PACK>>>'
GRANT INSERT ON PACK TO nsql
GRANT UPDATE ON PACK TO nsql
GRANT DELETE ON PACK TO nsql
GRANT SELECT ON PACK TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- SKUxLOC 表 (SKU位置关联表)
-- =============================================
CREATE TABLE SKUxLOC
(StorerKey                char(15) NOT NULL
CONSTRAINT DF_SKUxLOC_StorerKey DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_SKUxLOC_Sku DEFAULT "" ,
Loc                      char(10) NOT NULL
CONSTRAINT DF_SKUxLOC_Loc DEFAULT "" ,
Qty                      int NOT NULL
CONSTRAINT DF_SKUxLOC_Qty DEFAULT 0
CONSTRAINT CK_SKUxLOC_Qty CHECK (Qty >= 0),
QtyAllocated             int NOT NULL
CONSTRAINT DF_SKUxLOC_QtyAllocated DEFAULT 0 ,
QtyPicked                int NOT NULL
CONSTRAINT DF_SKUxLOC_QtyPicked DEFAULT 0 ,
QtyExpected              int NOT NULL
CONSTRAINT DF_SKUxLOC_Qtyxpected DEFAULT 0 ,
QtyLocationLimit         int NOT NULL
CONSTRAINT DF_SKUxLOC_QtyLocationLimit DEFAULT 0 ,
QtyLocationMinimum       int NOT NULL
CONSTRAINT DF_SKUxLOC_QtyLocationMin DEFAULT 0,
QtyPickInProcess         int NOT NULL
CONSTRAINT DF_SKUxLOC_QtyPickInP DEFAULT 0,
QtyReplenishmentOverride int NOT NULL
CONSTRAINT DF_SKUxLOC_QtyReplOver DEFAULT 0,
ReplenishmentPriority     char(5) NOT NULL
CONSTRAINT DF_SKUXLOC_replpriority DEFAULT "9" ,
ReplenishmentSeverity     int NOT NULL
CONSTRAINT DF_SKUXLOC_replseverity DEFAULT 0 ,
ReplenishmentCasecnt      int NOT NULL
CONSTRAINT DF_SKUXLOC_replcasecnt DEFAULT 0 ,
LocationType             char(10) NOT NULL
CONSTRAINT DF_SKUxLOC_LocationType DEFAULT "" ,
TrafficCop               char(1)        NULL,
ArchiveCop               char(1)        NULL,
CONSTRAINT CK_SKUxLOC_01 CHECK (Qty + QtyExpected >= QtyAllocated + QtyPicked)
)
GO

IF OBJECT_ID('SKUxLOC') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE SKUxLOC FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE SKUxLOC>>>'
GRANT INSERT ON SKUxLOC TO nsql
GRANT UPDATE ON SKUxLOC TO nsql
GRANT DELETE ON SKUxLOC TO nsql
GRANT SELECT ON SKUxLOC TO nsql
END
GO

-- =============================================
-- ORDERS 表 (订单表)
-- =============================================
CREATE TABLE ORDERS
(OrderKey                 char(10) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_ORDERS_StorerKey DEFAULT "" ,
ExternOrderKey           char(30) NOT NULL
CONSTRAINT DF_ORDERS_ExternOrderKey DEFAULT "" ,
OrderDate                Datetime NOT NULL
CONSTRAINT DF_ORDERS_OrderDate DEFAULT CURRENT_TimeStamp ,
DeliveryDate             DateTime NOT NULL
CONSTRAINT DF_ORDERS_DeliveryDate DEFAULT CURRENT_TimeStamp ,
Priority                 char(10) NOT NULL
CONSTRAINT DF_ORDERS_Priority DEFAULT "5" ,
ConsigneeKey             char(15) NOT NULL
CONSTRAINT DF_ORDERS_ConsigneeKey DEFAULT "" ,
C_contact1               char(30) NULL,
C_Contact2               char(30) NULL,
C_Company                char(45) NULL,
C_Address1               char(45) NULL,
C_Address2               char(45) NULL,
C_Address3               char(45) NULL,
C_Address4               char(45) NULL,
C_City                   char(45) NULL,
C_State                  char(2)  NULL,
C_Zip                    char(18) NULL,
C_Country                char(30) NULL,
C_ISOCntryCode           char(10) NULL,
C_Phone1                 char(18) NULL,
C_Phone2                 char(18) NULL,
C_Fax1                   char(18) NULL,
C_Fax2                   char(18) NULL,
C_Email1                 char(60) NULL,
C_Email2                 char(60) NULL,
CarrierKey               char(15) NULL,
CarrierName              char(30) NULL,
CarrierAddress1          char(45) NULL,
CarrierAddress2          char(45) NULL,
CarrierCity              char(45) NULL,
CarrierState             char(2) NULL,
CarrierZip               char(10) NULL,
CarrierReference         char(18) NULL,
WarehouseReference       char(18) NULL,
Route                    char(10) NOT NULL
CONSTRAINT DF_ORDERS_Route DEFAULT "" ,
Stop                     char(10) NOT NULL
CONSTRAINT DF_ORDERS_Stop DEFAULT "" ,
Door                     char(10) NOT NULL
CONSTRAINT DF_ORDERS_Door DEFAULT "" ,
Type                     char(10) NOT NULL
CONSTRAINT DF_ORDERS_Type DEFAULT "0" ,
OrderGroup               char(20) NOT NULL
CONSTRAINT DF_ORDERS_OrderGroup DEFAULT "" ,
IntermodalVehicle        char(10) NOT NULL
CONSTRAINT DF_ORDERS_IntermodalVehicle DEFAULT "" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_ORDERS_Status DEFAULT "0"
CONSTRAINT CK_ORDERS_Status CHECK ( Status LIKE '[0-9]' ),
Notes                    text NULL,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_ORDERS_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_ORDERS_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_ORDERS_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_ORDERS_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_ORDERS_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
ContainerType            char(20) NULL,
ContainerQty             int      NULL,
BilledContainerQty       int      NULL
CONSTRAINT DF_ORDERS_BilConQty DEFAULT 0
)
GO

IF OBJECT_ID('ORDERS') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ORDERS FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ORDERS>>>'
GRANT INSERT ON ORDERS TO nsql
GRANT UPDATE ON ORDERS TO nsql
GRANT DELETE ON ORDERS TO nsql
GRANT SELECT ON ORDERS TO nsql
END
GO

-- =============================================
-- ORDERDETAIL 表 (订单明细表)
-- =============================================
CREATE TABLE ORDERDETAIL
(OrderKey                 char(10) NOT NULL,
OrderLineNumber          char(5) NOT NULL,
OrderDetailSysId         Int NULL ,
ExternOrderKey           char(20) NOT NULL
CONSTRAINT DF_ORDERDETAIL_XorderKey DEFAULT "" ,
ExternLineNo             char(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_XLineNo DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_ORDERDETAIL_Sku DEFAULT "" ,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_ORDERDETAIL_StorerKey DEFAULT "" ,
ManufacturerSku          char(20) NOT NULL
CONSTRAINT DF_ORDERDETAIL_ManSku DEFAULT "" ,
RetailSku                char(20) NOT NULL
CONSTRAINT DF_ORDERDETAIL_RetSku DEFAULT "" ,
AltSku                   char(20) NOT NULL
CONSTRAINT DF_ORDERDETAIL_AltSku DEFAULT "" ,
OriginalQty              int NOT NULL
CONSTRAINT DF_ORDERDETAIL_OrgQty DEFAULT 0 ,
OpenQty                  int NOT NULL
CONSTRAINT DF_ORDERDETAIL_OpenQty DEFAULT 0 ,
ShippedQty               int NOT NULL
CONSTRAINT DF_ORDERDETAIL_ShippedQty DEFAULT 0 ,
AdjustedQty               int NOT NULL
CONSTRAINT DF_ORDERDETAIL_AdjustedQty DEFAULT 0 ,
QtyPreAllocated           int NOT NULL
CONSTRAINT DF_ORDERDETAIL_QtyPreAllocated DEFAULT 0 ,
CONSTRAINT CK_ORDERDETAIL_QtyPreAlloc CHECK( QtyPreAllocated >= 0) ,
QtyAllocated             int NOT NULL
CONSTRAINT DF_ORDERDETAIL_QtyAllocated DEFAULT 0 ,
QtyPicked                int NOT NULL
CONSTRAINT DF_ORDERDETAIL_QtyPicked DEFAULT 0 ,
UOM                      char(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_UOM DEFAULT "" ,
PackKey                  char(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_PackKey DEFAULT "STD" ,
PickCode                 char(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_PickCode DEFAULT "" ,
CartonGroup              char(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_CartonGroup DEFAULT "" ,
Lot                      char(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_LOT DEFAULT "" ,
ID                       char(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_ID DEFAULT "" ,
Facility                 char(5) NOT NULL
CONSTRAINT DF_ORDERDETAIL_FAC DEFAULT "" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_Status DEFAULT "0"
CONSTRAINT CK_ORDERDETAIL_Status CHECK ( Status LIKE '[0-9]' ),
UnitPrice                float
CONSTRAINT DF_ORDERDETAIL_UnitPrice DEFAULT 0 ,
Tax01                    float
CONSTRAINT DF_ORDERDETAIL_Tax01 DEFAULT 0 ,
Tax02                    float
CONSTRAINT DF_ORDERDETAIL_Tax02 DEFAULT 0 ,
ExtendedPrice            float
CONSTRAINT DF_ORDERDETAIL_xtendedprice DEFAULT 0 ,
UpdateSource             char(10) NOT NULL
CONSTRAINT DF_ORDERDETAIL_Usource DEFAULT "0" ,
Lottable01              char(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_LOTTABLE01 DEFAULT "" ,
Lottable02              char(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_LOTTABLE02 DEFAULT "" ,
Lottable03              char(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_LOTTABLE03 DEFAULT "" ,
Lottable04              datetime NULL ,
Lottable05              datetime NULL ,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_ORDERDETAIL_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_ORDERDETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_ORDERDETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_ORDERDETAIL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
TariffKey                char(10) NULL,
CONSTRAINT CK_ORDERDETAIL_QtyAllocated CHECK (QtyAllocated >=0 AND QtyAllocated <=OpenQty),
CONSTRAINT CK_ORDERDETAIL_QtyPicked CHECK (QtyPicked >=0 AND QtyPicked <=OpenQty) ,
CONSTRAINT CK_ORDERDETAIL_QtyPreAllocated CHECK (QtyPreAllocated + QtyPicked + QtyAllocated <=OpenQty)
)
GO

IF OBJECT_ID('ORDERDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ORDERDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ORDERDETAIL>>>'
GRANT INSERT ON ORDERDETAIL TO nsql
GRANT UPDATE ON ORDERDETAIL TO nsql
GRANT DELETE ON ORDERDETAIL TO nsql
GRANT SELECT ON ORDERDETAIL TO nsql
END
GO

-- =============================================
-- PICKHEADER 表 (拣选头表)
-- =============================================
CREATE TABLE PICKHEADER
(PickHeaderKey            char(18) NOT NULL,
WaveKey                  char(10) NOT NULL
CONSTRAINT DF_PICKHEADER_WaveKey DEFAULT "" ,
OrderKey                 char(10) NOT NULL
CONSTRAINT DF_PICKHEADER_OrderKey DEFAULT "" ,
ExternOrderKey           char(20) NOT NULL
CONSTRAINT DF_PICKHEADER_ExternKey DEFAULT "" ,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_PICKHEADER_StorerKey DEFAULT "" ,
ConsigneeKey             char(30) NOT NULL
CONSTRAINT DF_PICKHEADER_ConsKey DEFAULT "" ,
Priority                 char(10) NOT NULL
CONSTRAINT DF_PICKHEADER_Priority DEFAULT "5" ,
Type                     char(10) NOT NULL
CONSTRAINT DF_PICKHEADER_Type DEFAULT "5" ,
Zone                     char(18) NOT NULL
CONSTRAINT DF_PICKHEADER_Zone DEFAULT "" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_PICKHEADER_Status DEFAULT "0"
CONSTRAINT CK_PICKHEADER_Status CHECK ( Status LIKE '[0-9]' ),
PickType                 char(10) NOT NULL
CONSTRAINT DF_PICKHEADER_PickType DEFAULT "3" ,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_PICKHEADER_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_PICKHEADER_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PICKHEADER_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PICKHEADER_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PICKHEADER_EditWho DEFAULT USER ,
TrafficCop     char(1)        NULL,
ArchiveCop   char(1)        NULL
)
GO

IF OBJECT_ID('PICKHEADER') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PICKHEADER FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PICKHEADER>>>'
GRANT INSERT ON PICKHEADER TO nsql
GRANT UPDATE ON PICKHEADER TO nsql
GRANT DELETE ON PICKHEADER TO nsql
GRANT SELECT ON PICKHEADER TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- PICKDETAIL 表 (拣选明细表)
-- =============================================
CREATE TABLE PICKDETAIL
(PickDetailKey            char(18) NOT NULL,
CaseID                   char(10) NOT NULL
CONSTRAINT DF_PICKDETAIL_CaseID DEFAULT "" ,
PickHeaderKey            char(18) NOT NULL,
OrderKey                 char(10) NOT NULL,
OrderLineNumber          char(5) NOT NULL,
Lot                      char(10) NOT NULL ,
Storerkey                char(15) NOT NULL ,
Sku                      char(20) NOT NULL,
AltSku                   char(20) NOT NULL
CONSTRAINT DF_PICKDETAIL_AltSku DEFAULT "" ,
UOM                      char(10) NOT NULL
CONSTRAINT DF_PICKDETAIL_UOM DEFAULT "" ,
UOMQty                   int NOT NULL
CONSTRAINT DF_PICKDETAIL_UOMQty DEFAULT 0 ,
Qty                      int NOT NULL
CONSTRAINT DF_PICKDETAIL_Qty DEFAULT 0 ,
QtyMoved                 int NOT NULL
CONSTRAINT DF_PICKDETAIL_QtyMoved DEFAULT 0 ,
Status                   char(10) NOT NULL
CONSTRAINT DF_PICKDETAIL_Status DEFAULT "0"
CONSTRAINT CK_PICKDETAIL_Status CHECK ( Status LIKE '[0-9]' ),
DropID                   char(18) NOT NULL
CONSTRAINT DF_PICKDETAIL_DropID DEFAULT "" ,
Loc                      char(10) NOT NULL
CONSTRAINT DF_PICKDETAIL_Loc DEFAULT "UNKNOWN" ,
ID                       char(18) NOT NULL
CONSTRAINT DF_PICKDETAIL_ID DEFAULT "" ,
PackKey                  char(10)
CONSTRAINT DF_PICKDETAIL_PackKey DEFAULT "",
UpdateSource             char(10)
CONSTRAINT DF_PICKDETAIL_UpdateSource DEFAULT "0" ,
CartonGroup              char(10) NULL ,
CartonType               char(10) NULL ,
ToLoc                    char(10) NULL
CONSTRAINT DF_PICKDETAIL_ToLoc DEFAULT "" ,
DoReplenish              char(1) NULL
CONSTRAINT DF_PICKDETAIL_DoReplenish DEFAULT "N" ,
ReplenishZone            char(10) NULL
CONSTRAINT DF_PICKDETAIL_ReplenishZone DEFAULT "" ,
DoCartonize              char(1) NULL
CONSTRAINT DF_PICKDETAIL_DoCartonize DEFAULT "N" ,
PickMethod               char(1) NOT NULL
CONSTRAINT DF_PICKDETAIL_PickMethod DEFAULT "" ,
WaveKey                  char(10) NOT NULL
CONSTRAINT DF_PICKDETAIL_WaveKey DEFAULT "" ,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_PICKDETAIL_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_PICKDETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PICKDETAIL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PICKDETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PICKDETAIL_EditWho DEFAULT USER ,
TrafficCop               char(1)        NULL,
ArchiveCop               char(1)        NULL,
OptimizeCop              char(1)        NULL
)
GO

IF OBJECT_ID('PICKDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PICKDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PICKDETAIL>>>'
GRANT INSERT ON PICKDETAIL TO nsql
GRANT UPDATE ON PICKDETAIL TO nsql
GRANT DELETE ON PICKDETAIL TO nsql
GRANT SELECT ON PICKDETAIL TO nsql
END
GO

-- =============================================
-- CARTONIZATION 表 (装箱表)
-- =============================================
CREATE TABLE CARTONIZATION
(CartonizationKey         char(10) NOT NULL,
CartonizationGroup       char(10) NOT NULL
CONSTRAINT DF_CARTON_CartonizationGroup DEFAULT "" ,
CartonType               char(10) NOT NULL
CONSTRAINT DF_CARTON_CartonType DEFAULT "" ,
CartonDescription        varchar(60) NOT NULL
CONSTRAINT DF_CARTON_CartonTDescrip DEFAULT "" ,
UseSequence              int NOT NULL
CONSTRAINT DF_CARTON_UseSequence DEFAULT 1 ,
Cube                     float NOT NULL
CONSTRAINT DF_CARTON_Cube DEFAULT 0 ,
MaxWeight                float NOT NULL
CONSTRAINT DF_CARTON_MaxWeight DEFAULT 0 ,
MaxCount                 int NOT NULL
CONSTRAINT DF_CARTON_MaxCount DEFAULT 0 ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_CARTON_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_CARTON_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_CARTON_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_CARTON_EditWho DEFAULT USER ,
TrafficCop     char(1)        NULL,
ArchiveCop     char(1)        NULL,
Timestamp             Timestamp NOT NULL
)
GO

IF OBJECT_ID('CARTONIZATION') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CARTONIZATION FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CARTONIZATION>>>'
GRANT INSERT ON CARTONIZATION TO nsql
GRANT UPDATE ON CARTONIZATION TO nsql
GRANT DELETE ON CARTONIZATION TO nsql
GRANT SELECT ON CARTONIZATION TO nsql
END
GO

-- =============================================
-- WAVE 表 (波次表)
-- =============================================
CREATE TABLE WAVE
(WaveKey                  char(10) NOT NULL,
WaveType                 char(18) NOT NULL
CONSTRAINT DF_WAVE_WaveType DEFAULT "0" ,
Descr                    char(60) NOT NULL
CONSTRAINT DF_WAVE_Desc     DEFAULT "" ,
DispatchPalletPickMethod char(10) NOT NULL
CONSTRAINT DF_WAVE_DPPM     DEFAULT "1" ,
DispatchCasePickMethod   char(10) NOT NULL
CONSTRAINT DF_WAVE_DCPM     DEFAULT "1" ,
DispatchPiecePickMethod  char(10) NOT NULL
CONSTRAINT DF_WAVE_DPIPM    DEFAULT "1" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_WAVE_Status DEFAULT "0" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_WAVE_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_WAVE_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_WAVE_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_WAVE_EditWho DEFAULT USER ,
TrafficCop     char(1)        NULL,
ArchiveCop     char(1)        NULL
)
GO

IF OBJECT_ID('WAVE') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE WAVE FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE WAVE>>>'
GRANT INSERT ON WAVE TO nsql
GRANT UPDATE ON WAVE TO nsql
GRANT DELETE ON WAVE TO nsql
GRANT SELECT ON WAVE TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- RECEIPT 表 (收货表)
-- =============================================
CREATE TABLE RECEIPT
(ReceiptKey               char(10) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_RECEIPT_StorerKey DEFAULT "" ,
ExternReceiptKey         char(30) NOT NULL
CONSTRAINT DF_RECEIPT_ExternReceiptKey DEFAULT "" ,
ReceiptDate              datetime NOT NULL
CONSTRAINT DF_RECEIPT_ReceiptDate DEFAULT CURRENT_Timestamp ,
POKey                    char(18) NOT NULL
CONSTRAINT DF_RECEIPT_POKey DEFAULT "" ,
ExternPOKey              char(30) NOT NULL
CONSTRAINT DF_RECEIPT_ExternPOKey DEFAULT "" ,
CarrierKey               char(15) NULL,
CarrierName              char(30) NULL,
CarrierAddress1          char(45) NULL,
CarrierAddress2          char(45) NULL,
CarrierCity              char(45) NULL,
CarrierState             char(2) NULL,
CarrierZip               char(10) NULL,
CarrierReference         char(18) NULL,
WarehouseReference       char(18) NULL,
TrailerKey               char(18) NULL,
ContainerKey             char(20) NULL,
Type                     char(10) NOT NULL
CONSTRAINT DF_RECEIPT_Type DEFAULT "0" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_RECEIPT_Status DEFAULT "0"
CONSTRAINT CK_RECEIPT_Status CHECK ( Status LIKE '[0-9]' ),
Notes                    text NULL,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_RECEIPT_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_RECEIPT_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_RECEIPT_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_RECEIPT_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_RECEIPT_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('RECEIPT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE RECEIPT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE RECEIPT>>>'
GRANT INSERT ON RECEIPT TO nsql
GRANT UPDATE ON RECEIPT TO nsql
GRANT DELETE ON RECEIPT TO nsql
GRANT SELECT ON RECEIPT TO nsql
END
GO

-- =============================================
-- RECEIPTDETAIL 表 (收货明细表)
-- =============================================
CREATE TABLE RECEIPTDETAIL
(ReceiptKey               char(10) NOT NULL,
ReceiptLineNumber        char(5) NOT NULL,
ReceiptDetailSysId       int NULL ,
ExternReceiptKey         char(30) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_XReceiptKey DEFAULT "" ,
ExternLineNo             char(10) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_XLineNo DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_Sku DEFAULT "" ,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_StorerKey DEFAULT "" ,
ManufacturerSku          char(20) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_ManSku DEFAULT "" ,
RetailSku                char(20) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_RetSku DEFAULT "" ,
AltSku                   char(20) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_AltSku DEFAULT "" ,
QtyExpected              int NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_QtyExpected DEFAULT 0 ,
QtyReceived              int NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_QtyReceived DEFAULT 0 ,
QtyDamaged               int NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_QtyDamaged DEFAULT 0 ,
UOM                      char(10) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_UOM DEFAULT "" ,
PackKey                  char(10) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_PackKey DEFAULT "STD" ,
Lot                      char(10) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_LOT DEFAULT "" ,
ID                       char(18) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_ID DEFAULT "" ,
ToLoc                    char(10) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_ToLoc DEFAULT "UNKNOWN" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_Status DEFAULT "0"
CONSTRAINT CK_RECEIPTDETAIL_Status CHECK ( Status LIKE '[0-9]' ),
UnitPrice                float
CONSTRAINT DF_RECEIPTDETAIL_UnitPrice DEFAULT 0 ,
Tax01                    float
CONSTRAINT DF_RECEIPTDETAIL_Tax01 DEFAULT 0 ,
Tax02                    float
CONSTRAINT DF_RECEIPTDETAIL_Tax02 DEFAULT 0 ,
ExtendedPrice            float
CONSTRAINT DF_RECEIPTDETAIL_xtendedprice DEFAULT 0 ,
Lottable01              char(18) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_LOTTABLE01 DEFAULT "" ,
Lottable02              char(18) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_LOTTABLE02 DEFAULT "" ,
Lottable03              char(18) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_LOTTABLE03 DEFAULT "" ,
Lottable04              datetime NULL ,
Lottable05              datetime NULL ,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_RECEIPTDETAIL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
TariffKey                char(10) NULL
)
GO

IF OBJECT_ID('RECEIPTDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE RECEIPTDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE RECEIPTDETAIL>>>'
GRANT INSERT ON RECEIPTDETAIL TO nsql
GRANT UPDATE ON RECEIPTDETAIL TO nsql
GRANT DELETE ON RECEIPTDETAIL TO nsql
GRANT SELECT ON RECEIPTDETAIL TO nsql
END
GO

-- =============================================
-- PO 表 (采购订单表)
-- =============================================
CREATE TABLE PO
(POKey                    char(18) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_PO_StorerKey DEFAULT "" ,
ExternPOKey              char(30) NOT NULL
CONSTRAINT DF_PO_ExternPOKey DEFAULT "" ,
PODate                   datetime NOT NULL
CONSTRAINT DF_PO_PODate DEFAULT CURRENT_Timestamp ,
DeliveryDate             datetime NOT NULL
CONSTRAINT DF_PO_DeliveryDate DEFAULT CURRENT_Timestamp ,
SupplierKey              char(15) NOT NULL
CONSTRAINT DF_PO_SupplierKey DEFAULT "" ,
S_contact1               char(30) NULL,
S_Contact2               char(30) NULL,
S_Company                char(45) NULL,
S_Address1               char(45) NULL,
S_Address2               char(45) NULL,
S_Address3               char(45) NULL,
S_Address4               char(45) NULL,
S_City                   char(45) NULL,
S_State                  char(2)  NULL,
S_Zip                    char(18) NULL,
S_Country                char(30) NULL,
S_ISOCntryCode           char(10) NULL,
S_Phone1                 char(18) NULL,
S_Phone2                 char(18) NULL,
S_Fax1                   char(18) NULL,
S_Fax2                   char(18) NULL,
S_Email1                 char(60) NULL,
S_Email2                 char(60) NULL,
CarrierKey               char(15) NULL,
CarrierName              char(30) NULL,
CarrierAddress1          char(45) NULL,
CarrierAddress2          char(45) NULL,
CarrierCity              char(45) NULL,
CarrierState             char(2) NULL,
CarrierZip               char(10) NULL,
CarrierReference         char(18) NULL,
WarehouseReference       char(18) NULL,
Type                     char(10) NOT NULL
CONSTRAINT DF_PO_Type DEFAULT "0" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_PO_Status DEFAULT "0"
CONSTRAINT CK_PO_Status CHECK ( Status LIKE '[0-9]' ),
Notes                    text NULL,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_PO_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_PO_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PO_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PO_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PO_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('PO') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PO FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PO>>>'
GRANT INSERT ON PO TO nsql
GRANT UPDATE ON PO TO nsql
GRANT DELETE ON PO TO nsql
GRANT SELECT ON PO TO nsql
END
GO

-- =============================================
-- PODETAIL 表 (采购订单明细表)
-- =============================================
CREATE TABLE PODETAIL
(POKey                    char(18) NOT NULL,
POLineNumber             char(5) NOT NULL,
PODetailSysId            int NULL ,
ExternPOKey              char(30) NOT NULL
CONSTRAINT DF_PODETAIL_XPOKey DEFAULT "" ,
ExternLineNo             char(10) NOT NULL
CONSTRAINT DF_PODETAIL_XLineNo DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_PODETAIL_Sku DEFAULT "" ,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_PODETAIL_StorerKey DEFAULT "" ,
ManufacturerSku          char(20) NOT NULL
CONSTRAINT DF_PODETAIL_ManSku DEFAULT "" ,
RetailSku                char(20) NOT NULL
CONSTRAINT DF_PODETAIL_RetSku DEFAULT "" ,
AltSku                   char(20) NOT NULL
CONSTRAINT DF_PODETAIL_AltSku DEFAULT "" ,
OriginalQty              int NOT NULL
CONSTRAINT DF_PODETAIL_OrgQty DEFAULT 0 ,
OpenQty                  int NOT NULL
CONSTRAINT DF_PODETAIL_OpenQty DEFAULT 0 ,
ReceivedQty              int NOT NULL
CONSTRAINT DF_PODETAIL_ReceivedQty DEFAULT 0 ,
UOM                      char(10) NOT NULL
CONSTRAINT DF_PODETAIL_UOM DEFAULT "" ,
PackKey                  char(10) NOT NULL
CONSTRAINT DF_PODETAIL_PackKey DEFAULT "STD" ,
Lot                      char(10) NOT NULL
CONSTRAINT DF_PODETAIL_LOT DEFAULT "" ,
ID                       char(18) NOT NULL
CONSTRAINT DF_PODETAIL_ID DEFAULT "" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_PODETAIL_Status DEFAULT "0"
CONSTRAINT CK_PODETAIL_Status CHECK ( Status LIKE '[0-9]' ),
UnitPrice                float
CONSTRAINT DF_PODETAIL_UnitPrice DEFAULT 0 ,
Tax01                    float
CONSTRAINT DF_PODETAIL_Tax01 DEFAULT 0 ,
Tax02                    float
CONSTRAINT DF_PODETAIL_Tax02 DEFAULT 0 ,
ExtendedPrice            float
CONSTRAINT DF_PODETAIL_xtendedprice DEFAULT 0 ,
Lottable01              char(18) NOT NULL
CONSTRAINT DF_PODETAIL_LOTTABLE01 DEFAULT "" ,
Lottable02              char(18) NOT NULL
CONSTRAINT DF_PODETAIL_LOTTABLE02 DEFAULT "" ,
Lottable03              char(18) NOT NULL
CONSTRAINT DF_PODETAIL_LOTTABLE03 DEFAULT "" ,
Lottable04              datetime NULL ,
Lottable05              datetime NULL ,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_PODETAIL_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_PODETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PODETAIL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PODETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PODETAIL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL,
TariffKey                char(10) NULL
)
GO

IF OBJECT_ID('PODETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PODETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PODETAIL>>>'
GRANT INSERT ON PODETAIL TO nsql
GRANT UPDATE ON PODETAIL TO nsql
GRANT DELETE ON PODETAIL TO nsql
GRANT SELECT ON PODETAIL TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- PALLET 表 (托盘表)
-- =============================================
CREATE TABLE PALLET
(PalletKey                char(10) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_PALLET_StorerKey DEFAULT "" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_PALLET_Status DEFAULT "OK" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_PALLET_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PALLET_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PALLET_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PALLET_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('PALLET') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PALLET FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PALLET>>>'
GRANT INSERT ON PALLET TO nsql
GRANT UPDATE ON PALLET TO nsql
GRANT DELETE ON PALLET TO nsql
GRANT SELECT ON PALLET TO nsql
END
GO

-- =============================================
-- PALLETDETAIL 表 (托盘明细表)
-- =============================================
CREATE TABLE PALLETDETAIL
(PalletKey                char(10) NOT NULL,
PalletLineNumber         char(5) NOT NULL,
Loc                      char(10) NOT NULL
CONSTRAINT DF_PALLETDETAIL_Loc DEFAULT "UNKNOWN" ,
ID                       char(18) NOT NULL
CONSTRAINT DF_PALLETDETAIL_ID DEFAULT "" ,
Lot                      char(10) NOT NULL
CONSTRAINT DF_PALLETDETAIL_Lot DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_PALLETDETAIL_Sku DEFAULT "" ,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_PALLETDETAIL_StorerKey DEFAULT "" ,
Qty                      int NOT NULL
CONSTRAINT DF_PALLETDETAIL_Qty DEFAULT 0 ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_PALLETDETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PALLETDETAIL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PALLETDETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PALLETDETAIL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('PALLETDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PALLETDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PALLETDETAIL>>>'
GRANT INSERT ON PALLETDETAIL TO nsql
GRANT UPDATE ON PALLETDETAIL TO nsql
GRANT DELETE ON PALLETDETAIL TO nsql
GRANT SELECT ON PALLETDETAIL TO nsql
END
GO

-- =============================================
-- CONTAINER 表 (容器表)
-- =============================================
CREATE TABLE CONTAINER
(ContainerKey             char(20) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_CONTAINER_StorerKey DEFAULT "" ,
ContainerType            char(20) NOT NULL
CONSTRAINT DF_CONTAINER_ContainerType DEFAULT "" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_CONTAINER_Status DEFAULT "OK" ,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_CONTAINER_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_CONTAINER_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_CONTAINER_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_CONTAINER_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_CONTAINER_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('CONTAINER') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CONTAINER FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CONTAINER>>>'
GRANT INSERT ON CONTAINER TO nsql
GRANT UPDATE ON CONTAINER TO nsql
GRANT DELETE ON CONTAINER TO nsql
GRANT SELECT ON CONTAINER TO nsql
END
GO

-- =============================================
-- CONTAINERDETAIL 表 (容器明细表)
-- =============================================
CREATE TABLE CONTAINERDETAIL
(ContainerKey             char(20) NOT NULL,
ContainerLineNumber      char(5) NOT NULL,
PalletKey                char(10) NOT NULL
CONSTRAINT DF_CONTAINERDETAIL_PalletKey DEFAULT "" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_CONTAINERDETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_CONTAINERDETAIL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_CONTAINERDETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_CONTAINERDETAIL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('CONTAINERDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CONTAINERDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CONTAINERDETAIL>>>'
GRANT INSERT ON CONTAINERDETAIL TO nsql
GRANT UPDATE ON CONTAINERDETAIL TO nsql
GRANT DELETE ON CONTAINERDETAIL TO nsql
GRANT SELECT ON CONTAINERDETAIL TO nsql
END
GO

-- =============================================
-- ADJUSTMENT 表 (调整表)
-- =============================================
CREATE TABLE ADJUSTMENT
(AdjustmentKey            char(10) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_ADJUSTMENT_StorerKey DEFAULT "" ,
ExternAdjustmentKey      char(30) NOT NULL
CONSTRAINT DF_ADJUSTMENT_ExternAdjustmentKey DEFAULT "" ,
AdjustmentDate           datetime NOT NULL
CONSTRAINT DF_ADJUSTMENT_AdjustmentDate DEFAULT CURRENT_Timestamp ,
Type                     char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENT_Type DEFAULT "0" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENT_Status DEFAULT "0"
CONSTRAINT CK_ADJUSTMENT_Status CHECK ( Status LIKE '[0-9]' ),
Notes                    text NULL,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_ADJUSTMENT_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_ADJUSTMENT_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_ADJUSTMENT_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_ADJUSTMENT_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_ADJUSTMENT_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('ADJUSTMENT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ADJUSTMENT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ADJUSTMENT>>>'
GRANT INSERT ON ADJUSTMENT TO nsql
GRANT UPDATE ON ADJUSTMENT TO nsql
GRANT DELETE ON ADJUSTMENT TO nsql
GRANT SELECT ON ADJUSTMENT TO nsql
END
GO

-- =============================================
-- ADJUSTMENTDETAIL 表 (调整明细表)
-- =============================================
CREATE TABLE ADJUSTMENTDETAIL
(AdjustmentKey            char(10) NOT NULL,
AdjustmentLineNumber     char(5) NOT NULL,
AdjustmentDetailSysId    int NULL ,
ExternAdjustmentKey      char(30) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_XAdjustmentKey DEFAULT "" ,
ExternLineNo             char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_XLineNo DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_Sku DEFAULT "" ,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_StorerKey DEFAULT "" ,
Lot                      char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_LOT DEFAULT "" ,
ID                       char(18) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_ID DEFAULT "" ,
Loc                      char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_Loc DEFAULT "UNKNOWN" ,
FromLoc                  char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_FromLoc DEFAULT "UNKNOWN" ,
ToLoc                    char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_ToLoc DEFAULT "UNKNOWN" ,
Qty                      int NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_Qty DEFAULT 0 ,
UOM                      char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_UOM DEFAULT "" ,
PackKey                  char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_PackKey DEFAULT "STD" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_Status DEFAULT "0"
CONSTRAINT CK_ADJUSTMENTDETAIL_Status CHECK ( Status LIKE '[0-9]' ),
ReasonCode               char(10) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_ReasonCode DEFAULT "" ,
Lottable01              char(18) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_LOTTABLE01 DEFAULT "" ,
Lottable02              char(18) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_LOTTABLE02 DEFAULT "" ,
Lottable03              char(18) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_LOTTABLE03 DEFAULT "" ,
Lottable04              datetime NULL ,
Lottable05              datetime NULL ,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_ADJUSTMENTDETAIL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('ADJUSTMENTDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ADJUSTMENTDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ADJUSTMENTDETAIL>>>'
GRANT INSERT ON ADJUSTMENTDETAIL TO nsql
GRANT UPDATE ON ADJUSTMENTDETAIL TO nsql
GRANT DELETE ON ADJUSTMENTDETAIL TO nsql
GRANT SELECT ON ADJUSTMENTDETAIL TO nsql
END
GO

-- =============================================
-- REPLENISHMENT 表 (补货表)
-- =============================================
CREATE TABLE REPLENISHMENT
(ReplenishmentKey         char(10) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_REPLENISHMENT_StorerKey DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_REPLENISHMENT_Sku DEFAULT "" ,
FromLoc                  char(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_FromLoc DEFAULT "UNKNOWN" ,
ToLoc                    char(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_ToLoc DEFAULT "UNKNOWN" ,
Lot                      char(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_Lot DEFAULT "" ,
ID                       char(18) NOT NULL
CONSTRAINT DF_REPLENISHMENT_ID DEFAULT "" ,
Qty                      int NOT NULL
CONSTRAINT DF_REPLENISHMENT_Qty DEFAULT 0 ,
Priority                 char(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_Priority DEFAULT "5" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_Status DEFAULT "0"
CONSTRAINT CK_REPLENISHMENT_Status CHECK ( Status LIKE '[0-9]' ),
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_REPLENISHMENT_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_REPLENISHMENT_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_REPLENISHMENT_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_REPLENISHMENT_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_REPLENISHMENT_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('REPLENISHMENT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE REPLENISHMENT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE REPLENISHMENT>>>'
GRANT INSERT ON REPLENISHMENT TO nsql
GRANT UPDATE ON REPLENISHMENT TO nsql
GRANT DELETE ON REPLENISHMENT TO nsql
GRANT SELECT ON REPLENISHMENT TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- 注意：此脚本包含主要表结构
-- 完整的表结构请参考原始 NEPISQL.sql 文件
