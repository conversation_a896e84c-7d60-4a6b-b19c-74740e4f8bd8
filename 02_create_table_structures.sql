-- =============================================
-- 创建表结构脚本
-- 功能：创建仓库管理系统的核心表结构
-- 用途：数据库重构后的表结构重建
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 核心事务表 - ITRN (Inventory Transaction)
-- =============================================
CREATE TABLE ITRN
(ItrnKey                  char(10) NOT NULL,
ItrnSysId                int      NULL,
TranType                 char(10) NOT NULL,
StorerKey                char(15) NOT NULL,
Sku                      char(20) NOT NULL,
Lot                      char(10) NOT NULL,
FromLoc                  char(10) NOT NULL,
FromID                   char(18) NOT NULL,
ToLoc                    char(10) NOT NULL,
ToID                     char(18) NOT NULL,
SourceKey                char(20) NULL,
SourceType               char(30) NULL,
Status                   char(10) NULL,
LOTTABLE01               char(18) NOT NULL
CONSTRAINT DF_Itrn_LOTTABLE01 DEFAULT "" ,
LOTTABLE02               char(18) NOT NULL
CONSTRAINT DF_Itrn_LOTTABLE02 DEFAULT "" ,
LOTTABLE03               char(18) NOT NULL
CONSTRAINT DF_Itrn_LOTTABLE03 DEFAULT "" ,
LOTTABLE04               datetime NULL ,
LOTTABLE05               datetime NULL ,
CaseCnt                  int       NOT NULL
CONSTRAINT DF_Itrn_CaseCnt   DEFAULT 0 ,
InnerPack                int       NOT NULL
CONSTRAINT DF_Itrn_Innerpack DEFAULT 0 ,
Qty                      int  NOT NULL
CONSTRAINT DF_Itrn_QTY DEFAULT 0 ,
Pallet                   int       NOT NULL
CONSTRAINT DF_Itrn_Pallet    DEFAULT 0 ,
Cube                     Float     NOT NULL
CONSTRAINT DF_Itrn_Cube      DEFAULT 0 ,
GrossWgt                 Float     NOT NULL
CONSTRAINT DF_Itrn_GrossWgt  DEFAULT 0 ,
NetWgt                   Float     NOT NULL
CONSTRAINT DF_Itrn_NetWgt    DEFAULT 0 ,
OtherUnit1               Float     NOT NULL
CONSTRAINT DF_Itrn_OtherUnit1    DEFAULT 0 ,
OtherUnit2               Float     NOT NULL
CONSTRAINT DF_Itrn_OtherUnit2    DEFAULT 0 ,
PackKey                  char(10)     NULL,
UOM                      char(10)     NULL,
UOMCalc                  int          NULL,
UOMQty                   int          NULL,
EffectiveDate            datetime NOT NULL
CONSTRAINT DF_ITRN_EffectiveDate DEFAULT CURRENT_Timestamp ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_ITRN_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_ITRN_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_ITRN_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_ITRN_EditWho DEFAULT USER ,
TrafficCop     char(1)        NULL,
ArchiveCop     char(1)        NULL
)
GO

IF OBJECT_ID('ITRN') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ITRN FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ITRN>>>'
GRANT INSERT ON ITRN TO nsql
GRANT UPDATE ON ITRN TO nsql
GRANT DELETE ON ITRN TO nsql
GRANT SELECT ON ITRN TO nsql
END
GO

-- =============================================
-- SKU 表 (Stock Keeping Unit)
-- =============================================
CREATE TABLE SKU
(StorerKey                char(15) NOT NULL,
Sku                      char(20) NOT NULL,
DESCR                    char(60) NULL,
SUSR1                    char(18) NULL,
SUSR2                    char(18) NULL,
SUSR3                    char(18) NULL,
SUSR4                    char(18) NULL,
SUSR5                    char(18) NULL,
MANUFACTURERSKU          char(20) NULL,
RETAILSKU                char(20) NULL,
ALTSKU                   char(20) NULL,
PACKKey                  char(10) NOT NULL
CONSTRAINT DF_SKU_Packkey DEFAULT "STD",
STDGROSSWGT              float NOT NULL
CONSTRAINT DF_SKU_StdGrossWgt DEFAULT 0 ,
STDNETWGT                float NOT NULL
CONSTRAINT DF_SKU_StdNetWgt DEFAULT 0 ,
STDCUBE                  float NOT NULL
CONSTRAINT DF_SKU_StdCube DEFAULT 0 ,
TARE                     float NOT NULL
CONSTRAINT DF_SKU_Tare DEFAULT 0 ,
CLASS                    char(10) NOT NULL
CONSTRAINT DF_SKU_Class DEFAULT "STD" ,
ACTIVE                   char(10) NOT NULL
CONSTRAINT DF_SKU_ACTIVE DEFAULT "1"  ,
SKUGROUP                 char(10) NOT NULL
CONSTRAINT DF_SKU_SKUGROUP DEFAULT "STD" ,
Tariffkey                char(10) NULL
CONSTRAINT DF_SKU_TARIFF DEFAULT "XXXXXXXXXX",
BUSR1                    char(30) NULL,
BUSR2                    Char(30) NULL,
BUSR3                    char(30) NULL,
BUSR4                    char(30) NULL,
BUSR5                    char(30) NULL,
LOTTABLE01LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll01 DEFAULT "" ,
LOTTABLE02LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll02 DEFAULT "" ,
LOTTABLE03LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll03 DEFAULT "" ,
LOTTABLE04LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll04 DEFAULT "" ,
LOTTABLE05LABEL          char(20) NOT NULL
CONSTRAINT DF_SKU_ll05 DEFAULT "" ,
NOTES1                   text NULL,
NOTES2                   text NULL,
PickCode                 char(10) NOT NULL
CONSTRAINT DF_SKU_PickCode DEFAULT "NSPFIFO",
StrategyKey              char(10) NOT NULL
CONSTRAINT DF_SKU_StrategyKey DEFAULT "STD",
CartonGroup              char(10) NOT NULL
CONSTRAINT DF_SKU_CartonGroup DEFAULT "STD" ,
PutCode                  char(10) NOT NULL
CONSTRAINT DF_SKU_PutCode DEFAULT "NSPPASTD" ,
PutawayLoc             char(10) NULL
CONSTRAINT DF_SKU_PutawayLoc DEFAULT "UNKNOWN" ,
PutawayZone               char(10) NULL
CONSTRAINT DF_SKU_Putawayzone DEFAULT "BULK" ,
InnerPack                int NOT NULL
CONSTRAINT DF_SKU_InnerPack DEFAULT 0,
Cube                     float NOT NULL
CONSTRAINT DF_SKU_Cube DEFAULT 0,
GrossWgt                 float NOT NULL
CONSTRAINT DF_SKU_GrossWgt DEFAULT 0,
NetWgt                   float NOT NULL
CONSTRAINT DF_SKU_NetWgt DEFAULT 0,
ABC                      char(5) NULL ,
CycleCountFrequency      int NULL,
LastCycleCount           datetime NULL,
ReorderPoint             int NULL,
ReorderQty               int NULL,
StdOrderCost             float NULL,
CarryCost                float NULL,
Price                    money NULL,
Cost                     money NULL,
ReceiptHoldCode          char(10) NOT NULL
CONSTRAINT DF_SKU_RHC DEFAULT "" ,
ReceiptInspectionLoc     char(10) NOT NULL
CONSTRAINT DF_SKU_RIL DEFAULT "QC" ,
OnReceiptCopyPackkey     char(10) NOT NULL
CONSTRAINT DF_SKU_ORCP DEFAULT "0" , -- "0" = no action, "1" = copy packkey to lottable01 on receipt
TrafficCop               char(1) NULL,
ArchiveCop               char(1) NULL,
IOFlag                   char (1) NULL,
TareWeight               float    NULL
CONSTRAINT DF_SKU_TareWeight DEFAULT 0 ,
LotxIdDetailOtherlabel1  char(30) NULL
CONSTRAINT DF_SKU_Other1 DEFAULT "Ser#" ,
LotxIdDetailOtherlabel2  char(30) NULL
CONSTRAINT DF_SKU_Other2 DEFAULT "CSID" ,
LotxIdDetailOtherlabel3  char(30) NULL
CONSTRAINT DF_SKU_Other3 DEFAULT "Other" ,
AvgCaseWeight            float    NULL
CONSTRAINT DF_SKU_AvgCaseWeght DEFAULT 0 ,
TolerancePct             float    NULL
CONSTRAINT DF_SKU_TolerancePct DEFAULT 0
)
GO

IF OBJECT_ID('SKU') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE SKU FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE SKU>>>'
GRANT INSERT ON SKU TO nsql
GRANT UPDATE ON SKU TO nsql
GRANT DELETE ON SKU TO nsql
GRANT SELECT ON SKU TO nsql
END
GO

-- =============================================
-- ID 表 (标识符表)
-- =============================================
CREATE TABLE ID
(Id                       char(18) NOT NULL
CONSTRAINT DF_ID_Id DEFAULT "" ,
Qty                      int NOT NULL
CONSTRAINT DF_ID_Qty DEFAULT 0 ,
Status                   char(10) NOT NULL
CONSTRAINT DF_ID_Status DEFAULT "OK" ,
Packkey                  char(10) NOT NULL
CONSTRAINT DF_ID_Packkey DEFAULT "STD" ,
PutAwayTI                int NOT NULL
CONSTRAINT DF_ID_PutAwayTi DEFAULT 0 ,
PutAwayHI                int NOT NULL
CONSTRAINT DF_ID_PutAwayHi DEFAULT 0 ,
TrafficCop               char(1) NULL,
ArchiveCop               char(1) NULL
)
GO

IF OBJECT_ID('ID') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE ID FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE ID>>>'
GRANT INSERT ON ID TO nsql
GRANT UPDATE ON ID TO nsql
GRANT DELETE ON ID TO nsql
GRANT SELECT ON ID TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- LOTATTRIBUTE 表 (批次属性表)
-- =============================================
CREATE TABLE LOTATTRIBUTE
(StorerKey                char(15) NOT NULL,
Sku                      char(20) NOT NULL,
Lot                      char(10) NOT NULL,
Lottable01               char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_LOTTABLE01 DEFAULT "" ,
Lottable02               char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_LOTTABLE02 DEFAULT "" ,
Lottable03               char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_LOTTABLE03 DEFAULT "" ,
Lottable04               datetime NULL ,
Lottable05               datetime NULL ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_EditWho DEFAULT USER ,
TrafficCop               char(1) NULL,
ArchiveCop               char(1) NULL
)
GO

IF OBJECT_ID('LOTATTRIBUTE') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LOTATTRIBUTE FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LOTATTRIBUTE>>>'
GRANT INSERT ON LOTATTRIBUTE TO nsql
GRANT UPDATE ON LOTATTRIBUTE TO nsql
GRANT DELETE ON LOTATTRIBUTE TO nsql
GRANT SELECT ON LOTATTRIBUTE TO nsql
END
GO

-- =============================================
-- LOT 表 (批次表)
-- =============================================
CREATE TABLE LOT
(Lot                      char(10)  NOT NULL,
StorerKey                char(15)  NOT NULL,
Sku                      char(20)  NOT NULL,
CaseCnt                  int       NOT NULL
CONSTRAINT DF_Lot_CaseCnt   DEFAULT 0 ,
InnerPack                int       NOT NULL
CONSTRAINT DF_Lot_Innerpack DEFAULT 0 ,
Qty                      int       NOT NULL
CONSTRAINT DF_LOT_QTY DEFAULT 0
CONSTRAINT CK_LOT_QTY CHECK (Qty >= 0),
Pallet                   int       NOT NULL
CONSTRAINT DF_Lot_Pallet    DEFAULT 0 ,
Cube                     Float     NOT NULL
CONSTRAINT DF_Lot_Cube      DEFAULT 0 ,
GrossWgt                 Float     NOT NULL
CONSTRAINT DF_Lot_GrossWgt  DEFAULT 0 ,
NetWgt                   Float     NOT NULL
CONSTRAINT DF_Lot_NetWgt    DEFAULT 0 ,
OtherUnit1               Float     NOT NULL
CONSTRAINT DF_Lot_OtherUnit1    DEFAULT 0 ,
OtherUnit2               Float     NOT NULL
CONSTRAINT DF_Lot_OtherUnit2    DEFAULT 0 ,
QtyPreAllocated          int       NOT NULL
CONSTRAINT DF_LOT_QtyPreAllocated  DEFAULT 0 ,
CONSTRAINT CK_LOT_QtyPreAllocated  CHECK( QtyPreAllocated >= 0),
GrossWgtpreAllocated     float       NOT NULL
CONSTRAINT DF_LOT_GWpreAllocated DEFAULT 0,
NetWgtpreAllocated       float       NOT NULL
CONSTRAINT DF_LOT_NWpreAllocated DEFAULT 0,
QtyAllocated             int       NOT NULL
CONSTRAINT DF_LOT_QtyAllocated DEFAULT 0 ,
GrossWgtAllocated        float       NOT NULL
CONSTRAINT DF_LOT_GWAllocated DEFAULT 0,
NetWgtAllocated          float       NOT NULL
CONSTRAINT DF_LOT_NWAllocated DEFAULT 0,
QtyPicked                int       NOT NULL
CONSTRAINT DF_LOT_QtyPicked DEFAULT 0 ,
GrossWgtPicked           float       NOT NULL
CONSTRAINT DF_LOT_GWPicked DEFAULT 0,
NetWgtPicked             float       NOT NULL
CONSTRAINT DF_LOT_NWPicked DEFAULT 0,
QtyOnHold                int       NOT NULL
CONSTRAINT DF_LOT_QtyOnHold DEFAULT 0 ,
Status                   char(10)  NOT NULL
CONSTRAINT DF_LOT_Status DEFAULT "OK" ,
ArchiveQty               int       NOT NULL
CONSTRAINT DF_LOT_ArchiveQty DEFAULT 0 ,
ArchiveDate              DateTime  NOT NULL
CONSTRAINT DF_LOT_ArchiveDate DEFAULT '01/01/1901' ,
TrafficCop               char(1)   NULL,
ArchiveCop               char(1)   NULL,
CONSTRAINT CK_LOT_01 CHECK (Qty >= QtyPreAllocated + QtyAllocated + QtyPicked)
)
GO

IF OBJECT_ID('LOT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LOT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LOT>>>'
GRANT INSERT ON LOT TO nsql
GRANT UPDATE ON LOT TO nsql
GRANT DELETE ON LOT TO nsql
GRANT SELECT ON LOT TO nsql
END
GO

-- =============================================
-- LOC 表 (位置表)
-- =============================================
CREATE TABLE LOC
(Loc                      char(10) NOT NULL
CONSTRAINT DF_LOC_Loc DEFAULT "UNKNOWN"
CONSTRAINT CK_LOC_Loc_01 CHECK (NOT Loc = ""),
Cube                     float
CONSTRAINT DF_LOC_cube DEFAULT 0 ,
Length                   float
CONSTRAINT DF_LOC_length DEFAULT 0 ,
Width                    float
CONSTRAINT DF_LOC_width DEFAULT 0 ,
Height                   float
CONSTRAINT DF_LOC_height DEFAULT 0 ,
LocationType                char(10) NOT NULL
CONSTRAINT DF_LOC_LocationType DEFAULT "OTHER" ,
LocationFlag                char(10) NOT NULL  -- Hold/Damage
CONSTRAINT DF_LOC_LocationFlag DEFAULT "NONE" ,
LocationHandling            char(10) NOT NULL -- Case/Pallet/Other
CONSTRAINT DF_LOC_LocationHdl DEFAULT "1" ,
LocationCategory            char(10) NOT NULL -- DOUBLEDEEP, ASRS etc
CONSTRAINT DF_LOC_LocationCategory DEFAULT "OTHER" ,
LogicalLocation             char(18)
CONSTRAINT DF_LOC_LogicalLocation DEFAULT "" ,
CubicCapacity               float
CONSTRAINT DF_LOC_CubicCapacity DEFAULT 0 ,
WeightCapacity               float
CONSTRAINT DF_LOC_WeightCapacity DEFAULT 0 ,
Status                      char(10)
CONSTRAINT DF_LOC_Status DEFAULT "OK" ,
LoseId                      char(1) NOT NULL
CONSTRAINT DF_LOC_Loseid DEFAULT "0" ,
Facility                         char(5) NOT NULL
CONSTRAINT DF_LOC_Facility DEFAULT "F1" ,
ABC                              char(1) NOT NULL
CONSTRAINT DF_LOC_ABC DEFAULT "B" ,
PickZone                         char(10) NOT NULL
CONSTRAINT DF_LOC_PickZone DEFAULT "" ,
PutawayZone                 char(10) NOT NULL
CONSTRAINT DF_LOC_PutAwayZone DEFAULT "RACK" ,
SectionKey                  char(10) NOT NULL
CONSTRAINT DF_LOC_SectionKey DEFAULT "FACILITY" ,
PickMethod                  char(1)   NOT NULL
CONSTRAINT DF_LOC_PickMethod DEFAULT "" ,
CommingleSku                char(1)   NOT NULL
CONSTRAINT DF_LOC_CommingleSku DEFAULT "1",
CommingleLot                char(1)   NOT NULL
CONSTRAINT DF_LOC_CommingleLot DEFAULT "1",
LocLevel                    int NOT NULL
CONSTRAINT DF_LOC_LocLevel  DEFAULT 0,
Xcoord                      int NOT NULL
CONSTRAINT DF_LOC_Xcoord  DEFAULT 0,
Ycoord                      int NOT NULL
CONSTRAINT DF_LOC_Ycoord  DEFAULT 0,
Zcoord                      int NOT NULL
CONSTRAINT DF_LOC_Zcoord  DEFAULT 0,
TrafficCop     char(1)        NULL,
ArchiveCop     char(1)        NULL
)
GO

IF OBJECT_ID('LOC') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LOC FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LOC>>>'
GRANT INSERT ON LOC TO nsql
GRANT UPDATE ON LOC TO nsql
GRANT DELETE ON LOC TO nsql
GRANT SELECT ON LOC TO nsql
END
GO

-- 注意：此脚本包含核心表结构
-- 更多表结构请参考原始文件或后续脚本
