-- =============================================
-- Oracle版本 - 创建航空运单触发器脚本 (第七部分)
-- 功能：创建仓库管理系统的航空运单触发器集合 (Oracle版本)
-- 用途：MASTER AIRWAY BILL、HOUSE AIRWAY BILL、MBOL等航空运输触发器
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中的108个SQL Server触发器转换为Oracle语法 (航空运单部分)
-- 包含：主运单、分运单、MBOL等航空运输管理触发器
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- MASTER AIRWAY BILL管理触发器 (从SQL Server转换)
-- =============================================

-- MASTERAIRWAYBILL表的插入触发器 (从ntrMasterAirWayBillAdd转换)
CREATE OR REPLACE TRIGGER NTR_MASTERAIRWAYBILL_ADD
BEFORE INSERT ON MASTERAIRWAYBILL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成主运单键如果为空
    IF :NEW.MasterAirWayBillKey IS NULL OR TRIM(:NEW.MasterAirWayBillKey) = ' ' THEN
        SELECT 'MAWB' || LPAD(TO_CHAR(SEQ_MASTERAIRWAYBILL.NEXTVAL), 6, '0') INTO :NEW.MasterAirWayBillKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MASTERAIRWAYBILL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MASTERAIRWAYBILL_ADD (主运单插入触发器)';

-- MASTERAIRWAYBILL表的更新触发器 (从ntrMasterAirWayBillUpdate转换)
CREATE OR REPLACE TRIGGER NTR_MASTERAIRWAYBILL_UPDATE
BEFORE UPDATE ON MASTERAIRWAYBILL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MASTERAIRWAYBILL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MASTERAIRWAYBILL_UPDATE (主运单更新触发器)';

-- MASTERAIRWAYBILL表的删除触发器 (从ntrMasterAirWayBillDelete转换)
CREATE OR REPLACE TRIGGER NTR_MASTERAIRWAYBILL_DELETE
BEFORE DELETE ON MASTERAIRWAYBILL
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有主运单明细
    SELECT COUNT(*) INTO v_cnt
    FROM MASTERAIRWAYBILLDETAIL
    WHERE MasterAirWayBillKey = :OLD.MasterAirWayBillKey;
    
    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20022, 'Cannot delete master airway bill with existing details');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MASTERAIRWAYBILL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MASTERAIRWAYBILL_DELETE (主运单删除触发器)';

-- MASTERAIRWAYBILLDETAIL表的插入触发器 (从ntrMasterAirWayBillDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_MASTERAIRWAYBILLDETAIL_ADD
BEFORE INSERT ON MASTERAIRWAYBILLDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 生成主运单明细键如果为空
    IF :NEW.MasterAirWayBillDetailKey IS NULL OR TRIM(:NEW.MasterAirWayBillDetailKey) = ' ' THEN
        SELECT 'MAWBD' || LPAD(TO_CHAR(SEQ_MASTERAIRWAYBILLDETAIL.NEXTVAL), 5, '0') INTO :NEW.MasterAirWayBillDetailKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MASTERAIRWAYBILLDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MASTERAIRWAYBILLDETAIL_ADD (主运单明细插入触发器)';

-- MASTERAIRWAYBILLDETAIL表的更新触发器 (从ntrMasterAirWayBillDetailUpd转换)
CREATE OR REPLACE TRIGGER NTR_MASTERAIRWAYBILLDETAIL_UPD
BEFORE UPDATE ON MASTERAIRWAYBILLDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MASTERAIRWAYBILLDETAIL_UPD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MASTERAIRWAYBILLDETAIL_UPD (主运单明细更新触发器)';

-- MASTERAIRWAYBILLDETAIL表的删除触发器 (从ntrMasterAirWayBillDetailDel转换)
CREATE OR REPLACE TRIGGER NTR_MASTERAIRWAYBILLDETAIL_DEL
AFTER DELETE ON MASTERAIRWAYBILLDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'MASTERAIRWAYBILLDETAIL',
        'RECORD_DELETE',
        'Master Airway Bill Detail deleted: ' || :OLD.MasterAirWayBillKey || '/' || :OLD.MasterAirWayBillDetailKey,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MASTERAIRWAYBILLDETAIL_DEL');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MASTERAIRWAYBILLDETAIL_DEL (主运单明细删除触发器)';

-- =============================================
-- HOUSE AIRWAY BILL管理触发器 (从SQL Server转换)
-- =============================================

-- HOUSEAIRWAYBILL表的插入触发器 (从ntrHouseAirWayBillAdd转换)
CREATE OR REPLACE TRIGGER NTR_HOUSEAIRWAYBILL_ADD
BEFORE INSERT ON HOUSEAIRWAYBILL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成分运单键如果为空
    IF :NEW.HouseAirWayBillKey IS NULL OR TRIM(:NEW.HouseAirWayBillKey) = ' ' THEN
        SELECT 'HAWB' || LPAD(TO_CHAR(SEQ_HOUSEAIRWAYBILL.NEXTVAL), 6, '0') INTO :NEW.HouseAirWayBillKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_HOUSEAIRWAYBILL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_HOUSEAIRWAYBILL_ADD (分运单插入触发器)';

-- HOUSEAIRWAYBILL表的更新触发器 (从ntrHouseAirWayBillUpdate转换)
CREATE OR REPLACE TRIGGER NTR_HOUSEAIRWAYBILL_UPDATE
BEFORE UPDATE ON HOUSEAIRWAYBILL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_HOUSEAIRWAYBILL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_HOUSEAIRWAYBILL_UPDATE (分运单更新触发器)';

-- HOUSEAIRWAYBILL表的删除触发器 (从ntrHouseAirWayBillDelete转换)
CREATE OR REPLACE TRIGGER NTR_HOUSEAIRWAYBILL_DELETE
BEFORE DELETE ON HOUSEAIRWAYBILL
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有分运单明细
    SELECT COUNT(*) INTO v_cnt
    FROM HOUSEAIRWAYBILLDETAIL
    WHERE HouseAirWayBillKey = :OLD.HouseAirWayBillKey;
    
    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20023, 'Cannot delete house airway bill with existing details');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_HOUSEAIRWAYBILL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_HOUSEAIRWAYBILL_DELETE (分运单删除触发器)';

-- HOUSEAIRWAYBILLDETAIL表的插入触发器 (从ntrHouseAirWayBillDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_HOUSEAIRWAYBILLDETAIL_ADD
BEFORE INSERT ON HOUSEAIRWAYBILLDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 生成分运单明细键如果为空
    IF :NEW.HouseAirWayBillDetailKey IS NULL OR TRIM(:NEW.HouseAirWayBillDetailKey) = ' ' THEN
        SELECT 'HAWBD' || LPAD(TO_CHAR(SEQ_HOUSEAIRWAYBILLDETAIL.NEXTVAL), 5, '0') INTO :NEW.HouseAirWayBillDetailKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_HOUSEAIRWAYBILLDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_HOUSEAIRWAYBILLDETAIL_ADD (分运单明细插入触发器)';

-- HOUSEAIRWAYBILLDETAIL表的更新触发器 (从ntrHouseAirWayBillDetailUpd转换)
CREATE OR REPLACE TRIGGER NTR_HOUSEAIRWAYBILLDETAIL_UPD
BEFORE UPDATE ON HOUSEAIRWAYBILLDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_HOUSEAIRWAYBILLDETAIL_UPD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_HOUSEAIRWAYBILLDETAIL_UPD (分运单明细更新触发器)';

-- HOUSEAIRWAYBILLDETAIL表的删除触发器 (从ntrHouseAirWayBillDetailDel转换)
CREATE OR REPLACE TRIGGER NTR_HOUSEAIRWAYBILLDETAIL_DEL
AFTER DELETE ON HOUSEAIRWAYBILLDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'HOUSEAIRWAYBILLDETAIL',
        'RECORD_DELETE',
        'House Airway Bill Detail deleted: ' || :OLD.HouseAirWayBillKey || '/' || :OLD.HouseAirWayBillDetailKey,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_HOUSEAIRWAYBILLDETAIL_DEL');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_HOUSEAIRWAYBILLDETAIL_DEL (分运单明细删除触发器)';

-- =============================================
-- MBOL管理触发器 (从SQL Server转换)
-- =============================================

-- MBOL表的插入触发器 (从ntrMbolHeaderAdd转换)
CREATE OR REPLACE TRIGGER NTR_MBOL_ADD
BEFORE INSERT ON MBOL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;

    -- 生成MBOL键如果为空
    IF :NEW.MBOLKey IS NULL OR TRIM(:NEW.MBOLKey) = ' ' THEN
        SELECT 'MBOL' || LPAD(TO_CHAR(SEQ_MBOL.NEXTVAL), 6, '0') INTO :NEW.MBOLKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MBOL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MBOL_ADD (MBOL插入触发器)';

-- MBOL表的更新触发器 (从ntrMBOLHeaderUpdate转换)
CREATE OR REPLACE TRIGGER NTR_MBOL_UPDATE
BEFORE UPDATE ON MBOL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MBOL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MBOL_UPDATE (MBOL更新触发器)';

-- MBOL表的删除触发器 (从ntrMbolHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_MBOL_DELETE
BEFORE DELETE ON MBOL
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有MBOL明细
    SELECT COUNT(*) INTO v_cnt
    FROM MBOLDETAIL
    WHERE MBOLKey = :OLD.MBOLKey;

    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20024, 'Cannot delete MBOL with existing MBOL details');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MBOL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MBOL_DELETE (MBOL删除触发器)';

-- MBOLDETAIL表的插入触发器 (从ntrMBOLDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_MBOLDETAIL_ADD
BEFORE INSERT ON MBOLDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 生成MBOL明细键如果为空
    IF :NEW.MBOLDetailKey IS NULL OR TRIM(:NEW.MBOLDetailKey) = ' ' THEN
        SELECT 'MBOLD' || LPAD(TO_CHAR(SEQ_MBOLDETAIL.NEXTVAL), 5, '0') INTO :NEW.MBOLDetailKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MBOLDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MBOLDETAIL_ADD (MBOL明细插入触发器)';

-- MBOLDETAIL表的更新触发器 (从ntrMBOLDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_MBOLDETAIL_UPDATE
BEFORE UPDATE ON MBOLDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MBOLDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MBOLDETAIL_UPDATE (MBOL明细更新触发器)';

-- MBOLDETAIL表的删除触发器 (从ntrMBOLDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_MBOLDETAIL_DELETE
AFTER DELETE ON MBOLDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'MBOLDETAIL',
        'RECORD_DELETE',
        'MBOL Detail deleted: ' || :OLD.MBOLKey || '/' || :OLD.MBOLDetailKey,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_MBOLDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_MBOLDETAIL_DELETE (MBOL明细删除触发器)';

-- 提交事务
COMMIT;

PROMPT '>>> Oracle航空运单触发器脚本执行完成';
PROMPT '>>> 已创建主运单、分运单和MBOL管理触发器 (18个)';
PROMPT '>>> 所有航空运单触发器创建完成！';
