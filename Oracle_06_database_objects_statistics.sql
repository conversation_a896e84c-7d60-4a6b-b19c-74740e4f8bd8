-- =============================================
-- Oracle版本 - 数据库对象统计验证脚本
-- 功能：统计和验证仓库管理系统的数据库对象 (Oracle版本)
-- 用途：部署验证、对象清单、完整性检查等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：验证所有数据库对象是否正确创建并生成统计报告
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;
SET PAGESIZE 1000;
SET LINESIZE 200;

-- =============================================
-- 数据库对象统计
-- =============================================

PROMPT '=============================================';
PROMPT 'Oracle版本 - 仓库管理系统数据库对象统计报告';
PROMPT '生成时间: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS');
PROMPT '数据库版本: ' || (SELECT BANNER FROM V$VERSION WHERE ROWNUM = 1);
PROMPT '当前用户: ' || USER;
PROMPT '=============================================';
PROMPT '';

-- 表统计
PROMPT '=== 表统计 ===';
SELECT 
    'Tables' AS ObjectType,
    COUNT(*) AS ObjectCount
FROM USER_TABLES;

PROMPT '';
PROMPT '=== 表清单 (按字母顺序) ===';
SELECT 
    RPAD(TABLE_NAME, 30) AS TableName,
    RPAD(TO_CHAR(NUM_ROWS), 10) AS NumRows,
    RPAD(TABLESPACE_NAME, 20) AS TablespaceName,
    CREATED
FROM USER_TABLES
ORDER BY TABLE_NAME;

PROMPT '';

-- 索引统计
PROMPT '=== 索引统计 ===';
SELECT 
    'Indexes' AS ObjectType,
    COUNT(*) AS ObjectCount
FROM USER_INDEXES
WHERE INDEX_TYPE != 'LOB';

PROMPT '';
PROMPT '=== 索引清单 (按表名分组) ===';
SELECT 
    RPAD(TABLE_NAME, 30) AS TableName,
    RPAD(INDEX_NAME, 30) AS IndexName,
    RPAD(INDEX_TYPE, 15) AS IndexType,
    RPAD(UNIQUENESS, 10) AS Uniqueness,
    STATUS
FROM USER_INDEXES
WHERE INDEX_TYPE != 'LOB'
ORDER BY TABLE_NAME, INDEX_NAME;

PROMPT '';

-- 序列统计
PROMPT '=== 序列统计 ===';
SELECT 
    'Sequences' AS ObjectType,
    COUNT(*) AS ObjectCount
FROM USER_SEQUENCES;

PROMPT '';
PROMPT '=== 序列清单 ===';
SELECT 
    RPAD(SEQUENCE_NAME, 30) AS SequenceName,
    RPAD(TO_CHAR(MIN_VALUE), 15) AS MinValue,
    RPAD(TO_CHAR(MAX_VALUE), 15) AS MaxValue,
    RPAD(TO_CHAR(INCREMENT_BY), 10) AS IncrementBy,
    RPAD(TO_CHAR(LAST_NUMBER), 15) AS LastNumber,
    RPAD(CYCLE_FLAG, 5) AS CycleFlag
FROM USER_SEQUENCES
ORDER BY SEQUENCE_NAME;

PROMPT '';

-- 视图统计
PROMPT '=== 视图统计 ===';
SELECT 
    'Views' AS ObjectType,
    COUNT(*) AS ObjectCount
FROM USER_VIEWS;

PROMPT '';
PROMPT '=== 视图清单 ===';
SELECT 
    RPAD(VIEW_NAME, 40) AS ViewName,
    RPAD(TO_CHAR(LENGTH(TEXT)), 10) AS TextLength,
    CREATED
FROM USER_VIEWS
ORDER BY VIEW_NAME;

PROMPT '';

-- 存储过程统计
PROMPT '=== 存储过程统计 ===';
SELECT 
    OBJECT_TYPE AS ObjectType,
    COUNT(*) AS ObjectCount
FROM USER_OBJECTS
WHERE OBJECT_TYPE IN ('PROCEDURE', 'FUNCTION', 'PACKAGE')
GROUP BY OBJECT_TYPE
ORDER BY OBJECT_TYPE;

PROMPT '';
PROMPT '=== 存储过程清单 ===';
SELECT 
    RPAD(OBJECT_NAME, 40) AS ObjectName,
    RPAD(OBJECT_TYPE, 15) AS ObjectType,
    RPAD(STATUS, 10) AS Status,
    CREATED,
    LAST_DDL_TIME
FROM USER_OBJECTS
WHERE OBJECT_TYPE IN ('PROCEDURE', 'FUNCTION', 'PACKAGE')
ORDER BY OBJECT_TYPE, OBJECT_NAME;

PROMPT '';

-- 触发器统计
PROMPT '=== 触发器统计 ===';
SELECT 
    'Triggers' AS ObjectType,
    COUNT(*) AS ObjectCount
FROM USER_TRIGGERS;

PROMPT '';
PROMPT '=== 触发器清单 ===';
SELECT 
    RPAD(TRIGGER_NAME, 40) AS TriggerName,
    RPAD(TABLE_NAME, 30) AS TableName,
    RPAD(TRIGGERING_EVENT, 20) AS TriggeringEvent,
    RPAD(STATUS, 10) AS Status
FROM USER_TRIGGERS
ORDER BY TABLE_NAME, TRIGGER_NAME;

PROMPT '';

-- 约束统计
PROMPT '=== 约束统计 ===';
SELECT 
    CONSTRAINT_TYPE,
    CASE CONSTRAINT_TYPE
        WHEN 'P' THEN 'Primary Key'
        WHEN 'R' THEN 'Foreign Key'
        WHEN 'U' THEN 'Unique'
        WHEN 'C' THEN 'Check'
        ELSE 'Other'
    END AS ConstraintTypeName,
    COUNT(*) AS ConstraintCount
FROM USER_CONSTRAINTS
GROUP BY CONSTRAINT_TYPE
ORDER BY CONSTRAINT_TYPE;

PROMPT '';

-- 主键统计
PROMPT '=== 主键清单 ===';
SELECT 
    RPAD(TABLE_NAME, 30) AS TableName,
    RPAD(CONSTRAINT_NAME, 30) AS ConstraintName,
    RPAD(STATUS, 10) AS Status
FROM USER_CONSTRAINTS
WHERE CONSTRAINT_TYPE = 'P'
ORDER BY TABLE_NAME;

PROMPT '';

-- 对象状态检查
PROMPT '=== 对象状态检查 ===';
SELECT 
    OBJECT_TYPE,
    STATUS,
    COUNT(*) AS ObjectCount
FROM USER_OBJECTS
GROUP BY OBJECT_TYPE, STATUS
ORDER BY OBJECT_TYPE, STATUS;

PROMPT '';

-- 无效对象检查
PROMPT '=== 无效对象检查 ===';
SELECT 
    RPAD(OBJECT_NAME, 40) AS ObjectName,
    RPAD(OBJECT_TYPE, 15) AS ObjectType,
    CREATED,
    LAST_DDL_TIME
FROM USER_OBJECTS
WHERE STATUS = 'INVALID'
ORDER BY OBJECT_TYPE, OBJECT_NAME;

PROMPT '';

-- 表空间使用情况
PROMPT '=== 表空间使用情况 ===';
SELECT 
    RPAD(TABLESPACE_NAME, 20) AS TablespaceName,
    COUNT(*) AS TableCount
FROM USER_TABLES
WHERE TABLESPACE_NAME IS NOT NULL
GROUP BY TABLESPACE_NAME
ORDER BY TABLESPACE_NAME;

PROMPT '';

-- 核心业务表验证
PROMPT '=== 核心业务表验证 ===';
DECLARE
    v_count NUMBER;
    v_table_name VARCHAR2(30);
    TYPE table_list_type IS TABLE OF VARCHAR2(30);
    core_tables table_list_type := table_list_type(
        'STORER', 'SKU', 'LOC', 'LOTxLOCxID', 'ITRN',
        'ORDERS', 'ORDERDETAIL', 'PICKHEADER', 'PICKDETAIL',
        'RECEIPT', 'RECEIPTDETAIL', 'ASN', 'ASNDETAIL',
        'WAVE', 'CARTONIZATION', 'PACK', 'LOT', 'ID'
    );
BEGIN
    DBMS_OUTPUT.PUT_LINE('核心表名                      | 状态');
    DBMS_OUTPUT.PUT_LINE('------------------------------|--------');
    
    FOR i IN 1..core_tables.COUNT LOOP
        v_table_name := core_tables(i);
        
        SELECT COUNT(*) INTO v_count
        FROM USER_TABLES
        WHERE TABLE_NAME = v_table_name;
        
        IF v_count > 0 THEN
            DBMS_OUTPUT.PUT_LINE(RPAD(v_table_name, 30) || '| 存在');
        ELSE
            DBMS_OUTPUT.PUT_LINE(RPAD(v_table_name, 30) || '| 缺失');
        END IF;
    END LOOP;
END;
/

PROMPT '';

-- 核心存储过程验证
PROMPT '=== 核心存储过程验证 ===';
DECLARE
    v_count NUMBER;
    v_proc_name VARCHAR2(40);
    TYPE proc_list_type IS TABLE OF VARCHAR2(40);
    core_procs proc_list_type := proc_list_type(
        'nsp_logerror', 'nspg_getkey', 'nspUOMCONV', 'nsp_lotgen',
        'nspItrnAddDeposit', 'nspItrnAddWithdrawal', 'nspItrnAddMove',
        'nspOrderAllocate', 'nspPickConfirm', 'nspWaveRelease',
        'nspBillingRun', 'nspInventoryReport', 'nspTaskManagerGetNextTask'
    );
BEGIN
    DBMS_OUTPUT.PUT_LINE('核心存储过程名                          | 状态');
    DBMS_OUTPUT.PUT_LINE('---------------------------------------|--------');
    
    FOR i IN 1..core_procs.COUNT LOOP
        v_proc_name := core_procs(i);
        
        SELECT COUNT(*) INTO v_count
        FROM USER_OBJECTS
        WHERE OBJECT_NAME = UPPER(v_proc_name)
        AND OBJECT_TYPE = 'PROCEDURE';
        
        IF v_count > 0 THEN
            DBMS_OUTPUT.PUT_LINE(RPAD(v_proc_name, 39) || '| 存在');
        ELSE
            DBMS_OUTPUT.PUT_LINE(RPAD(v_proc_name, 39) || '| 缺失');
        END IF;
    END LOOP;
END;
/

PROMPT '';

-- 总体统计汇总
PROMPT '=== 总体统计汇总 ===';
SELECT 
    '表' AS ObjectType,
    (SELECT COUNT(*) FROM USER_TABLES) AS ObjectCount
FROM DUAL
UNION ALL
SELECT 
    '索引' AS ObjectType,
    (SELECT COUNT(*) FROM USER_INDEXES WHERE INDEX_TYPE != 'LOB') AS ObjectCount
FROM DUAL
UNION ALL
SELECT 
    '序列' AS ObjectType,
    (SELECT COUNT(*) FROM USER_SEQUENCES) AS ObjectCount
FROM DUAL
UNION ALL
SELECT 
    '视图' AS ObjectType,
    (SELECT COUNT(*) FROM USER_VIEWS) AS ObjectCount
FROM DUAL
UNION ALL
SELECT 
    '存储过程' AS ObjectType,
    (SELECT COUNT(*) FROM USER_OBJECTS WHERE OBJECT_TYPE = 'PROCEDURE') AS ObjectCount
FROM DUAL
UNION ALL
SELECT 
    '函数' AS ObjectType,
    (SELECT COUNT(*) FROM USER_OBJECTS WHERE OBJECT_TYPE = 'FUNCTION') AS ObjectCount
FROM DUAL
UNION ALL
SELECT 
    '触发器' AS ObjectType,
    (SELECT COUNT(*) FROM USER_TRIGGERS) AS ObjectCount
FROM DUAL
UNION ALL
SELECT 
    '约束' AS ObjectType,
    (SELECT COUNT(*) FROM USER_CONSTRAINTS) AS ObjectCount
FROM DUAL;

PROMPT '';
PROMPT '=============================================';
PROMPT 'Oracle版本 - 仓库管理系统数据库对象统计完成！';
PROMPT '报告生成时间: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS');
PROMPT '=============================================';
