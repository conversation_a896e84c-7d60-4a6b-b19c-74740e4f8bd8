-- =============================================
-- 创建附加表结构脚本 (第二部分)
-- 功能：创建仓库管理系统的扩展表结构
-- 用途：数据库重构后的附加表结构重建
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- CASEMANIFEST 表 (箱单表)
-- =============================================
CREATE TABLE CASEMANIFEST
(CaseManifestKey          char(20) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_CASEMANIFEST_StorerKey DEFAULT "" ,
OrderKey                 char(10) NOT NULL
CONSTRAINT DF_CASEMANIFEST_OrderKey DEFAULT "" ,
CaseID                   char(10) NOT NULL
CONSTRAINT DF_CASEMANIFEST_CaseID DEFAULT "" ,
CartonType               char(10) NOT NULL
CONSTRAINT DF_CASEMANIFEST_CartonType DEFAULT "" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_CASEMANIFEST_Status DEFAULT "0"
CONSTRAINT CK_CASEMANIFEST_Status CHECK ( Status LIKE '[0-9]' ),
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_CASEMANIFEST_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_CASEMANIFEST_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_CASEMANIFEST_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_CASEMANIFEST_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_CASEMANIFEST_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('CASEMANIFEST') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CASEMANIFEST FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CASEMANIFEST>>>'
GRANT INSERT ON CASEMANIFEST TO nsql
GRANT UPDATE ON CASEMANIFEST TO nsql
GRANT DELETE ON CASEMANIFEST TO nsql
GRANT SELECT ON CASEMANIFEST TO nsql
END
GO

-- =============================================
-- PHYSICAL 表 (盘点表)
-- =============================================
CREATE TABLE PHYSICAL
(PhysicalKey              char(10) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_PHYSICAL_StorerKey DEFAULT "" ,
PhysicalDate             datetime NOT NULL
CONSTRAINT DF_PHYSICAL_PhysicalDate DEFAULT CURRENT_Timestamp ,
Type                     char(10) NOT NULL
CONSTRAINT DF_PHYSICAL_Type DEFAULT "0" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_PHYSICAL_Status DEFAULT "0"
CONSTRAINT CK_PHYSICAL_Status CHECK ( Status LIKE '[0-9]' ),
Notes                    text NULL,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_PHYSICAL_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_PHYSICAL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PHYSICAL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PHYSICAL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PHYSICAL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('PHYSICAL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PHYSICAL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PHYSICAL>>>'
GRANT INSERT ON PHYSICAL TO nsql
GRANT UPDATE ON PHYSICAL TO nsql
GRANT DELETE ON PHYSICAL TO nsql
GRANT SELECT ON PHYSICAL TO nsql
END
GO

-- =============================================
-- HOSTINTERFACE 表 (主机接口表)
-- =============================================
CREATE TABLE HOSTINTERFACE
(HostInterfaceKey         char(10) NOT NULL,
InterfaceType            char(10) NOT NULL
CONSTRAINT DF_HOSTINTERFACE_InterfaceType DEFAULT "" ,
Direction                char(10) NOT NULL
CONSTRAINT DF_HOSTINTERFACE_Direction DEFAULT "IN" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_HOSTINTERFACE_Status DEFAULT "0"
CONSTRAINT CK_HOSTINTERFACE_Status CHECK ( Status LIKE '[0-9]' ),
FileName                 char(255) NOT NULL
CONSTRAINT DF_HOSTINTERFACE_FileName DEFAULT "" ,
FileContent              text NULL,
ProcessedDate            datetime NULL,
ErrorMessage             text NULL,
AddDate                  datetime NOT NULL
CONSTRAINT DF_HOSTINTERFACE_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_HOSTINTERFACE_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_HOSTINTERFACE_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_HOSTINTERFACE_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('HOSTINTERFACE') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE HOSTINTERFACE FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE HOSTINTERFACE>>>'
GRANT INSERT ON HOSTINTERFACE TO nsql
GRANT UPDATE ON HOSTINTERFACE TO nsql
GRANT DELETE ON HOSTINTERFACE TO nsql
GRANT SELECT ON HOSTINTERFACE TO nsql
END
GO

-- =============================================
-- CLPORDER 表 (CLP订单表)
-- =============================================
CREATE TABLE CLPORDER
(CLPOrderKey              char(10) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_CLPORDER_StorerKey DEFAULT "" ,
ExternCLPOrderKey        char(30) NOT NULL
CONSTRAINT DF_CLPORDER_ExternCLPOrderKey DEFAULT "" ,
CLPOrderDate             datetime NOT NULL
CONSTRAINT DF_CLPORDER_CLPOrderDate DEFAULT CURRENT_Timestamp ,
Type                     char(10) NOT NULL
CONSTRAINT DF_CLPORDER_Type DEFAULT "0" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_CLPORDER_Status DEFAULT "0"
CONSTRAINT CK_CLPORDER_Status CHECK ( Status LIKE '[0-9]' ),
Notes                    text NULL,
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_CLPORDER_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_CLPORDER_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_CLPORDER_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_CLPORDER_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_CLPORDER_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('CLPORDER') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CLPORDER FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CLPORDER>>>'
GRANT INSERT ON CLPORDER TO nsql
GRANT UPDATE ON CLPORDER TO nsql
GRANT DELETE ON CLPORDER TO nsql
GRANT SELECT ON CLPORDER TO nsql
END
GO

-- =============================================
-- CLPDETAIL 表 (CLP订单明细表)
-- =============================================
CREATE TABLE CLPDETAIL
(CLPOrderKey              char(10) NOT NULL,
CLPLineNumber            char(5) NOT NULL,
CLPDetailSysId           int NULL ,
ExternCLPOrderKey        char(30) NOT NULL
CONSTRAINT DF_CLPDETAIL_XCLPOrderKey DEFAULT "" ,
ExternLineNo             char(10) NOT NULL
CONSTRAINT DF_CLPDETAIL_XLineNo DEFAULT "" ,
Sku                      char(20) NOT NULL
CONSTRAINT DF_CLPDETAIL_Sku DEFAULT "" ,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_CLPDETAIL_StorerKey DEFAULT "" ,
Qty                      int NOT NULL
CONSTRAINT DF_CLPDETAIL_Qty DEFAULT 0 ,
UOM                      char(10) NOT NULL
CONSTRAINT DF_CLPDETAIL_UOM DEFAULT "" ,
PackKey                  char(10) NOT NULL
CONSTRAINT DF_CLPDETAIL_PackKey DEFAULT "STD" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_CLPDETAIL_Status DEFAULT "0"
CONSTRAINT CK_CLPDETAIL_Status CHECK ( Status LIKE '[0-9]' ),
EffectiveDate            datetime     NOT NULL
CONSTRAINT DF_CLPDETAIL_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_CLPDETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_CLPDETAIL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_CLPDETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_CLPDETAIL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('CLPDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CLPDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CLPDETAIL>>>'
GRANT INSERT ON CLPDETAIL TO nsql
GRANT UPDATE ON CLPDETAIL TO nsql
GRANT DELETE ON CLPDETAIL TO nsql
GRANT SELECT ON CLPDETAIL TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- STORERBILLING 表 (存储商计费表)
-- =============================================
CREATE TABLE STORERBILLING
(StorerBillingKey         char(10) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_STORERBILLING_StorerKey DEFAULT "" ,
BillingPeriod            char(10) NOT NULL
CONSTRAINT DF_STORERBILLING_BillingPeriod DEFAULT "" ,
BillingDate              datetime NOT NULL
CONSTRAINT DF_STORERBILLING_BillingDate DEFAULT CURRENT_Timestamp ,
TotalAmount              money NOT NULL
CONSTRAINT DF_STORERBILLING_TotalAmount DEFAULT 0 ,
Status                   char(10) NOT NULL
CONSTRAINT DF_STORERBILLING_Status DEFAULT "0"
CONSTRAINT CK_STORERBILLING_Status CHECK ( Status LIKE '[0-9]' ),
Notes                    text NULL,
AddDate                  datetime NOT NULL
CONSTRAINT DF_STORERBILLING_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_STORERBILLING_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_STORERBILLING_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_STORERBILLING_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('STORERBILLING') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE STORERBILLING FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE STORERBILLING>>>'
GRANT INSERT ON STORERBILLING TO nsql
GRANT UPDATE ON STORERBILLING TO nsql
GRANT DELETE ON STORERBILLING TO nsql
GRANT SELECT ON STORERBILLING TO nsql
END
GO

-- =============================================
-- AccumulatedCharges 表 (累计费用表)
-- =============================================
CREATE TABLE AccumulatedCharges
(AccumulatedChargesKey    char(10) NOT NULL,
StorerKey                char(15) NOT NULL
CONSTRAINT DF_ACCCHARGES_StorerKey DEFAULT "" ,
ChargeType               char(10) NOT NULL
CONSTRAINT DF_ACCCHARGES_ChargeType DEFAULT "" ,
ChargeAmount             money NOT NULL
CONSTRAINT DF_ACCCHARGES_ChargeAmount DEFAULT 0 ,
ChargeDate               datetime NOT NULL
CONSTRAINT DF_ACCCHARGES_ChargeDate DEFAULT CURRENT_Timestamp ,
Description              varchar(255) NULL,
Status                   char(10) NOT NULL
CONSTRAINT DF_ACCCHARGES_Status DEFAULT "0"
CONSTRAINT CK_ACCCHARGES_Status CHECK ( Status LIKE '[0-9]' ),
AddDate                  datetime NOT NULL
CONSTRAINT DF_ACCCHARGES_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_ACCCHARGES_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_ACCCHARGES_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_ACCCHARGES_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('AccumulatedCharges') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE AccumulatedCharges FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE AccumulatedCharges>>>'
GRANT INSERT ON AccumulatedCharges TO nsql
GRANT UPDATE ON AccumulatedCharges TO nsql
GRANT DELETE ON AccumulatedCharges TO nsql
GRANT SELECT ON AccumulatedCharges TO nsql
END
GO

-- =============================================
-- TaskDetail 表 (任务明细表)
-- =============================================
CREATE TABLE TaskDetail
(TaskDetailKey            char(10) NOT NULL,
TaskType                 char(10) NOT NULL
CONSTRAINT DF_TASKDETAIL_TaskType DEFAULT "" ,
AssignedUser             char(18) NOT NULL
CONSTRAINT DF_TASKDETAIL_AssignedUser DEFAULT "" ,
Priority                 char(10) NOT NULL
CONSTRAINT DF_TASKDETAIL_Priority DEFAULT "5" ,
Status                   char(10) NOT NULL
CONSTRAINT DF_TASKDETAIL_Status DEFAULT "0"
CONSTRAINT CK_TASKDETAIL_Status CHECK ( Status LIKE '[0-9]' ),
TaskDescription          varchar(255) NULL,
DueDate                  datetime NULL,
CompletedDate            datetime NULL,
AddDate                  datetime NOT NULL
CONSTRAINT DF_TASKDETAIL_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_TASKDETAIL_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_TASKDETAIL_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_TASKDETAIL_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('TaskDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TaskDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TaskDetail>>>'
GRANT INSERT ON TaskDetail TO nsql
GRANT UPDATE ON TaskDetail TO nsql
GRANT DELETE ON TaskDetail TO nsql
GRANT SELECT ON TaskDetail TO nsql
END
GO

-- =============================================
-- TaskManagerUser 表 (任务管理用户表)
-- =============================================
CREATE TABLE TaskManagerUser
(UserKey                  char(18) NOT NULL,
UserName                 char(30) NOT NULL
CONSTRAINT DF_TASKMGRUSER_UserName DEFAULT "" ,
UserRole                 char(10) NOT NULL
CONSTRAINT DF_TASKMGRUSER_UserRole DEFAULT "" ,
Active                   char(1) NOT NULL
CONSTRAINT DF_TASKMGRUSER_Active DEFAULT "Y" ,
AddDate                  datetime NOT NULL
CONSTRAINT DF_TASKMGRUSER_AddDate DEFAULT CURRENT_Timestamp ,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_TASKMGRUSER_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_TASKMGRUSER_EditDate DEFAULT CURRENT_Timestamp ,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_TASKMGRUSER_EditWho DEFAULT USER ,
TrafficCop               char(1)  NULL,
ArchiveCop               char(1)  NULL
)
GO

IF OBJECT_ID('TaskManagerUser') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TaskManagerUser FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TaskManagerUser>>>'
GRANT INSERT ON TaskManagerUser TO nsql
GRANT UPDATE ON TaskManagerUser TO nsql
GRANT DELETE ON TaskManagerUser TO nsql
GRANT SELECT ON TaskManagerUser TO nsql
END
GO

-- 注意：此脚本包含扩展表结构
-- 更多表结构将在后续部分添加
