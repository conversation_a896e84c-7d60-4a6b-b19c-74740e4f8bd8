-- =============================================
-- 创建剩余遗漏表结构脚本 (第九部分)
-- 功能：创建仓库管理系统中剩余遗漏的重要表结构
-- 用途：日历管理、计费明细、轮询任务、服务管理等
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 日历管理表
-- =============================================

-- CALENDAR 表 (日历表)
CREATE TABLE CALENDAR
( CalendarGroup          char(10) NOT NULL,
CalendarDate           datetime NOT NULL
CONSTRAINT DF_CALENDAR_CalendarDate DEFAULT CURRENT_Timestamp,
WorkingDay             char(1) NOT NULL
CONSTRAINT DF_CALENDAR_WorkingDay DEFAULT "Y",
AddDate                datetime NOT NULL
CONSTRAINT DF_CALENDAR_AddDate DEFAULT CURRENT_Timestamp,
AddWho                 char(18) NOT NULL
CONSTRAINT DF_CALENDAR_AddWho DEFAULT USER ,
EditDate               datetime NOT NULL
CONSTRAINT DF_CALENDAR_EditDate DEFAULT CURRENT_Timestamp,
EditWho                char(18) NOT NULL
CONSTRAINT DF_CALENDAR_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('CALENDAR') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CALENDAR FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CALENDAR>>>'
GRANT INSERT ON CALENDAR TO nsql
GRANT UPDATE ON CALENDAR TO nsql
GRANT DELETE ON CALENDAR TO nsql
GRANT SELECT ON CALENDAR TO nsql
END
GO

-- CALENDARDETAIL 表 (日历明细表)
CREATE TABLE CALENDARDETAIL
( CalendarGroup           char(10) NOT NULL,
CalendarDate            datetime NOT NULL,
StartTime               datetime NOT NULL
CONSTRAINT DF_CALENDARDETAIL_StartTime DEFAULT CURRENT_Timestamp,
EndTime                 datetime NOT NULL
CONSTRAINT DF_CALENDARDETAIL_EndTime DEFAULT CURRENT_Timestamp,
AddDate                 datetime NOT NULL
CONSTRAINT DF_CALENDARDETAIL_AddDate DEFAULT CURRENT_Timestamp,
AddWho                  char(18) NOT NULL
CONSTRAINT DF_CALENDARDETAIL_AddWho DEFAULT USER ,
EditDate                datetime NOT NULL
CONSTRAINT DF_CALENDARDETAIL_EditDate DEFAULT CURRENT_Timestamp,
EditWho                 char(18) NOT NULL
CONSTRAINT DF_CALENDARDETAIL_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('CALENDARDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE CALENDARDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE CALENDARDETAIL>>>'
GRANT INSERT ON CALENDARDETAIL TO nsql
GRANT UPDATE ON CALENDARDETAIL TO nsql
GRANT DELETE ON CALENDARDETAIL TO nsql
GRANT SELECT ON CALENDARDETAIL TO nsql
END
GO

-- =============================================
-- 计费明细表
-- =============================================

-- BILL_STOCKMOVEMENT 表 (库存移动计费表)
CREATE TABLE BILL_STOCKMOVEMENT
(StorerKey       char(15) NOT NULL,
BillThruDate    datetime NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_BillThruDate DEFAULT CURRENT_Timestamp,
QtyReceived     int NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_QtyReceived DEFAULT 0,
QtyShipped      int NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_QtyShipped DEFAULT 0,
AddDate         datetime NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_AddDate DEFAULT CURRENT_Timestamp,
AddWho          char(18) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_AddWho DEFAULT USER ,
EditDate        datetime NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_EditDate DEFAULT CURRENT_Timestamp,
EditWho         char(18) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('BILL_STOCKMOVEMENT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE BILL_STOCKMOVEMENT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE BILL_STOCKMOVEMENT>>>'
GRANT INSERT ON BILL_STOCKMOVEMENT TO nsql
GRANT UPDATE ON BILL_STOCKMOVEMENT TO nsql
GRANT DELETE ON BILL_STOCKMOVEMENT TO nsql
GRANT SELECT ON BILL_STOCKMOVEMENT TO nsql
END
GO

-- BILL_STOCKMOVEMENT_DETAIL 表 (库存移动计费明细表)
CREATE TABLE BILL_STOCKMOVEMENT_DETAIL
(StorerKey       char(15) NOT NULL,
BillThruDate    datetime NOT NULL,
Sku             char(20) NOT NULL,
QtyReceived     int NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_QtyReceived DEFAULT 0,
QtyShipped      int NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_QtyShipped DEFAULT 0,
AddDate         datetime NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_AddDate DEFAULT CURRENT_Timestamp,
AddWho          char(18) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_AddWho DEFAULT USER ,
EditDate        datetime NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_EditDate DEFAULT CURRENT_Timestamp,
EditWho         char(18) NOT NULL
CONSTRAINT DF_BILL_STOCKMOVEMENT_DETAIL_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('BILL_STOCKMOVEMENT_DETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE BILL_STOCKMOVEMENT_DETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE BILL_STOCKMOVEMENT_DETAIL>>>'
GRANT INSERT ON BILL_STOCKMOVEMENT_DETAIL TO nsql
GRANT UPDATE ON BILL_STOCKMOVEMENT_DETAIL TO nsql
GRANT DELETE ON BILL_STOCKMOVEMENT_DETAIL TO nsql
GRANT SELECT ON BILL_STOCKMOVEMENT_DETAIL TO nsql
END
GO

-- =============================================
-- 轮询任务表
-- =============================================

-- POLL_ALLOCATE 表 (分配轮询表)
CREATE TABLE POLL_ALLOCATE
(orderkey                 char(10) NOT NULL,
orderlineNumber          char(5) NOT NULL,
storerkey                char(15) NOT NULL,
sku                      char(20) NOT NULL,
requestedqty             int NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_requestedqty DEFAULT 0,
allocatedqty             int NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_allocatedqty DEFAULT 0,
status                   char(10) NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_status DEFAULT "0",
AddDate                  datetime NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_POLL_ALLOCATE_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('POLL_ALLOCATE') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE POLL_ALLOCATE FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE POLL_ALLOCATE>>>'
GRANT INSERT ON POLL_ALLOCATE TO nsql
GRANT UPDATE ON POLL_ALLOCATE TO nsql
GRANT DELETE ON POLL_ALLOCATE TO nsql
GRANT SELECT ON POLL_ALLOCATE TO nsql
END
GO

-- POLL_PICK 表 (拣选轮询表)
CREATE TABLE POLL_PICK
(Caseid                   char(10) NOT NULL,
orderkey                 char(10) NOT NULL,
orderlineNumber          char(5) NOT NULL,
storerkey                char(15) NOT NULL,
sku                      char(20) NOT NULL,
requestedqty             int NOT NULL
CONSTRAINT DF_POLL_PICK_requestedqty DEFAULT 0,
pickedqty                int NOT NULL
CONSTRAINT DF_POLL_PICK_pickedqty DEFAULT 0,
status                   char(10) NOT NULL
CONSTRAINT DF_POLL_PICK_status DEFAULT "0",
AddDate                  datetime NOT NULL
CONSTRAINT DF_POLL_PICK_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_POLL_PICK_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_POLL_PICK_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_POLL_PICK_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('POLL_PICK') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE POLL_PICK FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE POLL_PICK>>>'
GRANT INSERT ON POLL_PICK TO nsql
GRANT UPDATE ON POLL_PICK TO nsql
GRANT DELETE ON POLL_PICK TO nsql
GRANT SELECT ON POLL_PICK TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- POLL_SHIP 表 (发货轮询表)
CREATE TABLE POLL_SHIP
(Caseid                   char(10) NOT NULL,
orderkey                 char(10) NOT NULL,
storerkey                char(15) NOT NULL,
status                   char(10) NOT NULL
CONSTRAINT DF_POLL_SHIP_status DEFAULT "0",
shippeddate              datetime NULL,
AddDate                  datetime NOT NULL
CONSTRAINT DF_POLL_SHIP_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_POLL_SHIP_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_POLL_SHIP_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_POLL_SHIP_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('POLL_SHIP') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE POLL_SHIP FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE POLL_SHIP>>>'
GRANT INSERT ON POLL_SHIP TO nsql
GRANT UPDATE ON POLL_SHIP TO nsql
GRANT DELETE ON POLL_SHIP TO nsql
GRANT SELECT ON POLL_SHIP TO nsql
END
GO

-- POLL_UPDATE 表 (更新轮询表)
CREATE TABLE POLL_UPDATE
(PollUpdateKey            int IDENTITY ,
tablename                char(30) NOT NULL,
keyfield1                char(30) NOT NULL,
keyvalue1                char(30) NOT NULL,
keyfield2                char(30) NOT NULL,
keyvalue2                char(30) NOT NULL,
keyfield3                char(30) NOT NULL,
keyvalue3                char(30) NOT NULL,
status                   char(10) NOT NULL
CONSTRAINT DF_POLL_UPDATE_status DEFAULT "0",
AddDate                  datetime NOT NULL
CONSTRAINT DF_POLL_UPDATE_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_POLL_UPDATE_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_POLL_UPDATE_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_POLL_UPDATE_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('POLL_UPDATE') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE POLL_UPDATE FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE POLL_UPDATE>>>'
GRANT INSERT ON POLL_UPDATE TO nsql
GRANT UPDATE ON POLL_UPDATE TO nsql
GRANT DELETE ON POLL_UPDATE TO nsql
GRANT SELECT ON POLL_UPDATE TO nsql
END
GO

-- POLL_PRINT 表 (打印轮询表)
CREATE TABLE POLL_PRINT
(printtype                char(10) NOT NULL,
keyfield1                char(30) NOT NULL,
keyvalue1                char(30) NOT NULL,
keyfield2                char(30) NOT NULL,
keyvalue2                char(30) NOT NULL,
keyfield3                char(30) NOT NULL,
keyvalue3                char(30) NOT NULL,
keyfield4                char(30) NOT NULL,
keyvalue4                char(30) NOT NULL,
keyfield5                char(30) NOT NULL,
keyvalue5                char(30) NOT NULL,
status                   char(10) NOT NULL
CONSTRAINT DF_POLL_PRINT_status DEFAULT "0",
AddDate                  datetime NOT NULL
CONSTRAINT DF_POLL_PRINT_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_POLL_PRINT_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_POLL_PRINT_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_POLL_PRINT_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('POLL_PRINT') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE POLL_PRINT FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE POLL_PRINT>>>'
GRANT INSERT ON POLL_PRINT TO nsql
GRANT UPDATE ON POLL_PRINT TO nsql
GRANT DELETE ON POLL_PRINT TO nsql
GRANT SELECT ON POLL_PRINT TO nsql
END
GO

-- =============================================
-- 服务管理表
-- =============================================

-- Services 表 (服务表)
CREATE TABLE Services
(Servicekey              char(10)  NOT NULL,
SupportFlag             char(1) NOT NULL
CONSTRAINT DF_Services_SupportFlag   DEFAULT "A"
CONSTRAINT CK_Services_SupportFlag   CHECK (SupportFLag in ("A", "I", "D")),
Descrip                 char(30) NOT NULL
CONSTRAINT DF_Services_Descrip  DEFAULT "",
AddDate                 datetime NOT NULL
CONSTRAINT DF_Services_AddDate DEFAULT CURRENT_Timestamp,
AddWho                  char(18) NOT NULL
CONSTRAINT DF_Services_AddWho DEFAULT USER ,
EditDate                datetime NOT NULL
CONSTRAINT DF_Services_EditDate DEFAULT CURRENT_Timestamp,
EditWho                 char(18) NOT NULL
CONSTRAINT DF_Services_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('Services') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE Services FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE Services>>>'
GRANT INSERT ON Services TO nsql
GRANT UPDATE ON Services TO nsql
GRANT DELETE ON Services TO nsql
GRANT SELECT ON Services TO nsql
END
GO

-- Accessorial 表 (附加服务表)
CREATE TABLE Accessorial
(Accessorialkey              char(10)  NOT NULL,
SupportFlag                 char(1) NOT NULL
CONSTRAINT DF_Accessorial_SupportFlag   DEFAULT "A"
CONSTRAINT CK_Accessorial_SupportFlag   CHECK (SupportFLag in ("A", "I", "D")),
Descrip                     char(30) NOT NULL
CONSTRAINT DF_Accessorial_Descrip  DEFAULT "",
TariffKey                   char(10) NOT NULL
CONSTRAINT DF_Accessorial_TariffKey DEFAULT "",
AddDate                     datetime NOT NULL
CONSTRAINT DF_Accessorial_AddDate DEFAULT CURRENT_Timestamp,
AddWho                      char(18) NOT NULL
CONSTRAINT DF_Accessorial_AddWho DEFAULT USER ,
EditDate                    datetime NOT NULL
CONSTRAINT DF_Accessorial_EditDate DEFAULT CURRENT_Timestamp,
EditWho                     char(18) NOT NULL
CONSTRAINT DF_Accessorial_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('Accessorial') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE Accessorial FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE Accessorial>>>'
GRANT INSERT ON Accessorial TO nsql
GRANT UPDATE ON Accessorial TO nsql
GRANT DELETE ON Accessorial TO nsql
GRANT SELECT ON Accessorial TO nsql
END
GO

-- 注意：此脚本包含剩余遗漏的重要表结构
-- 完整的表结构请参考原始 NEPISQL.sql 文件
