-- =============================================
-- Oracle版本 - 创建剩余表结构脚本 (第十三部分)
-- 功能：创建仓库管理系统中剩余的重要表结构 (Oracle版本)
-- 用途：策略管理、设备配置、任务管理、区域管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 策略管理表
-- =============================================

-- Strategy 表 (策略表)
CREATE TABLE Strategy
(StrategyKey               CHAR(10) NOT NULL
CONSTRAINT DF_STRATEGY_Key DEFAULT ' ',
Descr                     CHAR(60) NOT NULL
CONSTRAINT DF_STRATEGY_Descr DEFAULT ' ',
PreAllocateStrategyKey   CHAR(10) NOT NULL
CONSTRAINT DF_STRATEGY_PreAllocateStrategyKey DEFAULT ' ',
AllocateStrategyKey      CHAR(10) NOT NULL
CONSTRAINT DF_STRATEGY_AllocateStrategyKey DEFAULT ' ',
PutawayStrategyKey       CHAR(10) NOT NULL
CONSTRAINT DF_STRATEGY_PutawayStrategyKey DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_STRATEGY_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_STRATEGY_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_STRATEGY_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_STRATEGY_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE Strategy ADD CONSTRAINT PK_Strategy PRIMARY KEY (StrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Strategy TO nsql;

PROMPT '>>> 已创建表 Strategy (策略表)';

-- PreAllocateStrategy 表 (预分配策略表)
CREATE TABLE PreAllocateStrategy
(PreAllocateStrategyKey   CHAR(10) NOT NULL
CONSTRAINT DF_PreAS_Key DEFAULT ' ',
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_PreAS_Descr DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_PreAS_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PreAS_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PreAS_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PreAS_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PreAllocateStrategy ADD CONSTRAINT PK_PreAllocateStrategy PRIMARY KEY (PreAllocateStrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PreAllocateStrategy TO nsql;

PROMPT '>>> 已创建表 PreAllocateStrategy (预分配策略表)';

-- PreAllocateStrategyDetail 表 (预分配策略明细表)
CREATE TABLE PreAllocateStrategyDetail
(PreAllocateStrategyKey         CHAR(10) NOT NULL
CONSTRAINT DF_PreAsd_Key DEFAULT ' ',
PreAllocateStrategyLineNumber  CHAR(5) NOT NULL
CONSTRAINT DF_PreAsd_LineNumber DEFAULT ' ',
FromLoc                        CHAR(10) NOT NULL
CONSTRAINT DF_PreAsd_FromLoc DEFAULT ' ',
ToLoc                          CHAR(10) NOT NULL
CONSTRAINT DF_PreAsd_ToLoc DEFAULT ' ',
AddDate                        DATE NOT NULL
CONSTRAINT DF_PreAsd_AddDate DEFAULT SYSDATE,
AddWho                         CHAR(18) NOT NULL
CONSTRAINT DF_PreAsd_AddWho DEFAULT USER,
EditDate                       DATE NOT NULL
CONSTRAINT DF_PreAsd_EditDate DEFAULT SYSDATE,
EditWho                        CHAR(18) NOT NULL
CONSTRAINT DF_PreAsd_EditWho DEFAULT USER,
TrafficCop                     CHAR(1) NULL,
ArchiveCop                     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PreAllocateStrategyDetail ADD CONSTRAINT PK_PreAllocateStrategyDetail 
PRIMARY KEY (PreAllocateStrategyKey, PreAllocateStrategyLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PreAllocateStrategyDetail TO nsql;

PROMPT '>>> 已创建表 PreAllocateStrategyDetail (预分配策略明细表)';

-- AllocateStrategy 表 (分配策略表)
CREATE TABLE AllocateStrategy
(AllocateStrategyKey      CHAR(10) NOT NULL
CONSTRAINT DF_AS_Key DEFAULT ' ',
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_AS_Descr DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_AS_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_AS_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_AS_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_AS_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE AllocateStrategy ADD CONSTRAINT PK_AllocateStrategy PRIMARY KEY (AllocateStrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON AllocateStrategy TO nsql;

PROMPT '>>> 已创建表 AllocateStrategy (分配策略表)';

-- AllocateStrategyDetail 表 (分配策略明细表)
CREATE TABLE AllocateStrategyDetail
(AllocateStrategyKey            CHAR(10) NOT NULL
CONSTRAINT DF_Asd_Key DEFAULT ' ',
AllocateStrategyLineNumber     CHAR(5) NOT NULL
CONSTRAINT DF_Asd_LineNumber DEFAULT ' ',
RotationRule                   CHAR(10) NOT NULL
CONSTRAINT DF_Asd_RotationRule DEFAULT ' ',
AllocationRule                 CHAR(10) NOT NULL
CONSTRAINT DF_Asd_AllocationRule DEFAULT ' ',
AddDate                        DATE NOT NULL
CONSTRAINT DF_Asd_AddDate DEFAULT SYSDATE,
AddWho                         CHAR(18) NOT NULL
CONSTRAINT DF_Asd_AddWho DEFAULT USER,
EditDate                       DATE NOT NULL
CONSTRAINT DF_Asd_EditDate DEFAULT SYSDATE,
EditWho                        CHAR(18) NOT NULL
CONSTRAINT DF_Asd_EditWho DEFAULT USER,
TrafficCop                     CHAR(1) NULL,
ArchiveCop                     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE AllocateStrategyDetail ADD CONSTRAINT PK_AllocateStrategyDetail 
PRIMARY KEY (AllocateStrategyKey, AllocateStrategyLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON AllocateStrategyDetail TO nsql;

PROMPT '>>> 已创建表 AllocateStrategyDetail (分配策略明细表)';

-- PutawayStrategy 表 (上架策略表)
CREATE TABLE PutawayStrategy
(PutawayStrategyKey       CHAR(10) NOT NULL,
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_PAS_Descr DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_PAS_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PAS_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PAS_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PAS_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PutawayStrategy ADD CONSTRAINT PK_PutawayStrategy PRIMARY KEY (PutawayStrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PutawayStrategy TO nsql;

PROMPT '>>> 已创建表 PutawayStrategy (上架策略表)';

-- PutawayStrategyDetail 表 (上架策略明细表)
CREATE TABLE PutawayStrategyDetail
(PutawayStrategyKey             CHAR(10) NOT NULL,
PutawayStrategyLineNumber      CHAR(5) NOT NULL,
PAType                         CHAR(5) NOT NULL
CONSTRAINT DF_PSD_PAType DEFAULT ' ',
TOLOC                          CHAR(10) NOT NULL
CONSTRAINT DF_PSD_TOLOC DEFAULT ' ',
LOCTYPE                        CHAR(10) NOT NULL
CONSTRAINT DF_PSD_LOCTYPE DEFAULT ' ',
PUTAWAYZONE                    CHAR(10) NOT NULL
CONSTRAINT DF_PSD_PUTAWAYZONE DEFAULT ' ',
MAXQTY                         NUMBER(10) NOT NULL
CONSTRAINT DF_PSD_MAXQTY DEFAULT 0,
MAXWEIGHT                      NUMBER(12,6) NOT NULL
CONSTRAINT DF_PSD_MAXWEIGHT DEFAULT 0,
MAXCUBE                        NUMBER(12,6) NOT NULL
CONSTRAINT DF_PSD_MAXCUBE DEFAULT 0,
AddDate                        DATE NOT NULL
CONSTRAINT DF_PSD_AddDate DEFAULT SYSDATE,
AddWho                         CHAR(18) NOT NULL
CONSTRAINT DF_PSD_AddWho DEFAULT USER,
EditDate                       DATE NOT NULL
CONSTRAINT DF_PSD_EditDate DEFAULT SYSDATE,
EditWho                        CHAR(18) NOT NULL
CONSTRAINT DF_PSD_EditWho DEFAULT USER,
TrafficCop                     CHAR(1) NULL,
ArchiveCop                     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PutawayStrategyDetail ADD CONSTRAINT PK_PutawayStrategyDetail 
PRIMARY KEY (PutawayStrategyKey, PutawayStrategyLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PutawayStrategyDetail TO nsql;

PROMPT '>>> 已创建表 PutawayStrategyDetail (上架策略明细表)';

-- =============================================
-- 任务管理表
-- =============================================

-- TaskDetail 表 (任务明细表)
CREATE TABLE TaskDetail
(TaskDetailKey CHAR(10) NOT NULL
CONSTRAINT DF_TD_Key DEFAULT ' ',
TaskType      CHAR(10) NOT NULL
CONSTRAINT DF_TD_TaskType DEFAULT ' ',
FromLoc       CHAR(10) NOT NULL
CONSTRAINT DF_TD_FromLoc DEFAULT ' ',
ToLoc         CHAR(10) NOT NULL
CONSTRAINT DF_TD_ToLoc DEFAULT ' ',
StorerKey     CHAR(15) NOT NULL
CONSTRAINT DF_TD_StorerKey DEFAULT ' ',
Sku           CHAR(20) NOT NULL
CONSTRAINT DF_TD_Sku DEFAULT ' ',
Lot           CHAR(10) NOT NULL
CONSTRAINT DF_TD_Lot DEFAULT ' ',
ID            CHAR(18) NOT NULL
CONSTRAINT DF_TD_ID DEFAULT ' ',
Qty           NUMBER(10) NOT NULL
CONSTRAINT DF_TD_Qty DEFAULT 0,
Priority      CHAR(5) NOT NULL
CONSTRAINT DF_TD_Priority DEFAULT '5',
Status        CHAR(10) NOT NULL
CONSTRAINT DF_TD_Status DEFAULT '0',
AssignedUser  CHAR(18) NULL,
CompletedUser CHAR(18) NULL,
CompletedDate DATE NULL,
AddDate       DATE NOT NULL
CONSTRAINT DF_TD_AddDate DEFAULT SYSDATE,
AddWho        CHAR(18) NOT NULL
CONSTRAINT DF_TD_AddWho DEFAULT USER,
EditDate      DATE NOT NULL
CONSTRAINT DF_TD_EditDate DEFAULT SYSDATE,
EditWho       CHAR(18) NOT NULL
CONSTRAINT DF_TD_EditWho DEFAULT USER,
TrafficCop    CHAR(1) NULL,
ArchiveCop    CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TaskDetail ADD CONSTRAINT PK_TaskDetail PRIMARY KEY (TaskDetailKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaskDetail TO nsql;

PROMPT '>>> 已创建表 TaskDetail (任务明细表)';

-- TaskManagerUser 表 (任务管理用户表)
CREATE TABLE TaskManagerUser
(UserKey                  CHAR(18) NOT NULL
CONSTRAINT DF_TMU_UserKey DEFAULT ' ',
PriorityTaskType         CHAR(10) NOT NULL
CONSTRAINT DF_TMU_PriorityTaskType DEFAULT ' ',
StrategyKey              CHAR(10) NOT NULL
CONSTRAINT DF_TMU_StrategyKey DEFAULT ' ',
TTMStrategyKey           CHAR(10) NOT NULL
CONSTRAINT DF_TMU_TTMStrategyKey DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_TMU_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TMU_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TMU_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TMU_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TaskManagerUser ADD CONSTRAINT PK_TaskManagerUser PRIMARY KEY (UserKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaskManagerUser TO nsql;

PROMPT '>>> 已创建表 TaskManagerUser (任务管理用户表)';

-- TaskManagerUserDetail 表 (任务管理用户明细表)
CREATE TABLE TaskManagerUserDetail
(UserKey                  CHAR(18) NOT NULL
CONSTRAINT DF_TMUD_UserKey DEFAULT ' ',
UserLineNumber           CHAR(5) NOT NULL
CONSTRAINT DF_TMUD_UserLineNumber DEFAULT ' ',
TaskType                 CHAR(10) NOT NULL
CONSTRAINT DF_TMUD_TaskType DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_TMUD_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TMUD_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TMUD_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TMUD_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TaskManagerUserDetail ADD CONSTRAINT PK_TaskManagerUserDetail
PRIMARY KEY (UserKey, UserLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaskManagerUserDetail TO nsql;

PROMPT '>>> 已创建表 TaskManagerUserDetail (任务管理用户明细表)';

-- TTMStrategy 表 (TTM策略表)
CREATE TABLE TTMStrategy
(TTMStrategyKey           CHAR(10) NOT NULL
CONSTRAINT DF_TTMS_Key DEFAULT ' ',
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_TTMS_Descr DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_TTMS_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TTMS_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TTMS_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TTMS_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TTMStrategy ADD CONSTRAINT PK_TTMStrategy PRIMARY KEY (TTMStrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TTMStrategy TO nsql;

PROMPT '>>> 已创建表 TTMStrategy (TTM策略表)';

-- TTMStrategyDetail 表 (TTM策略明细表)
CREATE TABLE TTMStrategyDetail
(TTMStrategyKey                 CHAR(10) NOT NULL
CONSTRAINT DF_TTMSD_Key DEFAULT ' ',
TTMStrategyLineNumber          CHAR(5) NOT NULL
CONSTRAINT DF_TTMSD_LineNumber DEFAULT ' ',
TaskType                       CHAR(10) NOT NULL
CONSTRAINT DF_TTMSD_TaskType DEFAULT ' ',
Priority                       CHAR(5) NOT NULL
CONSTRAINT DF_TTMSD_Priority DEFAULT '5',
AddDate                        DATE NOT NULL
CONSTRAINT DF_TTMSD_AddDate DEFAULT SYSDATE,
AddWho                         CHAR(18) NOT NULL
CONSTRAINT DF_TTMSD_AddWho DEFAULT USER,
EditDate                       DATE NOT NULL
CONSTRAINT DF_TTMSD_EditDate DEFAULT SYSDATE,
EditWho                        CHAR(18) NOT NULL
CONSTRAINT DF_TTMSD_EditWho DEFAULT USER,
TrafficCop                     CHAR(1) NULL,
ArchiveCop                     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TTMStrategyDetail ADD CONSTRAINT PK_TTMStrategyDetail
PRIMARY KEY (TTMStrategyKey, TTMStrategyLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TTMStrategyDetail TO nsql;

PROMPT '>>> 已创建表 TTMStrategyDetail (TTM策略明细表)';

-- 提交事务
COMMIT;

PROMPT '>>> 剩余表结构脚本第一部分执行完成';
PROMPT '>>> 已创建策略管理和任务管理表 (12张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
