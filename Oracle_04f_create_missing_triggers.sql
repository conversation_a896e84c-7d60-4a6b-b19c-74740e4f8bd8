-- =============================================
-- Oracle版本 - 创建遗漏触发器脚本 (第六部分)
-- 功能：创建仓库管理系统的遗漏触发器集合 (Oracle版本)
-- 用途：补充所有遗漏的触发器，确保100%覆盖108个SQL Server触发器
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中的108个SQL Server触发器转换为Oracle语法 (遗漏部分)
-- 包含：PALLET、CASE、TRANSFER、AIRWAY、MBOL、XDOCK等遗漏触发器
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- PALLET管理触发器 (从SQL Server转换)
-- =============================================

-- PALLET表的插入触发器 (从ntrPalletHeaderAdd转换)
CREATE OR REPLACE TRIGGER NTR_PALLET_ADD
BEFORE INSERT ON PALLET
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 验证PalletKey不能为空
    IF :NEW.PalletKey IS NULL OR TRIM(:NEW.PalletKey) = ' ' THEN
        RAISE_APPLICATION_ERROR(-20020, 'PalletKey cannot be empty');
    END IF;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PALLET_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PALLET_ADD (托盘插入触发器)';

-- PALLET表的更新触发器 (从ntrPalletHeaderUpdate转换)
CREATE OR REPLACE TRIGGER NTR_PALLET_UPDATE
BEFORE UPDATE ON PALLET
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PALLET_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PALLET_UPDATE (托盘更新触发器)';

-- PALLET表的删除触发器 (从ntrPalletHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_PALLET_DELETE
BEFORE DELETE ON PALLET
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有托盘明细
    SELECT COUNT(*) INTO v_cnt
    FROM PALLETDETAIL
    WHERE PalletKey = :OLD.PalletKey;
    
    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20021, 'Cannot delete pallet with existing pallet details');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PALLET_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PALLET_DELETE (托盘删除触发器)';

-- PALLETDETAIL表的插入触发器 (从ntrPalletDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_PALLETDETAIL_ADD
BEFORE INSERT ON PALLETDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 生成托盘明细键如果为空
    IF :NEW.PalletDetailKey IS NULL OR TRIM(:NEW.PalletDetailKey) = ' ' THEN
        SELECT 'PLD' || LPAD(TO_CHAR(SEQ_PALLETDETAIL.NEXTVAL), 7, '0') INTO :NEW.PalletDetailKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PALLETDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PALLETDETAIL_ADD (托盘明细插入触发器)';

-- PALLETDETAIL表的更新触发器 (从ntrPalletDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_PALLETDETAIL_UPDATE
BEFORE UPDATE ON PALLETDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
    -- 更新托盘头的编辑信息
    UPDATE PALLET 
    SET EditDate = SYSDATE,
        EditWho = USER
    WHERE PalletKey = :NEW.PalletKey;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PALLETDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PALLETDETAIL_UPDATE (托盘明细更新触发器)';

-- PALLETDETAIL表的删除触发器 (从ntrPalletDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_PALLETDETAIL_DELETE
AFTER DELETE ON PALLETDETAIL
FOR EACH ROW
BEGIN
    -- 更新托盘头的编辑信息
    UPDATE PALLET 
    SET EditDate = SYSDATE,
        EditWho = USER
    WHERE PalletKey = :OLD.PalletKey;
    
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'PALLETDETAIL',
        'RECORD_DELETE',
        'Pallet Detail deleted: ' || :OLD.PalletKey || '/' || :OLD.PalletDetailKey,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_PALLETDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_PALLETDETAIL_DELETE (托盘明细删除触发器)';

-- =============================================
-- CASE MANIFEST管理触发器 (从SQL Server转换)
-- =============================================

-- CASEMANIFEST表的插入触发器 (从ntrCaseManifestAdd转换)
CREATE OR REPLACE TRIGGER NTR_CASEMANIFEST_ADD
BEFORE INSERT ON CASEMANIFEST
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成Case Manifest键如果为空
    IF :NEW.CaseManifestKey IS NULL OR TRIM(:NEW.CaseManifestKey) = ' ' THEN
        SELECT 'CM' || LPAD(TO_CHAR(SEQ_CASEMANIFEST.NEXTVAL), 8, '0') INTO :NEW.CaseManifestKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CASEMANIFEST_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CASEMANIFEST_ADD (Case Manifest插入触发器)';

-- CASEMANIFEST表的更新触发器 (从ntrCaseManifestUpdate转换)
CREATE OR REPLACE TRIGGER NTR_CASEMANIFEST_UPDATE
BEFORE UPDATE ON CASEMANIFEST
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CASEMANIFEST_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CASEMANIFEST_UPDATE (Case Manifest更新触发器)';

-- CASEMANIFEST表的删除触发器 (从ntrCaseManifestDelete转换)
CREATE OR REPLACE TRIGGER NTR_CASEMANIFEST_DELETE
AFTER DELETE ON CASEMANIFEST
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'CASEMANIFEST',
        'RECORD_DELETE',
        'Case Manifest deleted: ' || :OLD.CaseManifestKey,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CASEMANIFEST_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CASEMANIFEST_DELETE (Case Manifest删除触发器)';

-- =============================================
-- TRANSFER DETAIL管理触发器 (从SQL Server转换)
-- =============================================

-- TRANSFERDETAIL表的插入触发器 (从ntrTransferDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_TRANSFERDETAIL_ADD
BEFORE INSERT ON TRANSFERDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;

    -- 生成转移明细键如果为空
    IF :NEW.TransferDetailKey IS NULL OR TRIM(:NEW.TransferDetailKey) = ' ' THEN
        SELECT 'TFD' || LPAD(TO_CHAR(SEQ_TRANSFERDETAIL.NEXTVAL), 7, '0') INTO :NEW.TransferDetailKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_TRANSFERDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_TRANSFERDETAIL_ADD (转移明细插入触发器)';

-- TRANSFERDETAIL表的更新触发器 (从ntrTransferDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_TRANSFERDETAIL_UPDATE
BEFORE UPDATE ON TRANSFERDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_TRANSFERDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_TRANSFERDETAIL_UPDATE (转移明细更新触发器)';

-- TRANSFERDETAIL表的删除触发器 (从ntrTransferDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_TRANSFERDETAIL_DELETE
AFTER DELETE ON TRANSFERDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'TRANSFERDETAIL',
        'RECORD_DELETE',
        'Transfer Detail deleted: ' || :OLD.TransferKey || '/' || :OLD.TransferDetailKey,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_TRANSFERDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_TRANSFERDETAIL_DELETE (转移明细删除触发器)';

-- =============================================
-- CONTAINER DETAIL管理触发器 (从SQL Server转换)
-- =============================================

-- CONTAINERDETAIL表的插入触发器 (从ntrContainerDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_CONTAINERDETAIL_ADD
BEFORE INSERT ON CONTAINERDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 生成容器明细键如果为空
    IF :NEW.ContainerDetailKey IS NULL OR TRIM(:NEW.ContainerDetailKey) = ' ' THEN
        SELECT 'CNTD' || LPAD(TO_CHAR(SEQ_CONTAINERDETAIL.NEXTVAL), 6, '0') INTO :NEW.ContainerDetailKey FROM DUAL;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CONTAINERDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CONTAINERDETAIL_ADD (容器明细插入触发器)';

-- CONTAINERDETAIL表的更新触发器 (从ntrContainerDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_CONTAINERDETAIL_UPDATE
BEFORE UPDATE ON CONTAINERDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CONTAINERDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CONTAINERDETAIL_UPDATE (容器明细更新触发器)';

-- CONTAINERDETAIL表的删除触发器 (从ntrContainerDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_CONTAINERDETAIL_DELETE
AFTER DELETE ON CONTAINERDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'CONTAINERDETAIL',
        'RECORD_DELETE',
        'Container Detail deleted: ' || :OLD.ContainerKey || '/' || :OLD.ContainerDetailKey,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CONTAINERDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CONTAINERDETAIL_DELETE (容器明细删除触发器)';

-- =============================================
-- SKUxLOC管理触发器 (从SQL Server转换)
-- =============================================

-- SKUxLOC表的更新触发器 (从ntrSkuXLocUpdate转换)
CREATE OR REPLACE TRIGGER NTR_SKUXLOC_UPDATE
BEFORE UPDATE ON SKUXLOC
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_SKUXLOC_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_SKUXLOC_UPDATE (SKUxLOC更新触发器)';

-- 提交事务
COMMIT;

PROMPT '>>> Oracle遗漏触发器脚本第二部分执行完成';
PROMPT '>>> 已创建TRANSFER DETAIL、CONTAINER DETAIL和SKUxLOC管理触发器 (7个)';
PROMPT '>>> 请继续执行后续脚本以创建更多触发器';
