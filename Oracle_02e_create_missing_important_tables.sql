-- =============================================
-- Oracle版本 - 创建遗漏重要表结构脚本 (第五部分)
-- 功能：创建仓库管理系统中遗漏的重要表结构 (Oracle版本)
-- 用途：订单选择、事务头、装箱操作、转移管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 订单选择和波次管理表
-- =============================================

-- OrderSelection 表 (订单选择表)
CREATE TABLE OrderSelection
(OrderSelectionKey        CHAR(10) NOT NULL,
DefaultFlag              CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_DefaultFlag DEFAULT '0',
OrderDateStart           DATE NOT NULL
CONSTRAINT DF_OrderSelection_OrderDateStart DEFAULT TO_DATE('1900-01-01', 'YYYY-MM-DD'),
OrderDateEnd             DATE NOT NULL
CONSTRAINT DF_OrderSelection_OrderDateEnd DEFAULT TO_DATE('2100-01-01', 'YYYY-MM-DD'),
DeliveryDateStart        DATE NOT NULL
CONSTRAINT DF_OrderSelection_DeliveryDateStart DEFAULT TO_DATE('1900-01-01', 'YYYY-MM-DD'),
DeliveryDateEnd          DATE NOT NULL
CONSTRAINT DF_OrderSelection_DeliveryDateEnd DEFAULT TO_DATE('2100-01-01', 'YYYY-MM-DD'),
StorerKeyStart           CHAR(15) NOT NULL
CONSTRAINT DF_OrderSelection_StorerKeyStart DEFAULT ' ',
StorerKeyEnd             CHAR(15) NOT NULL
CONSTRAINT DF_OrderSelection_StorerKeyEnd DEFAULT 'ZZZZZZZZZZZZZZZ',
OrderKeyStart            CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_OrderKeyStart DEFAULT ' ',
OrderKeyEnd              CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_OrderKeyEnd DEFAULT 'ZZZZZZZZZZ',
ConsigneeKeyStart        CHAR(15) NOT NULL
CONSTRAINT DF_OrderSelection_ConsigneeKeyStart DEFAULT ' ',
ConsigneeKeyEnd          CHAR(15) NOT NULL
CONSTRAINT DF_OrderSelection_ConsigneeKeyEnd DEFAULT 'ZZZZZZZZZZZZZZZ',
CarrierKeyStart          CHAR(15) NOT NULL
CONSTRAINT DF_OrderSelection_CarrierKeyStart DEFAULT ' ',
CarrierKeyEnd            CHAR(15) NOT NULL
CONSTRAINT DF_OrderSelection_CarrierKeyEnd DEFAULT 'ZZZZZZZZZZZZZZZ',
RouteStart               CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_RouteStart DEFAULT ' ',
RouteEnd                 CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_RouteEnd DEFAULT 'ZZZZZZZZZZ',
StopStart                CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_StopStart DEFAULT ' ',
StopEnd                  CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_StopEnd DEFAULT 'ZZZZZZZZZZ',
DoorStart                CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_DoorStart DEFAULT ' ',
DoorEnd                  CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_DoorEnd DEFAULT 'ZZZZZZZZZZ',
TypeStart                CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_TypeStart DEFAULT ' ',
TypeEnd                  CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_TypeEnd DEFAULT 'ZZZZZZZZZZ',
OrderGroupStart          CHAR(20) NOT NULL
CONSTRAINT DF_OrderSelection_OrderGroupStart DEFAULT ' ',
OrderGroupEnd            CHAR(20) NOT NULL
CONSTRAINT DF_OrderSelection_OrderGroupEnd DEFAULT 'ZZZZZZZZZZZZZZZZZZZZ',
IntermodalVehicleStart   CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_IntermodalVehicleStart DEFAULT ' ',
IntermodalVehicleEnd     CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_IntermodalVehicleEnd DEFAULT 'ZZZZZZZZZZ',
StatusStart              CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_StatusStart DEFAULT ' ',
StatusEnd                CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_StatusEnd DEFAULT 'ZZZZZZZZZZ',
PriorityStart            CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_PriorityStart DEFAULT ' ',
PriorityEnd              CHAR(10) NOT NULL
CONSTRAINT DF_OrderSelection_PriorityEnd DEFAULT 'ZZZZZZZZZZ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_OrderSelection_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_OrderSelection_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_OrderSelection_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_OrderSelection_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE OrderSelection ADD CONSTRAINT PK_OrderSelection PRIMARY KEY (OrderSelectionKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON OrderSelection TO nsql;

PROMPT '>>> 已创建表 OrderSelection (订单选择表)';

-- =============================================
-- 事务头表
-- =============================================

-- ITRNHDR 表 (事务头表)
CREATE TABLE ITRNHDR
(HeaderType               CHAR(2) NOT NULL,
ItrnKey                  CHAR(10) NOT NULL,
HeaderKey                CHAR(10) NOT NULL,
AddDate                  DATE NOT NULL
CONSTRAINT DF_ITRNHDR_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_ITRNHDR_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_ITRNHDR_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_ITRNHDR_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE ITRNHDR ADD CONSTRAINT PK_ITRNHDR PRIMARY KEY (HeaderType, ItrnKey, HeaderKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON ITRNHDR TO nsql;

PROMPT '>>> 已创建表 ITRNHDR (事务头表)';

-- =============================================
-- 运营处理表
-- =============================================

-- OP_CARTONLINES 表 (装箱行表)
CREATE TABLE OP_CARTONLINES
(
Cartonbatch CHAR(10) NOT NULL,
PickDetailKey CHAR(10) NOT NULL,
PickHeaderKey CHAR(10) NULL,
OrderKey CHAR(10) NULL,
OrderLineNumber CHAR(5) NULL,
Storerkey CHAR(15) NULL,
Sku CHAR(20) NULL,
Lot CHAR(10) NULL,
Loc CHAR(10) NULL,
Id CHAR(18) NULL,
Qty NUMBER(10) NULL,
CaseId CHAR(10) NULL,
CartonType CHAR(10) NULL,
CartonGroup CHAR(10) NULL,
AddDate DATE NOT NULL
CONSTRAINT DF_OP_CARTONLINES_AddDate DEFAULT SYSDATE,
AddWho CHAR(18) NOT NULL
CONSTRAINT DF_OP_CARTONLINES_AddWho DEFAULT USER,
EditDate DATE NOT NULL
CONSTRAINT DF_OP_CARTONLINES_EditDate DEFAULT SYSDATE,
EditWho CHAR(18) NOT NULL
CONSTRAINT DF_OP_CARTONLINES_EditWho DEFAULT USER,
TrafficCop CHAR(1) NULL,
ArchiveCop CHAR(1) NULL
);

-- 添加主键
ALTER TABLE OP_CARTONLINES ADD CONSTRAINT PK_OP_CARTONLINES PRIMARY KEY (Cartonbatch, PickDetailKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON OP_CARTONLINES TO nsql;

PROMPT '>>> 已创建表 OP_CARTONLINES (装箱行表)';

-- =============================================
-- 转移管理表
-- =============================================

-- TRANSFER 表 (转移表)
CREATE TABLE TRANSFER
(TransferKey              CHAR(10) NOT NULL,
FromStorerKey            CHAR(15) NOT NULL
CONSTRAINT DF_TRANSFER_FromStorerKey DEFAULT ' ',
ToStorerKey              CHAR(15) NOT NULL
CONSTRAINT DF_TRANSFER_ToStorerKey DEFAULT ' ',
ExternTransferKey        CHAR(30) NOT NULL
CONSTRAINT DF_TRANSFER_ExternTransferKey DEFAULT ' ',
TransferDate             DATE NOT NULL
CONSTRAINT DF_TRANSFER_TransferDate DEFAULT SYSDATE,
Type                     CHAR(10) NOT NULL
CONSTRAINT DF_TRANSFER_Type DEFAULT '0',
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_TRANSFER_Status DEFAULT '0',
Notes                    CLOB NULL,
EffectiveDate            DATE NOT NULL
CONSTRAINT DF_TRANSFER_EffectiveDate DEFAULT SYSDATE,
AddDate                  DATE NOT NULL
CONSTRAINT DF_TRANSFER_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TRANSFER_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TRANSFER_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TRANSFER_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TRANSFER ADD CONSTRAINT PK_TRANSFER PRIMARY KEY (TransferKey);

-- 添加检查约束
ALTER TABLE TRANSFER ADD CONSTRAINT CK_TRANSFER_Status 
CHECK (Status IN ('0', '1', '2', '3', '4', '5', '9'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TRANSFER TO nsql;

PROMPT '>>> 已创建表 TRANSFER (转移表)';

-- TRANSFERDETAIL 表 (转移明细表)
CREATE TABLE TRANSFERDETAIL
(TransferKey              CHAR(10) NOT NULL,
TransferLineNumber       CHAR(5) NOT NULL,
FromStorerKey            CHAR(15) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_FromStorerKey DEFAULT ' ',
FromSku                  CHAR(20) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_FromSku DEFAULT ' ',
ToStorerKey              CHAR(15) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_ToStorerKey DEFAULT ' ',
ToSku                    CHAR(20) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_ToSku DEFAULT ' ',
ExternTransferKey        CHAR(30) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_ExternTransferKey DEFAULT ' ',
ExternLineNo             CHAR(10) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_ExternLineNo DEFAULT ' ',
Qty                      NUMBER(10) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_Qty DEFAULT 0,
QtyTransferred           NUMBER(10) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_QtyTransferred DEFAULT 0,
UOM                      CHAR(10) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_UOM DEFAULT ' ',
PackKey                  CHAR(10) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_PackKey DEFAULT 'STD',
Lot                      CHAR(10) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_Lot DEFAULT ' ',
ID                       CHAR(18) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_ID DEFAULT ' ',
FromLoc                  CHAR(10) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_FromLoc DEFAULT 'UNKNOWN',
ToLoc                    CHAR(10) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_ToLoc DEFAULT 'UNKNOWN',
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_Status DEFAULT '0',
Lottable01              CHAR(18) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_LOTTABLE01 DEFAULT ' ',
Lottable02              CHAR(18) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_LOTTABLE02 DEFAULT ' ',
Lottable03              CHAR(18) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_LOTTABLE03 DEFAULT ' ',
Lottable04              DATE NULL,
Lottable05              DATE NULL,
EffectiveDate            DATE NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_EffectiveDate DEFAULT SYSDATE,
AddDate                  DATE NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TRANSFERDETAIL_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TRANSFERDETAIL ADD CONSTRAINT PK_TRANSFERDETAIL PRIMARY KEY (TransferKey, TransferLineNumber);

-- 添加检查约束
ALTER TABLE TRANSFERDETAIL ADD CONSTRAINT CK_TRANSFERDETAIL_Status
CHECK (Status IN ('0', '1', '2', '3', '4', '5', '9'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TRANSFERDETAIL TO nsql;

PROMPT '>>> 已创建表 TRANSFERDETAIL (转移明细表)';

-- =============================================
-- 错误处理表
-- =============================================

-- HIERROR 表 (主机接口错误表)
CREATE TABLE HIERROR
(HiErrorGroup             CHAR(10) NOT NULL,
ErrorText                VARCHAR2(254) NOT NULL,
ErrorType                CHAR(20) NOT NULL,
SourceKey                CHAR(20) NOT NULL,
AddDate                  DATE NOT NULL
CONSTRAINT DF_HIERROR_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_HIERROR_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_HIERROR_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_HIERROR_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON HIERROR TO nsql;

PROMPT '>>> 已创建表 HIERROR (主机接口错误表)';

-- =============================================
-- 标签管理表
-- =============================================

-- LABELLIST 表 (标签列表表)
CREATE TABLE LABELLIST
(
LabelName           CHAR(30) NOT NULL
CONSTRAINT DF_LABELLIST_LabelName DEFAULT ' ',
LabelDesc           CHAR(60) NOT NULL
CONSTRAINT DF_LABELLIST_LabelDesc DEFAULT ' ',
LabelType           CHAR(10) NOT NULL
CONSTRAINT DF_LABELLIST_LabelType DEFAULT ' ',
LabelFormat         CHAR(30) NOT NULL
CONSTRAINT DF_LABELLIST_LabelFormat DEFAULT ' ',
LabelPrinter        CHAR(30) NOT NULL
CONSTRAINT DF_LABELLIST_LabelPrinter DEFAULT ' ',
AddDate             DATE NOT NULL
CONSTRAINT DF_LABELLIST_AddDate DEFAULT SYSDATE,
AddWho              CHAR(18) NOT NULL
CONSTRAINT DF_LABELLIST_AddWho DEFAULT USER,
EditDate            DATE NOT NULL
CONSTRAINT DF_LABELLIST_EditDate DEFAULT SYSDATE,
EditWho             CHAR(18) NOT NULL
CONSTRAINT DF_LABELLIST_EditWho DEFAULT USER,
TrafficCop          CHAR(1) NULL,
ArchiveCop          CHAR(1) NULL
);

-- 添加主键
ALTER TABLE LABELLIST ADD CONSTRAINT PK_LABELLIST PRIMARY KEY (LabelName);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON LABELLIST TO nsql;

PROMPT '>>> 已创建表 LABELLIST (标签列表表)';

-- 提交事务
COMMIT;

PROMPT '>>> 遗漏重要表结构脚本执行完成 (7张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
