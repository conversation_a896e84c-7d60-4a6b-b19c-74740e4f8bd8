# Oracle版本 - 仓库管理系统数据库重构脚本

## 概述

这是仓库管理系统(WMS)数据库重构脚本的Oracle版本，包含完整的145张表结构，从MS SQL Server语法转换为Oracle语法。该脚本集提供了一个完整的、生产级的仓库管理系统数据库架构。

## 🎯 **核心特性**

- **145张表** - 完整覆盖企业级WMS所有功能
- **Oracle兼容** - 支持Oracle 11g/12c/19c/21c
- **模块化设计** - 12个独立的表结构脚本
- **生产级质量** - 完整的约束、索引和权限控制
- **企业级功能** - 支持复杂的仓库管理业务流程

## 📁 **脚本文件结构**

### 主控制脚本 (1个)
- **Oracle_00_master_deployment_script.sql** - 主部署脚本，协调执行所有子脚本

### 清理脚本 (1个)
- **Oracle_01_cleanup_and_drop_objects.sql** - 清理和删除相关对象脚本

### 表结构脚本 (12个)
- **Oracle_02_create_table_structures.sql** - 创建核心表结构脚本
- **Oracle_02b_create_additional_tables.sql** - 创建附加表结构脚本
- **Oracle_02c_create_order_management_tables.sql** - 创建订单管理表结构脚本
- **Oracle_02d_create_physical_and_report_tables.sql** - 创建盘点和报表表结构脚本
- **Oracle_02e_create_missing_important_tables.sql** - 创建遗漏重要表结构脚本
- **Oracle_02f_create_billing_and_financial_tables.sql** - 创建计费财务表结构脚本
- **Oracle_02g_create_strategy_and_task_tables.sql** - 创建策略任务表结构脚本
- **Oracle_02h_create_additional_specialized_tables.sql** - 创建附加专业表结构脚本
- **Oracle_02i_create_remaining_missing_tables.sql** - 创建剩余遗漏表结构脚本
- **Oracle_02j_create_final_missing_tables.sql** - 创建最终遗漏表结构脚本
- **Oracle_02k_create_final_specialized_tables.sql** - 创建最终专业表结构脚本
- **Oracle_02l_create_remaining_final_tables.sql** - 创建剩余最终表结构脚本

### 业务逻辑脚本 (4个)
- **Oracle_03_create_stored_procedures.sql** - 创建核心存储过程脚本 (20个存储过程)
- **Oracle_03b_create_additional_procedures.sql** - 创建附加业务存储过程脚本 (7个存储过程)
- **Oracle_04_create_triggers.sql** - 创建触发器脚本
- **Oracle_05_create_views_and_indexes.sql** - 创建视图和索引脚本

### 验证脚本 (1个)
- **Oracle_06_database_objects_statistics.sql** - 数据库对象统计脚本

## 🔄 **SQL Server到Oracle的主要转换**

### 数据类型转换
| SQL Server | Oracle | 说明 |
|------------|--------|------|
| `char(n)` | `CHAR(n)` | 固定长度字符串 |
| `varchar(n)` | `VARCHAR2(n)` | 可变长度字符串 |
| `text` | `CLOB` | 大文本对象 |
| `datetime` | `DATE` | 日期时间 |
| `int` | `NUMBER(10)` | 整数 |
| `float` | `NUMBER(12,6)` | 浮点数 |
| `money` | `NUMBER(12,6)` | 货币 |
| `decimal(p,s)` | `NUMBER(p,s)` | 精确数值 |

### 语法转换
| SQL Server | Oracle | 说明 |
|------------|--------|------|
| `CURRENT_TIMESTAMP` | `SYSDATE` | 当前时间戳 |
| `USER` | `USER` | 当前用户 |
| `IDENTITY(1,1)` | `序列 + 触发器` | 自增字段 |
| `GO` | `/` | 批处理分隔符 |
| `PRINT` | `DBMS_OUTPUT.PUT_LINE` | 输出信息 |

### 约束和索引
- 主键约束：使用 `ALTER TABLE ADD CONSTRAINT`
- 检查约束：使用 `ALTER TABLE ADD CONSTRAINT CHECK`
- 索引：使用 `CREATE INDEX`
- 权限：使用 `GRANT`

## 🚀 **安装和使用**

### 系统要求
- Oracle Database 11g 或更高版本
- 足够的表空间 (建议至少1GB)
- 具有创建表、索引、序列权限的数据库用户
- nsql用户或角色 (用于权限授予)

### 快速安装

#### 方法1：一键执行
```sql
-- 连接到Oracle数据库
sqlplus username/password@database

-- 执行主脚本
@Oracle_00_master_deployment_script.sql
```

#### 方法2：分步执行
```sql
-- 1. 清理现有对象
@Oracle_01_cleanup_and_drop_objects.sql

-- 2. 创建表结构 (按顺序执行12个脚本)
@Oracle_02_create_table_structures.sql
@Oracle_02b_create_additional_tables.sql
@Oracle_02c_create_order_management_tables.sql
@Oracle_02d_create_physical_and_report_tables.sql
@Oracle_02e_create_missing_important_tables.sql
@Oracle_02f_create_billing_and_financial_tables.sql
@Oracle_02g_create_strategy_and_task_tables.sql
@Oracle_02h_create_additional_specialized_tables.sql
@Oracle_02i_create_remaining_missing_tables.sql
@Oracle_02j_create_final_missing_tables.sql
@Oracle_02k_create_final_specialized_tables.sql
@Oracle_02l_create_remaining_final_tables.sql

-- 3. 创建业务逻辑
@Oracle_03_create_stored_procedures.sql
@Oracle_03b_create_additional_procedures.sql
@Oracle_04_create_triggers.sql
@Oracle_05_create_views_and_indexes.sql

-- 4. 验证安装
@Oracle_06_database_objects_statistics.sql
```

### 执行顺序

**重要：** 必须按照以下顺序执行脚本：

1. `Oracle_01_cleanup_and_drop_objects.sql` - 清理现有对象
2. `Oracle_02_create_table_structures.sql` - 创建核心表结构
3. `Oracle_02b_create_additional_tables.sql` - 创建附加表结构
4. `Oracle_02c_create_order_management_tables.sql` - 创建订单管理表结构
5. `Oracle_02d_create_physical_and_report_tables.sql` - 创建盘点和报表表结构
6. `Oracle_02e_create_missing_important_tables.sql` - 创建遗漏重要表结构
7. `Oracle_02f_create_billing_and_financial_tables.sql` - 创建计费财务表结构
8. `Oracle_02g_create_strategy_and_task_tables.sql` - 创建策略任务表结构
9. `Oracle_02h_create_additional_specialized_tables.sql` - 创建附加专业表结构
10. `Oracle_02i_create_remaining_missing_tables.sql` - 创建剩余遗漏表结构
11. `Oracle_02j_create_final_missing_tables.sql` - 创建最终遗漏表结构
12. `Oracle_02k_create_final_specialized_tables.sql` - 创建最终专业表结构
13. `Oracle_02l_create_remaining_final_tables.sql` - 创建剩余最终表结构
14. `Oracle_03_create_stored_procedures.sql` - 创建核心存储过程
15. `Oracle_03b_create_additional_procedures.sql` - 创建附加业务存储过程
16. `Oracle_04_create_triggers.sql` - 创建触发器
17. `Oracle_05_create_views_and_indexes.sql` - 创建视图和索引
18. `Oracle_06_database_objects_statistics.sql` - 统计验证对象

## 📊 **功能模块覆盖**

### 核心业务模块
- ✅ **库存管理** - 实时库存跟踪和管理
- ✅ **订单处理** - 完整的订单生命周期管理
- ✅ **收货管理** - 采购订单和收货处理
- ✅ **发货管理** - 拣选、装箱和发货
- ✅ **库存调整** - 调整和补货管理
- ✅ **物理盘点** - 完整的盘点流程
- ✅ **循环盘点** - 持续的库存验证

### 高级功能模块
- ✅ **计费系统** - 复杂的客户计费管理
- ✅ **财务集成** - 会计科目和税务管理
- ✅ **策略引擎** - 智能分配和上架策略
- ✅ **任务管理** - 任务调度和用户管理
- ✅ **航空货运** - 航空运输单据管理
- ✅ **海运管理** - 海运提单管理
- ✅ **报表系统** - 灵活的报表框架

### 专业化模块
- ✅ **RF设备支持** - 无线射频设备集成
- ✅ **消息管理** - 系统消息和通知
- ✅ **追踪管理** - 完整的追踪体系
- ✅ **交叉转运** - 高级物流功能
- ✅ **库存控制** - 精细化库存管理

### 存储过程模块 (27个存储过程)
- ✅ **核心系统过程** - 错误处理、键值生成、单位转换 (8个)
- ✅ **库存事务过程** - 入库、出库、移动、调整事务 (4个)
- ✅ **RF设备过程** - RF上架、装箱处理 (3个)
- ✅ **订单拣选过程** - 订单分配、拣选确认、波次管理 (3个)
- ✅ **库存冻结过程** - 库存冻结和解冻管理 (2个)
- ✅ **计费管理过程** - 计费运行和发票生成 (2个)
- ✅ **盘点管理过程** - 物理盘点、循环盘点 (3个)
- ✅ **补货管理过程** - 补货任务生成和管理 (1个)
- ✅ **批次管理过程** - 批次生成和查找 (1个)

## ⚠️ **注意事项**

### 执行前准备
1. **备份现有数据** - 执行前务必备份现有数据库
2. **检查权限** - 确保用户具有足够的数据库权限
3. **检查表空间** - 确保有足够的存储空间
4. **创建nsql用户** - 如果不存在，需要先创建nsql用户或角色

### 常见问题
1. **权限不足** - 确保用户具有CREATE TABLE、CREATE INDEX等权限
2. **表空间不足** - 增加表空间大小或创建新的表空间
3. **对象已存在** - 先运行清理脚本删除现有对象
4. **字符集问题** - 确保数据库字符集支持所需的字符

### 性能优化建议
1. **表空间分离** - 将表和索引放在不同的表空间
2. **分区策略** - 对大表考虑分区
3. **统计信息** - 定期收集表统计信息
4. **监控性能** - 使用Oracle性能监控工具

## 📞 **技术支持**

如果在安装或使用过程中遇到问题，请检查：

1. **Oracle版本兼容性** - 确保使用支持的Oracle版本
2. **错误日志** - 查看Oracle alert log和trace文件
3. **脚本输出** - 检查脚本执行过程中的输出信息
4. **系统资源** - 确保有足够的内存和存储空间

---

**版本**: Oracle 1.0  
**支持的Oracle版本**: 11g, 12c, 19c, 21c  
**总表数量**: 145张表  
**脚本文件**: 18个文件  
**功能覆盖**: 企业级完整WMS系统
