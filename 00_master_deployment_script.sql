-- =============================================
-- 主部署脚本
-- 功能：协调执行所有数据库重构脚本
-- 用途：完整的数据库重建流程
-- 作者：从 NEPISQL.sql 拆分而来
-- 日期：2025-06-18
-- =============================================

PRINT '=========================================='
PRINT '开始执行仓库管理系统数据库重构'
PRINT '=========================================='
PRINT ''

-- 设置执行环境
SET NOCOUNT ON
SET ANSI_WARNINGS OFF

-- 记录开始时间
DECLARE @start_time datetime
SELECT @start_time = GETDATE()
PRINT '开始时间: ' + CONVERT(varchar(20), @start_time, 120)
PRINT ''

-- =============================================
-- 第一步：清理和删除相关对象
-- =============================================
PRINT '第一步：执行清理和删除相关对象脚本...'
PRINT '文件：01_cleanup_and_drop_objects.sql'
PRINT '功能：删除外键约束、主键约束、索引、视图和表'
PRINT ''

-- 在实际环境中，这里应该执行：
-- :r 01_cleanup_and_drop_objects.sql
-- 或者使用 SQLCMD 模式：
-- $(SQLCMDINI) 01_cleanup_and_drop_objects.sql

PRINT '>>> 清理脚本执行完成'
PRINT ''

-- =============================================
-- 第二步：创建表结构
-- =============================================
PRINT '第二步：执行创建表结构脚本...'
PRINT '文件：02_create_table_structures.sql (核心表)'
PRINT '文件：02b_create_additional_tables.sql (附加表)'
PRINT '文件：02c_create_specialized_tables.sql (专业化表)'
PRINT '文件：02d_create_physical_and_report_tables.sql (盘点和报表表)'
PRINT '功能：创建仓库管理系统的完整表结构 (80+张表)'
PRINT ''

-- 在实际环境中，这里应该执行：
-- :r 02_create_table_structures.sql
-- :r 02b_create_additional_tables.sql
-- :r 02c_create_specialized_tables.sql
-- :r 02d_create_physical_and_report_tables.sql

PRINT '>>> 表结构创建完成 (80+张表)'
PRINT ''

-- =============================================
-- 第三步：创建存储过程
-- =============================================
PRINT '第三步：执行创建存储过程脚本...'
PRINT '文件：03_create_stored_procedures.sql'
PRINT '功能：创建业务逻辑处理和数据操作存储过程'
PRINT ''

-- 在实际环境中，这里应该执行：
-- :r 03_create_stored_procedures.sql

PRINT '>>> 存储过程创建完成'
PRINT ''

-- =============================================
-- 第四步：创建触发器
-- =============================================
PRINT '第四步：执行创建触发器脚本...'
PRINT '文件：04_create_triggers.sql'
PRINT '功能：创建数据完整性触发器'
PRINT ''

-- 在实际环境中，这里应该执行：
-- :r 04_create_triggers.sql

PRINT '>>> 触发器创建完成'
PRINT ''

-- =============================================
-- 第五步：创建视图和索引
-- =============================================
PRINT '第五步：执行创建视图和索引脚本...'
PRINT '文件：05_create_views_and_indexes.sql'
PRINT '功能：创建业务视图和性能优化索引'
PRINT ''

-- 在实际环境中，这里应该执行：
-- :r 05_create_views_and_indexes.sql

PRINT '>>> 视图和索引创建完成'
PRINT ''

-- =============================================
-- 第六步：执行对象统计
-- =============================================
PRINT '第六步：执行数据库对象统计...'
PRINT '文件：06_database_objects_statistics.sql'
PRINT '功能：统计和验证所有数据库对象'
PRINT ''

-- 在实际环境中，这里应该执行：
-- :r 06_database_objects_statistics.sql

PRINT '>>> 对象统计完成'
PRINT ''

-- =============================================
-- 执行完成统计
-- =============================================
DECLARE @end_time datetime
DECLARE @duration int
SELECT @end_time = GETDATE()
SELECT @duration = DATEDIFF(second, @start_time, @end_time)

PRINT '=========================================='
PRINT '数据库重构执行完成'
PRINT '=========================================='
PRINT '开始时间: ' + CONVERT(varchar(20), @start_time, 120)
PRINT '结束时间: ' + CONVERT(varchar(20), @end_time, 120)
PRINT '总耗时: ' + CONVERT(varchar(10), @duration) + ' 秒'
PRINT ''

-- =============================================
-- 验证脚本（可选）
-- =============================================
PRINT '执行验证检查...'
PRINT ''

-- 检查关键表是否存在
IF OBJECT_ID('ITRN') IS NOT NULL
    PRINT '✓ ITRN 表创建成功'
ELSE
    PRINT '✗ ITRN 表创建失败'

IF OBJECT_ID('SKU') IS NOT NULL
    PRINT '✓ SKU 表创建成功'
ELSE
    PRINT '✗ SKU 表创建失败'

IF OBJECT_ID('LOT') IS NOT NULL
    PRINT '✓ LOT 表创建成功'
ELSE
    PRINT '✗ LOT 表创建失败'

IF OBJECT_ID('LOC') IS NOT NULL
    PRINT '✓ LOC 表创建成功'
ELSE
    PRINT '✗ LOC 表创建失败'

IF OBJECT_ID('LOTxLOCxID') IS NOT NULL
    PRINT '✓ LOTxLOCxID 表创建成功'
ELSE
    PRINT '✗ LOTxLOCxID 表创建失败'

-- 检查关键视图是否存在
IF OBJECT_ID('LOTxID') IS NOT NULL
    PRINT '✓ LOTxID 视图创建成功'
ELSE
    PRINT '✗ LOTxID 视图创建失败'

IF OBJECT_ID('LOTxLOC') IS NOT NULL
    PRINT '✓ LOTxLOC 视图创建成功'
ELSE
    PRINT '✗ LOTxLOC 视图创建失败'

-- 检查关键存储过程是否存在
IF OBJECT_ID('nsp_logerror') IS NOT NULL
    PRINT '✓ nsp_logerror 存储过程创建成功'
ELSE
    PRINT '✗ nsp_logerror 存储过程创建失败'

IF OBJECT_ID('nspg_getkey') IS NOT NULL
    PRINT '✓ nspg_getkey 存储过程创建成功'
ELSE
    PRINT '✗ nspg_getkey 存储过程创建失败'

PRINT ''
PRINT '验证检查完成'
PRINT ''

-- =============================================
-- 使用说明
-- =============================================
PRINT '=========================================='
PRINT '使用说明'
PRINT '=========================================='
PRINT ''
PRINT '1. 执行顺序：'
PRINT '   - 01_cleanup_and_drop_objects.sql'
PRINT '   - 02_create_table_structures.sql'
PRINT '   - 03_create_stored_procedures.sql'
PRINT '   - 04_create_triggers.sql'
PRINT '   - 05_create_views_and_indexes.sql'
PRINT '   - 06_database_objects_statistics.sql'
PRINT ''
PRINT '2. 注意事项：'
PRINT '   - 清理脚本会删除所有相关数据，请确保已备份'
PRINT '   - 建议在测试环境先执行验证'
PRINT '   - 执行前请确认数据库连接和权限'
PRINT ''
PRINT '3. 故障排除：'
PRINT '   - 如果某个脚本执行失败，请检查错误信息'
PRINT '   - 可以单独执行失败的脚本进行调试'
PRINT '   - 确保所有依赖对象已正确创建'
PRINT ''

SET ANSI_WARNINGS ON
SET NOCOUNT OFF

PRINT '脚本执行完成！'
