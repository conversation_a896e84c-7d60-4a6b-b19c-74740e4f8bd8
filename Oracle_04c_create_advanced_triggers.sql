-- =============================================
-- Oracle版本 - 创建高级触发器脚本 (第三部分)
-- 功能：创建仓库管理系统的高级触发器集合 (Oracle版本)
-- 用途：盘点管理、任务管理、Drop ID管理、区域管理等专业触发器
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中的108个SQL Server触发器转换为Oracle语法 (高级部分)
-- 包含：盘点管理、任务管理、Drop ID管理、区域管理等触发器
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 盘点管理触发器 (从SQL Server转换)
-- =============================================

-- CC表的插入触发器 (从ntrCCAdd转换)
CREATE OR REPLACE TRIGGER NTR_CC_ADD
BEFORE INSERT ON CC
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    -- 生成盘点键如果为空
    IF :NEW.CCKey IS NULL OR TRIM(:NEW.CCKey) = ' ' THEN
        SELECT 'CC' || LPAD(TO_CHAR(SEQ_CC.NEXTVAL), 8, '0') INTO :NEW.CCKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CC_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CC_ADD (盘点插入触发器)';

-- CC表的更新触发器 (从ntrCCUpdate转换)
CREATE OR REPLACE TRIGGER NTR_CC_UPDATE
BEFORE UPDATE ON CC
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
    -- 状态变更日志
    IF :OLD.Status != :NEW.Status THEN
        INSERT INTO ALERT (
            AlertKey, ModuleName, AlertType, AlertMessage, 
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
            'CC',
            'STATUS_CHANGE',
            'Cycle Count ' || :NEW.CCKey || ' status changed from ' || :OLD.Status || ' to ' || :NEW.Status,
            SYSDATE, USER, SYSDATE, USER
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CC_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CC_UPDATE (盘点更新触发器)';

-- CC表的删除触发器 (从ntrCCDelete转换)
CREATE OR REPLACE TRIGGER NTR_CC_DELETE
BEFORE DELETE ON CC
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有盘点明细
    SELECT COUNT(*) INTO v_cnt
    FROM CCDETAIL
    WHERE CCKey = :OLD.CCKey;
    
    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20009, 'Cannot delete cycle count with existing cycle count details');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CC_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CC_DELETE (盘点删除触发器)';

-- CCDETAIL表的插入触发器 (从ntrCCDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_CCDETAIL_ADD
BEFORE INSERT ON CCDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    IF :NEW.QtyExpected IS NULL THEN
        :NEW.QtyExpected := 0;
    END IF;
    
    IF :NEW.QtyCountedA IS NULL THEN
        :NEW.QtyCountedA := 0;
    END IF;
    
    IF :NEW.QtyCountedB IS NULL THEN
        :NEW.QtyCountedB := 0;
    END IF;
    
    -- 生成盘点明细键如果为空
    IF :NEW.CCDetailKey IS NULL OR TRIM(:NEW.CCDetailKey) = ' ' THEN
        SELECT 'CCD' || LPAD(TO_CHAR(SEQ_CCDETAIL.NEXTVAL), 7, '0') INTO :NEW.CCDetailKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CCDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CCDETAIL_ADD (盘点明细插入触发器)';

-- CCDETAIL表的更新触发器 (从ntrCCDetailUpd转换)
CREATE OR REPLACE TRIGGER NTR_CCDETAIL_UPDATE
BEFORE UPDATE ON CCDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
    -- 计算差异
    IF :NEW.QtyCountedA IS NOT NULL AND :NEW.QtyExpected IS NOT NULL THEN
        :NEW.VarianceA := :NEW.QtyCountedA - :NEW.QtyExpected;
    END IF;
    
    IF :NEW.QtyCountedB IS NOT NULL AND :NEW.QtyExpected IS NOT NULL THEN
        :NEW.VarianceB := :NEW.QtyCountedB - :NEW.QtyExpected;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CCDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CCDETAIL_UPDATE (盘点明细更新触发器)';

-- CCDETAIL表的删除触发器 (从ntrCCDetailDel转换)
CREATE OR REPLACE TRIGGER NTR_CCDETAIL_DELETE
AFTER DELETE ON CCDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'CCDETAIL',
        'RECORD_DELETE',
        'Cycle Count Detail deleted: ' || :OLD.CCKey || '/' || :OLD.CCLineNumber,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_CCDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_CCDETAIL_DELETE (盘点明细删除触发器)';

-- =============================================
-- 任务管理触发器 (从SQL Server转换)
-- =============================================

-- TASKDETAIL表的插入触发器 (从ntrTaskDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_TASKDETAIL_ADD
BEFORE INSERT ON TASKDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 设置默认值
    IF :NEW.Status IS NULL THEN
        :NEW.Status := '0';
    END IF;
    
    IF :NEW.Priority IS NULL THEN
        :NEW.Priority := '5';
    END IF;
    
    -- 生成任务明细键如果为空
    IF :NEW.TaskDetailKey IS NULL OR TRIM(:NEW.TaskDetailKey) = ' ' THEN
        SELECT 'TD' || LPAD(TO_CHAR(SEQ_TASKDETAIL.NEXTVAL), 8, '0') INTO :NEW.TaskDetailKey FROM DUAL;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_TASKDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_TASKDETAIL_ADD (任务明细插入触发器)';

-- TASKDETAIL表的更新触发器 (从ntrTaskDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_TASKDETAIL_UPDATE
BEFORE UPDATE ON TASKDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;
    
    -- 如果状态变为完成，设置完成时间和用户
    IF :NEW.Status = '9' AND :OLD.Status != '9' THEN
        :NEW.CompletedDate := SYSDATE;
        :NEW.CompletedUser := USER;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_TASKDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_TASKDETAIL_UPDATE (任务明细更新触发器)';

-- TASKDETAIL表的删除触发器 (从ntrTaskDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_TASKDETAIL_DELETE
AFTER DELETE ON TASKDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage, 
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'TASKDETAIL',
        'RECORD_DELETE',
        'Task Detail deleted: ' || :OLD.TaskDetailKey || ' Type: ' || :OLD.TaskType,
        SYSDATE, USER, SYSDATE, USER
    );
    
EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_TASKDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_TASKDETAIL_DELETE (任务明细删除触发器)';

-- =============================================
-- Drop ID管理触发器 (从SQL Server转换)
-- =============================================

-- DROPID表的插入触发器 (从ntrDropIDHeaderAdd转换)
CREATE OR REPLACE TRIGGER NTR_DROPID_ADD
BEFORE INSERT ON DROPID
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 验证Drop ID格式
    IF LENGTH(TRIM(:NEW.Dropid)) < 1 THEN
        RAISE_APPLICATION_ERROR(-20010, 'Drop ID cannot be empty');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_DROPID_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_DROPID_ADD (Drop ID插入触发器)';

-- DROPID表的更新触发器 (从ntrDropIDHeaderUpdate转换)
CREATE OR REPLACE TRIGGER NTR_DROPID_UPDATE
BEFORE UPDATE ON DROPID
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_DROPID_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_DROPID_UPDATE (Drop ID更新触发器)';

-- DROPID表的删除触发器 (从ntrDropIDHeaderDelete转换)
CREATE OR REPLACE TRIGGER NTR_DROPID_DELETE
BEFORE DELETE ON DROPID
FOR EACH ROW
DECLARE
    v_cnt NUMBER;
BEGIN
    -- 检查是否有Drop ID明细
    SELECT COUNT(*) INTO v_cnt
    FROM DROPIDDETAIL
    WHERE Dropid = :OLD.Dropid;

    IF v_cnt > 0 THEN
        RAISE_APPLICATION_ERROR(-20011, 'Cannot delete Drop ID with existing Drop ID details');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_DROPID_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_DROPID_DELETE (Drop ID删除触发器)';

-- DROPIDDETAIL表的插入触发器 (从ntrDropIDDetailAdd转换)
CREATE OR REPLACE TRIGGER NTR_DROPIDDETAIL_ADD
BEFORE INSERT ON DROPIDDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    :NEW.AddDate := SYSDATE;
    :NEW.AddWho := USER;
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 验证Child ID不能为空
    IF :NEW.ChildId IS NULL OR TRIM(:NEW.ChildId) = ' ' THEN
        RAISE_APPLICATION_ERROR(-20012, 'Child ID cannot be empty');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_DROPIDDETAIL_ADD');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_DROPIDDETAIL_ADD (Drop ID明细插入触发器)';

-- DROPIDDETAIL表的更新触发器 (从ntrDropIDDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_DROPIDDETAIL_UPDATE
BEFORE UPDATE ON DROPIDDETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_DROPIDDETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_DROPIDDETAIL_UPDATE (Drop ID明细更新触发器)';

-- DROPIDDETAIL表的删除触发器 (从ntrDropIDDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_DROPIDDETAIL_DELETE
AFTER DELETE ON DROPIDDETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'DROPIDDETAIL',
        'RECORD_DELETE',
        'Drop ID Detail deleted: ' || :OLD.Dropid || '/' || :OLD.ChildId,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_DROPIDDETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_DROPIDDETAIL_DELETE (Drop ID明细删除触发器)';

-- =============================================
-- 区域管理触发器 (从SQL Server转换)
-- =============================================

-- AREADETAIL表的更新触发器 (从ntrAreaDetailUpdate转换)
CREATE OR REPLACE TRIGGER NTR_AREADETAIL_UPDATE
BEFORE UPDATE ON AREADETAIL
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;

    -- 保持原始添加信息
    :NEW.AddDate := :OLD.AddDate;
    :NEW.AddWho := :OLD.AddWho;

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_AREADETAIL_UPDATE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_AREADETAIL_UPDATE (区域明细更新触发器)';

-- AREADETAIL表的删除触发器 (从ntrAreaDetailDelete转换)
CREATE OR REPLACE TRIGGER NTR_AREADETAIL_DELETE
AFTER DELETE ON AREADETAIL
FOR EACH ROW
BEGIN
    -- 记录删除日志
    INSERT INTO ALERT (
        AlertKey, ModuleName, AlertType, AlertMessage,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
        'AREADETAIL',
        'RECORD_DELETE',
        'Area Detail deleted: ' || :OLD.AreaKey || '/' || :OLD.PutawayZone,
        SYSDATE, USER, SYSDATE, USER
    );

EXCEPTION
    WHEN OTHERS THEN
        nsp_logerror(SQLCODE, SQLERRM, 'NTR_AREADETAIL_DELETE');
        RAISE;
END;
/

PROMPT '>>> 已创建触发器 NTR_AREADETAIL_DELETE (区域明细删除触发器)';

-- 提交事务
COMMIT;

PROMPT '>>> Oracle高级触发器脚本执行完成';
PROMPT '>>> 已创建盘点管理、任务管理、Drop ID管理和区域管理触发器 (17个)';
PROMPT '>>> 所有高级触发器创建完成！';
