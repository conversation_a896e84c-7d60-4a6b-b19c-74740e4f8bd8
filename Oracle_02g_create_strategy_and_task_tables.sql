-- =============================================
-- Oracle版本 - 创建策略和任务管理表结构脚本 (第七部分)
-- 功能：创建仓库管理系统的策略和任务管理相关表结构 (Oracle版本)
-- 用途：分配策略、上架策略、任务管理、设备管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 策略管理表
-- =============================================

-- Strategy 表 (策略表)
CREATE TABLE Strategy
(StrategyKey               CHAR(10) NOT NULL
CONSTRAINT DF_Strategy_StrategyKey DEFAULT ' ',
Descr                     CHAR(60) NOT NULL
CONSTRAINT DF_Strategy_Descr DEFAULT ' ',
PreAllocateStrategyKey    CHAR(10) NOT NULL
CONSTRAINT DF_Strategy_PreAllocateStrategyKey DEFAULT ' ',
AllocateStrategyKey       CHAR(10) NOT NULL
CONSTRAINT DF_Strategy_AllocateStrategyKey DEFAULT ' ',
PutawayStrategyKey        CHAR(10) NOT NULL
CONSTRAINT DF_Strategy_PutawayStrategyKey DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_Strategy_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_Strategy_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_Strategy_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_Strategy_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE Strategy ADD CONSTRAINT PK_Strategy PRIMARY KEY (StrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Strategy TO nsql;

PROMPT '>>> 已创建表 Strategy (策略表)';

-- PreAllocateStrategy 表 (预分配策略表)
CREATE TABLE PreAllocateStrategy
(PreAllocateStrategyKey   CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocateStrategy_PreAllocateStrategyKey DEFAULT ' ',
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_PreAllocateStrategy_Descr DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_PreAllocateStrategy_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PreAllocateStrategy_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PreAllocateStrategy_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PreAllocateStrategy_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PreAllocateStrategy ADD CONSTRAINT PK_PreAllocateStrategy PRIMARY KEY (PreAllocateStrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PreAllocateStrategy TO nsql;

PROMPT '>>> 已创建表 PreAllocateStrategy (预分配策略表)';

-- PreAllocateStrategyDetail 表 (预分配策略明细表)
CREATE TABLE PreAllocateStrategyDetail
(PreAllocateStrategyKey         CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_PreAllocateStrategyKey DEFAULT ' ',
PreAllocateStrategyLineNumber  CHAR(5) NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_PreAllocateStrategyLineNumber DEFAULT ' ',
DESCR                          CHAR(60) NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_DESCR DEFAULT ' ',
StrategyType                   CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_StrategyType DEFAULT ' ',
FromLoc                        CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_FromLoc DEFAULT ' ',
ToLoc                          CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_ToLoc DEFAULT ' ',
AddDate                        DATE NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_AddDate DEFAULT SYSDATE,
AddWho                         CHAR(18) NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_AddWho DEFAULT USER,
EditDate                       DATE NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_EditDate DEFAULT SYSDATE,
EditWho                        CHAR(18) NOT NULL
CONSTRAINT DF_PreAllocateStrategyDetail_EditWho DEFAULT USER,
TrafficCop                     CHAR(1) NULL,
ArchiveCop                     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PreAllocateStrategyDetail ADD CONSTRAINT PK_PreAllocateStrategyDetail 
PRIMARY KEY (PreAllocateStrategyKey, PreAllocateStrategyLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PreAllocateStrategyDetail TO nsql;

PROMPT '>>> 已创建表 PreAllocateStrategyDetail (预分配策略明细表)';

-- AllocateStrategy 表 (分配策略表)
CREATE TABLE AllocateStrategy
(AllocateStrategyKey      CHAR(10) NOT NULL
CONSTRAINT DF_AllocateStrategy_AllocateStrategyKey DEFAULT ' ',
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_AllocateStrategy_Descr DEFAULT ' ',
RetryIfQtyRemain         NUMBER(10) NOT NULL
CONSTRAINT DF_AllocateStrategy_RetryIfQtyRemain DEFAULT 0,
AddDate                  DATE NOT NULL
CONSTRAINT DF_AllocateStrategy_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_AllocateStrategy_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_AllocateStrategy_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_AllocateStrategy_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE AllocateStrategy ADD CONSTRAINT PK_AllocateStrategy PRIMARY KEY (AllocateStrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON AllocateStrategy TO nsql;

PROMPT '>>> 已创建表 AllocateStrategy (分配策略表)';

-- AllocateStrategyDetail 表 (分配策略明细表)
CREATE TABLE AllocateStrategyDetail
(AllocateStrategyKey            CHAR(10) NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_AllocateStrategyKey DEFAULT ' ',
AllocateStrategyLineNumber     CHAR(5) NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_AllocateStrategyLineNumber DEFAULT ' ',
DESCR                          CHAR(60) NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_DESCR DEFAULT ' ',
StrategyType                   CHAR(10) NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_StrategyType DEFAULT ' ',
FromLoc                        CHAR(10) NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_FromLoc DEFAULT ' ',
ToLoc                          CHAR(10) NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_ToLoc DEFAULT ' ',
SortOrder                      CHAR(10) NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_SortOrder DEFAULT ' ',
AddDate                        DATE NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_AddDate DEFAULT SYSDATE,
AddWho                         CHAR(18) NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_AddWho DEFAULT USER,
EditDate                       DATE NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_EditDate DEFAULT SYSDATE,
EditWho                        CHAR(18) NOT NULL
CONSTRAINT DF_AllocateStrategyDetail_EditWho DEFAULT USER,
TrafficCop                     CHAR(1) NULL,
ArchiveCop                     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE AllocateStrategyDetail ADD CONSTRAINT PK_AllocateStrategyDetail 
PRIMARY KEY (AllocateStrategyKey, AllocateStrategyLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON AllocateStrategyDetail TO nsql;

PROMPT '>>> 已创建表 AllocateStrategyDetail (分配策略明细表)';

-- PutawayStrategy 表 (上架策略表)
CREATE TABLE PutawayStrategy
(PutawayStrategyKey       CHAR(10) NOT NULL,
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_PutawayStrategy_Descr DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_PutawayStrategy_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PutawayStrategy_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PutawayStrategy_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PutawayStrategy_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PutawayStrategy ADD CONSTRAINT PK_PutawayStrategy PRIMARY KEY (PutawayStrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PutawayStrategy TO nsql;

PROMPT '>>> 已创建表 PutawayStrategy (上架策略表)';

-- PutawayStrategyDetail 表 (上架策略明细表)
CREATE TABLE PutawayStrategyDetail
(PutawayStrategyKey             CHAR(10) NOT NULL,
PutawayStrategyLineNumber      CHAR(5) NOT NULL,
PAType                         CHAR(5) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_PAType DEFAULT ' ',
FROMLOC                        CHAR(10) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_FROMLOC DEFAULT ' ',
TOLOC                          CHAR(10) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_TOLOC DEFAULT ' ',
LOCTYPE                        CHAR(10) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_LOCTYPE DEFAULT ' ',
PUTAWAYZONE                    CHAR(10) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_PUTAWAYZONE DEFAULT ' ',
MAXQTY                         NUMBER(10) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_MAXQTY DEFAULT 0,
MAXWEIGHT                      NUMBER(12,6) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_MAXWEIGHT DEFAULT 0,
MAXCUBE                        NUMBER(12,6) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_MAXCUBE DEFAULT 0,
MAXPALLETS                     NUMBER(10) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_MAXPALLETS DEFAULT 0,
EQUIPMENTPROFILEKEY            CHAR(10) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_EQUIPMENTPROFILEKEY DEFAULT ' ',
DESCR                          CHAR(60) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_DESCR DEFAULT ' ',
AddDate                        DATE NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_AddDate DEFAULT SYSDATE,
AddWho                         CHAR(18) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_AddWho DEFAULT USER,
EditDate                       DATE NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_EditDate DEFAULT SYSDATE,
EditWho                        CHAR(18) NOT NULL
CONSTRAINT DF_PutawayStrategyDetail_EditWho DEFAULT USER,
TrafficCop                     CHAR(1) NULL,
ArchiveCop                     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PutawayStrategyDetail ADD CONSTRAINT PK_PutawayStrategyDetail
PRIMARY KEY (PutawayStrategyKey, PutawayStrategyLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PutawayStrategyDetail TO nsql;

PROMPT '>>> 已创建表 PutawayStrategyDetail (上架策略明细表)';

-- =============================================
-- 任务管理表
-- =============================================

-- TaskManagerUser 表 (任务管理用户表) - 扩展版本
CREATE TABLE TaskManagerUser
(UserKey                  CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerUser_UserKey DEFAULT ' ',
PriorityTaskType         CHAR(10) NOT NULL
CONSTRAINT DF_TaskManagerUser_PriorityTaskType DEFAULT '1',
StrategyKey              CHAR(10) NOT NULL
CONSTRAINT DF_TaskManagerUser_StrategyKey DEFAULT ' ',
TTMStrategyKey           CHAR(10) NOT NULL
CONSTRAINT DF_TaskManagerUser_TTMStrategyKey DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_TaskManagerUser_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerUser_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TaskManagerUser_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerUser_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TaskManagerUser ADD CONSTRAINT PK_TaskManagerUser PRIMARY KEY (UserKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaskManagerUser TO nsql;

PROMPT '>>> 已创建表 TaskManagerUser (任务管理用户表)';

-- TaskManagerUserDetail 表 (任务管理用户明细表)
CREATE TABLE TaskManagerUserDetail
(UserKey                  CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_UserKey DEFAULT ' ',
UserLineNumber           CHAR(5) NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_UserLineNumber DEFAULT ' ',
PermissionType           CHAR(10) NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_PermissionType DEFAULT ' ',
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_Sku DEFAULT ' ',
Loc                      CHAR(10) NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_Loc DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerUserDetail_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TaskManagerUserDetail ADD CONSTRAINT PK_TaskManagerUserDetail
PRIMARY KEY (UserKey, UserLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaskManagerUserDetail TO nsql;

PROMPT '>>> 已创建表 TaskManagerUserDetail (任务管理用户明细表)';

-- TTMStrategy 表 (任务管理策略表)
CREATE TABLE TTMStrategy
(TTMStrategyKey           CHAR(10) NOT NULL
CONSTRAINT DF_TTMStrategy_TTMStrategyKey DEFAULT ' ',
Descr                    CHAR(60) NOT NULL
CONSTRAINT DF_TTMStrategy_Descr DEFAULT ' ',
InterleaveTasks          CHAR(10) NOT NULL
CONSTRAINT DF_TTMStrategy_InterleaveTasks DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_TTMStrategy_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TTMStrategy_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TTMStrategy_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TTMStrategy_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TTMStrategy ADD CONSTRAINT PK_TTMStrategy PRIMARY KEY (TTMStrategyKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TTMStrategy TO nsql;

PROMPT '>>> 已创建表 TTMStrategy (任务管理策略表)';

-- TTMStrategyDetail 表 (任务管理策略明细表)
CREATE TABLE TTMStrategyDetail
(TTMStrategyKey                 CHAR(10) NOT NULL
CONSTRAINT DF_TTMStrategyDetail_TTMStrategyKey DEFAULT ' ',
TTMStrategyLineNumber          CHAR(5) NOT NULL
CONSTRAINT DF_TTMStrategyDetail_TTMStrategyLineNumber DEFAULT ' ',
Descr                          CHAR(60) NOT NULL
CONSTRAINT DF_TTMStrategyDetail_Descr DEFAULT ' ',
TaskType                       CHAR(10) NOT NULL
CONSTRAINT DF_TTMStrategyDetail_TaskType DEFAULT ' ',
Priority                       CHAR(10) NOT NULL
CONSTRAINT DF_TTMStrategyDetail_Priority DEFAULT ' ',
AddDate                        DATE NOT NULL
CONSTRAINT DF_TTMStrategyDetail_AddDate DEFAULT SYSDATE,
AddWho                         CHAR(18) NOT NULL
CONSTRAINT DF_TTMStrategyDetail_AddWho DEFAULT USER,
EditDate                       DATE NOT NULL
CONSTRAINT DF_TTMStrategyDetail_EditDate DEFAULT SYSDATE,
EditWho                        CHAR(18) NOT NULL
CONSTRAINT DF_TTMStrategyDetail_EditWho DEFAULT USER,
TrafficCop                     CHAR(1) NULL,
ArchiveCop                     CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TTMStrategyDetail ADD CONSTRAINT PK_TTMStrategyDetail
PRIMARY KEY (TTMStrategyKey, TTMStrategyLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TTMStrategyDetail TO nsql;

PROMPT '>>> 已创建表 TTMStrategyDetail (任务管理策略明细表)';

-- 提交事务
COMMIT;

PROMPT '>>> 策略和任务管理表结构脚本执行完成 (11张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
