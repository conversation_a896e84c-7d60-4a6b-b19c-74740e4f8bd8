-- =============================================
-- Oracle版本 - 创建核心表结构脚本
-- 功能：创建仓库管理系统的核心表结构 (Oracle版本)
-- 用途：数据库重构后的核心表结构重建
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- ITRN 表 (库存事务表) - 核心事务记录表
-- =============================================
CREATE TABLE ITRN
(ItrnKey                  CHAR(10) NOT NULL,
TransactionType          CHAR(2) NOT NULL
CONSTRAINT DF_ITRN_TransactionType DEFAULT '  ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_ITRN_Sku DEFAULT ' ',
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_ITRN_StorerKey DEFAULT ' ',
Lot                      CHAR(10) NOT NULL
CONSTRAINT DF_ITRN_Lot DEFAULT ' ',
Loc                      CHAR(10) NOT NULL
CONSTRAINT DF_ITRN_Loc DEFAULT 'UNKNOWN',
ID                       CHAR(18) NOT NULL
CONSTRAINT DF_ITRN_ID DEFAULT ' ',
FromLoc                  CHAR(10) NOT NULL
CONSTRAINT DF_ITRN_FromLoc DEFAULT 'UNKNOWN',
ToLoc                    CHAR(10) NOT NULL
CONSTRAINT DF_ITRN_ToLoc DEFAULT 'UNKNOWN',
FromID                   CHAR(18) NOT NULL
CONSTRAINT DF_ITRN_FromID DEFAULT ' ',
ToID                     CHAR(18) NOT NULL
CONSTRAINT DF_ITRN_ToID DEFAULT ' ',
Qty                      NUMBER(10) NOT NULL
CONSTRAINT DF_ITRN_Qty DEFAULT 0,
UOM                      CHAR(10) NOT NULL
CONSTRAINT DF_ITRN_UOM DEFAULT ' ',
PackKey                  CHAR(10) NOT NULL
CONSTRAINT DF_ITRN_PackKey DEFAULT 'STD',
UOMQty                   NUMBER(10) NOT NULL
CONSTRAINT DF_ITRN_UOMQty DEFAULT 0,
Cost                     NUMBER(12,6) NOT NULL
CONSTRAINT DF_ITRN_Cost DEFAULT 0,
SourceKey                CHAR(10) NOT NULL
CONSTRAINT DF_ITRN_SourceKey DEFAULT ' ',
SourceType               CHAR(2) NOT NULL
CONSTRAINT DF_ITRN_SourceType DEFAULT ' ',
ReasonCode               CHAR(10) NOT NULL
CONSTRAINT DF_ITRN_ReasonCode DEFAULT ' ',
Lottable01              CHAR(18) NOT NULL
CONSTRAINT DF_ITRN_LOTTABLE01 DEFAULT ' ',
Lottable02              CHAR(18) NOT NULL
CONSTRAINT DF_ITRN_LOTTABLE02 DEFAULT ' ',
Lottable03              CHAR(18) NOT NULL
CONSTRAINT DF_ITRN_LOTTABLE03 DEFAULT ' ',
Lottable04              DATE NULL,
Lottable05              DATE NULL,
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_ITRN_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_ITRN_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_ITRN_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_ITRN_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_ITRN_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE ITRN ADD CONSTRAINT PK_ITRN PRIMARY KEY (ItrnKey);

-- 添加检查约束
ALTER TABLE ITRN ADD CONSTRAINT CK_ITRN_TransactionType 
CHECK (TransactionType IN ('AD', 'AL', 'AS', 'CC', 'MV', 'PK', 'RC', 'RF', 'SH', 'TR'));

-- 创建索引
CREATE INDEX IX_ITRN_Sku ON ITRN (Sku);
CREATE INDEX IX_ITRN_StorerKey ON ITRN (StorerKey);
CREATE INDEX IX_ITRN_Lot ON ITRN (Lot);
CREATE INDEX IX_ITRN_Loc ON ITRN (Loc);
CREATE INDEX IX_ITRN_EffectiveDate ON ITRN (EffectiveDate);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON ITRN TO nsql;

PROMPT '>>> 已创建表 ITRN (库存事务表)';

-- =============================================
-- SKU 表 (库存单位表) - 商品主数据表
-- =============================================
CREATE TABLE SKU
(StorerKey                CHAR(15) NOT NULL,
Sku                      CHAR(20) NOT NULL,
ManufacturerSku          CHAR(20) NOT NULL
CONSTRAINT DF_SKU_ManufacturerSku DEFAULT ' ',
RetailSku                CHAR(20) NOT NULL
CONSTRAINT DF_SKU_RetailSku DEFAULT ' ',
AltSku                   CHAR(20) NOT NULL
CONSTRAINT DF_SKU_AltSku DEFAULT ' ',
Descr                    VARCHAR2(60) NOT NULL
CONSTRAINT DF_SKU_Descr DEFAULT ' ',
SkuType                  CHAR(10) NOT NULL
CONSTRAINT DF_SKU_SkuType DEFAULT '0',
Class                    CHAR(10) NOT NULL
CONSTRAINT DF_SKU_Class DEFAULT ' ',
Commodity                CHAR(10) NOT NULL
CONSTRAINT DF_SKU_Commodity DEFAULT ' ',
HazMat                   CHAR(1) NOT NULL
CONSTRAINT DF_SKU_HazMat DEFAULT 'N',
HazMatID                 CHAR(18) NOT NULL
CONSTRAINT DF_SKU_HazMatID DEFAULT ' ',
HazMatClass              CHAR(10) NOT NULL
CONSTRAINT DF_SKU_HazMatClass DEFAULT ' ',
GrossWeight              NUMBER(12,6) NOT NULL
CONSTRAINT DF_SKU_GrossWeight DEFAULT 0,
NetWeight                NUMBER(12,6) NOT NULL
CONSTRAINT DF_SKU_NetWeight DEFAULT 0,
WeightUOM                CHAR(10) NOT NULL
CONSTRAINT DF_SKU_WeightUOM DEFAULT ' ',
Cube                     NUMBER(12,6) NOT NULL
CONSTRAINT DF_SKU_Cube DEFAULT 0,
CubeUOM                  CHAR(10) NOT NULL
CONSTRAINT DF_SKU_CubeUOM DEFAULT ' ',
QtyUOM                   CHAR(10) NOT NULL
CONSTRAINT DF_SKU_QtyUOM DEFAULT 'EA',
PackKey                  CHAR(10) NOT NULL
CONSTRAINT DF_SKU_PackKey DEFAULT 'STD',
RotationRule             CHAR(10) NOT NULL
CONSTRAINT DF_SKU_RotationRule DEFAULT 'FIFO',
MaxQty                   NUMBER(10) NOT NULL
CONSTRAINT DF_SKU_MaxQty DEFAULT 0,
MinQty                   NUMBER(10) NOT NULL
CONSTRAINT DF_SKU_MinQty DEFAULT 0,
ReorderQty               NUMBER(10) NOT NULL
CONSTRAINT DF_SKU_ReorderQty DEFAULT 0,
StandardCost             NUMBER(12,6) NOT NULL
CONSTRAINT DF_SKU_StandardCost DEFAULT 0,
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_SKU_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_SKU_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_SKU_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_SKU_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_SKU_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE SKU ADD CONSTRAINT PK_SKU PRIMARY KEY (StorerKey, Sku);

-- 添加检查约束
ALTER TABLE SKU ADD CONSTRAINT CK_SKU_HazMat CHECK (HazMat IN ('Y', 'N'));
ALTER TABLE SKU ADD CONSTRAINT CK_SKU_SkuType CHECK (SkuType IN ('0', '1', '2', '3', '4', '5'));

-- 创建索引
CREATE INDEX IX_SKU_Sku ON SKU (Sku);
CREATE INDEX IX_SKU_ManufacturerSku ON SKU (ManufacturerSku);
CREATE INDEX IX_SKU_RetailSku ON SKU (RetailSku);
CREATE INDEX IX_SKU_Class ON SKU (Class);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON SKU TO nsql;

PROMPT '>>> 已创建表 SKU (库存单位表)';

-- =============================================
-- LOT 表 (批次表) - 批次管理表
-- =============================================
CREATE TABLE LOT
(Lot                      CHAR(10) NOT NULL,
Sku                      CHAR(20) NOT NULL,
StorerKey                CHAR(15) NOT NULL,
Lottable01              CHAR(18) NOT NULL
CONSTRAINT DF_LOT_LOTTABLE01 DEFAULT ' ',
Lottable02              CHAR(18) NOT NULL
CONSTRAINT DF_LOT_LOTTABLE02 DEFAULT ' ',
Lottable03              CHAR(18) NOT NULL
CONSTRAINT DF_LOT_LOTTABLE03 DEFAULT ' ',
Lottable04              DATE NULL,
Lottable05              DATE NULL,
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_LOT_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_LOT_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_LOT_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_LOT_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_LOT_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE LOT ADD CONSTRAINT PK_LOT PRIMARY KEY (Lot, Sku, StorerKey);

-- 创建索引
CREATE INDEX IX_LOT_Lot ON LOT (Lot);
CREATE INDEX IX_LOT_Sku ON LOT (Sku);
CREATE INDEX IX_LOT_StorerKey ON LOT (StorerKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON LOT TO nsql;

PROMPT '>>> 已创建表 LOT (批次表)';

-- =============================================
-- LOTATTRIBUTE 表 (批次属性表) - 批次扩展信息表
-- =============================================
CREATE TABLE LOTATTRIBUTE
(StorerKey                CHAR(15) NOT NULL,
Sku                      CHAR(20) NOT NULL,
Lot                      CHAR(10) NOT NULL,
Lottable01              CHAR(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_LOTTABLE01 DEFAULT ' ',
Lottable02              CHAR(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_LOTTABLE02 DEFAULT ' ',
Lottable03              CHAR(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_LOTTABLE03 DEFAULT ' ',
Lottable04              DATE NULL,
Lottable05              DATE NULL,
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_LOTATTRIBUTE_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE LOTATTRIBUTE ADD CONSTRAINT PK_LOTATTRIBUTE PRIMARY KEY (StorerKey, Sku, Lot);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON LOTATTRIBUTE TO nsql;

PROMPT '>>> 已创建表 LOTATTRIBUTE (批次属性表)';

-- =============================================
-- LOC 表 (位置表) - 仓库位置表
-- =============================================
CREATE TABLE LOC
(Loc                      CHAR(10) NOT NULL,
LocationType             CHAR(10) NOT NULL
CONSTRAINT DF_LOC_LocationType DEFAULT '0',
PutawayZone              CHAR(10) NOT NULL
CONSTRAINT DF_LOC_PutawayZone DEFAULT ' ',
PickZone                 CHAR(10) NOT NULL
CONSTRAINT DF_LOC_PickZone DEFAULT ' ',
Area                     CHAR(10) NOT NULL
CONSTRAINT DF_LOC_Area DEFAULT ' ',
Aisle                    CHAR(10) NOT NULL
CONSTRAINT DF_LOC_Aisle DEFAULT ' ',
Bay                      CHAR(10) NOT NULL
CONSTRAINT DF_LOC_Bay DEFAULT ' ',
Tier                     CHAR(10) NOT NULL
CONSTRAINT DF_LOC_Tier DEFAULT ' ',
Position                 CHAR(10) NOT NULL
CONSTRAINT DF_LOC_Position DEFAULT ' ',
MaxQty                   NUMBER(10) NOT NULL
CONSTRAINT DF_LOC_MaxQty DEFAULT 0,
MaxWeight                NUMBER(12,6) NOT NULL
CONSTRAINT DF_LOC_MaxWeight DEFAULT 0,
MaxCube                  NUMBER(12,6) NOT NULL
CONSTRAINT DF_LOC_MaxCube DEFAULT 0,
MaxPallets               NUMBER(10) NOT NULL
CONSTRAINT DF_LOC_MaxPallets DEFAULT 0,
PickSequence             NUMBER(10) NOT NULL
CONSTRAINT DF_LOC_PickSequence DEFAULT 0,
PutawaySequence          NUMBER(10) NOT NULL
CONSTRAINT DF_LOC_PutawaySequence DEFAULT 0,
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_LOC_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_LOC_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_LOC_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_LOC_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_LOC_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE LOC ADD CONSTRAINT PK_LOC PRIMARY KEY (Loc);

-- 添加检查约束
ALTER TABLE LOC ADD CONSTRAINT CK_LOC_LocationType
CHECK (LocationType IN ('0', '1', '2', '3', '4', '5'));

-- 创建索引
CREATE INDEX IX_LOC_LocationType ON LOC (LocationType);
CREATE INDEX IX_LOC_PutawayZone ON LOC (PutawayZone);
CREATE INDEX IX_LOC_PickZone ON LOC (PickZone);
CREATE INDEX IX_LOC_Area ON LOC (Area);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON LOC TO nsql;

PROMPT '>>> 已创建表 LOC (位置表)';

-- =============================================
-- LOTxLOCxID 表 (批次位置标识关联表) - 实时库存表
-- =============================================
CREATE TABLE LOTxLOCxID
(Lot                      CHAR(10) NOT NULL,
Loc                      CHAR(10) NOT NULL,
ID                       CHAR(18) NOT NULL,
Sku                      CHAR(20) NOT NULL,
StorerKey                CHAR(15) NOT NULL,
Qty                      NUMBER(10) NOT NULL
CONSTRAINT DF_LOTxLOCxID_Qty DEFAULT 0,
QtyAllocated             NUMBER(10) NOT NULL
CONSTRAINT DF_LOTxLOCxID_QtyAllocated DEFAULT 0,
QtyPicked                NUMBER(10) NOT NULL
CONSTRAINT DF_LOTxLOCxID_QtyPicked DEFAULT 0,
PackKey                  CHAR(10) NOT NULL
CONSTRAINT DF_LOTxLOCxID_PackKey DEFAULT 'STD',
UOM                      CHAR(10) NOT NULL
CONSTRAINT DF_LOTxLOCxID_UOM DEFAULT 'EA',
RotationRule             CHAR(10) NOT NULL
CONSTRAINT DF_LOTxLOCxID_RotationRule DEFAULT 'FIFO',
MaxQty                   NUMBER(10) NOT NULL
CONSTRAINT DF_LOTxLOCxID_MaxQty DEFAULT 0,
MaxWeight                NUMBER(12,6) NOT NULL
CONSTRAINT DF_LOTxLOCxID_MaxWeight DEFAULT 0,
MaxCube                  NUMBER(12,6) NOT NULL
CONSTRAINT DF_LOTxLOCxID_MaxCube DEFAULT 0,
EffectiveDate           DATE NOT NULL
CONSTRAINT DF_LOTxLOCxID_EffectiveDate DEFAULT SYSDATE,
AddDate                 DATE NOT NULL
CONSTRAINT DF_LOTxLOCxID_AddDate DEFAULT SYSDATE,
AddWho                  CHAR(18) NOT NULL
CONSTRAINT DF_LOTxLOCxID_AddWho DEFAULT USER,
EditDate                DATE NOT NULL
CONSTRAINT DF_LOTxLOCxID_EditDate DEFAULT SYSDATE,
EditWho                 CHAR(18) NOT NULL
CONSTRAINT DF_LOTxLOCxID_EditWho DEFAULT USER,
TrafficCop              CHAR(1) NULL,
ArchiveCop              CHAR(1) NULL
);

-- 添加主键
ALTER TABLE LOTxLOCxID ADD CONSTRAINT PK_LOTxLOCxID PRIMARY KEY (Lot, Loc, ID);

-- 创建索引
CREATE INDEX IX_LOTxLOCxID_Sku ON LOTxLOCxID (Sku);
CREATE INDEX IX_LOTxLOCxID_StorerKey ON LOTxLOCxID (StorerKey);
CREATE INDEX IX_LOTxLOCxID_Loc ON LOTxLOCxID (Loc);
CREATE INDEX IX_LOTxLOCxID_Qty ON LOTxLOCxID (Qty);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON LOTxLOCxID TO nsql;

PROMPT '>>> 已创建表 LOTxLOCxID (批次位置标识关联表)';

-- 提交事务
COMMIT;

PROMPT '>>> 核心表结构脚本第一部分执行完成';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
