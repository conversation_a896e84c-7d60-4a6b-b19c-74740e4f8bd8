-- =============================================
-- Oracle版本 - 创建RF设备和任务管理存储过程脚本
-- 功能：创建仓库管理系统的RF设备和任务管理相关存储过程 (Oracle版本)
-- 用途：RF接收、RF拣选、任务分配、用户管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中提取的RF和任务管理存储过程转换为Oracle语法
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- RF接收存储过程
-- =============================================

-- nspRFReceiving 存储过程 (RF接收处理)
CREATE OR REPLACE PROCEDURE nspRFReceiving (
    p_receiptkey        IN CHAR,
    p_receiptlinenum    IN CHAR,
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_lot               IN CHAR,
    p_id                IN CHAR,
    p_loc               IN CHAR,
    p_qty               IN NUMBER,
    p_userkey           IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_packkey CHAR(10);
    v_expectedqty NUMBER;
    v_receivedqty NUMBER;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 验证收货明细是否存在
    BEGIN
        SELECT Qty, QtyReceived INTO v_expectedqty, v_receivedqty
        FROM RECEIPTDETAIL
        WHERE ReceiptKey = p_receiptkey 
        AND ReceiptLineNumber = p_receiptlinenum
        AND StorerKey = p_storerkey
        AND Sku = p_sku;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 75001;
            p_errmsg := 'NSQL75001: Receipt detail not found';
            RETURN;
    END;
    
    -- 检查接收数量是否超过预期
    IF (v_receivedqty + p_qty) > v_expectedqty THEN
        p_success := 0;
        p_err := 75002;
        p_errmsg := 'NSQL75002: Received quantity exceeds expected quantity';
        RETURN;
    END IF;
    
    -- 获取包装信息
    nspGetPack(p_storerkey, p_sku, p_lot, p_loc, p_id, v_packkey, p_success, p_err, p_errmsg);
    
    IF p_success = 1 THEN
        -- 更新收货明细
        UPDATE RECEIPTDETAIL
        SET QtyReceived = QtyReceived + p_qty,
            Status = CASE WHEN QtyReceived + p_qty >= Qty THEN '9' ELSE Status END,
            EditDate = v_currentdatetime,
            EditWho = v_currentuser
        WHERE ReceiptKey = p_receiptkey 
        AND ReceiptLineNumber = p_receiptlinenum;
        
        -- 执行入库事务
        nspItrnAddDeposit(
            NULL, p_storerkey, p_sku, p_lot, p_loc, p_id, '',
            '', '', '', NULL, NULL,
            0, 0, p_qty, 0, 0, 0, 0,
            v_packkey, 'EA', 'nspRFReceiving', p_receiptkey,
            p_success, p_err, p_errmsg
        );
        
        IF p_success = 1 THEN
            -- 记录RF日志
            INSERT INTO RFDB_LOG (
                adddate, addwho, rffunction, userkey, loc, sku, lot, id, qty,
                fromloc, toloc, fromid, toid, trantype, reference
            ) VALUES (
                v_currentdatetime, v_currentuser, 'RECEIVING', p_userkey, 
                p_loc, p_sku, p_lot, p_id, p_qty,
                'DOCK', p_loc, ' ', p_id, 'AD', 'RF_RECEIVING'
            );
            
            COMMIT;
        ELSE
            ROLLBACK;
        END IF;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspRFReceiving');
        ROLLBACK;
END nspRFReceiving;
/

-- nspRFPicking 存储过程 (RF拣选处理)
CREATE OR REPLACE PROCEDURE nspRFPicking (
    p_pickheaderkey     IN CHAR,
    p_pickdetailkey     IN CHAR,
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_lot               IN CHAR,
    p_id                IN CHAR,
    p_fromloc           IN CHAR,
    p_toloc             IN CHAR,
    p_qty               IN NUMBER,
    p_userkey           IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_packkey CHAR(10);
    v_requestedqty NUMBER;
    v_pickedqty NUMBER;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 验证拣选明细是否存在
    BEGIN
        SELECT Qty, QtyPicked INTO v_requestedqty, v_pickedqty
        FROM PICKDETAIL
        WHERE PickHeaderKey = p_pickheaderkey 
        AND PickDetailKey = p_pickdetailkey
        AND StorerKey = p_storerkey
        AND Sku = p_sku;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 76001;
            p_errmsg := 'NSQL76001: Pick detail not found';
            RETURN;
    END;
    
    -- 检查拣选数量是否超过请求
    IF (v_pickedqty + p_qty) > v_requestedqty THEN
        p_success := 0;
        p_err := 76002;
        p_errmsg := 'NSQL76002: Picked quantity exceeds requested quantity';
        RETURN;
    END IF;
    
    -- 获取包装信息
    nspGetPack(p_storerkey, p_sku, p_lot, p_fromloc, p_id, v_packkey, p_success, p_err, p_errmsg);
    
    IF p_success = 1 THEN
        -- 确认拣选
        nspPickConfirm(p_pickheaderkey, p_pickdetailkey, p_qty, p_userkey, p_success, p_err, p_errmsg);
        
        IF p_success = 1 THEN
            -- 如果指定了目标位置，执行移动
            IF TRIM(p_toloc) IS NOT NULL AND p_toloc != p_fromloc THEN
                nspItrnAddMove(
                    NULL, NULL, p_storerkey, p_sku, p_lot,
                    p_fromloc, p_id, p_toloc, p_id, '',
                    '', '', '', NULL, NULL,
                    0, 0, p_qty, 0, 0, 0, 0,
                    v_packkey, 'EA', 'nspRFPicking', p_pickheaderkey,
                    p_success, p_err, p_errmsg
                );
            END IF;
            
            IF p_success = 1 THEN
                -- 记录RF日志
                INSERT INTO RFDB_LOG (
                    adddate, addwho, rffunction, userkey, loc, sku, lot, id, qty,
                    fromloc, toloc, fromid, toid, trantype, reference
                ) VALUES (
                    v_currentdatetime, v_currentuser, 'PICKING', p_userkey, 
                    p_fromloc, p_sku, p_lot, p_id, p_qty,
                    p_fromloc, NVL(p_toloc, p_fromloc), p_id, p_id, 'AW', 'RF_PICKING'
                );
                
                COMMIT;
            ELSE
                ROLLBACK;
            END IF;
        ELSE
            ROLLBACK;
        END IF;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspRFPicking');
        ROLLBACK;
END nspRFPicking;
/

-- nspRFCycleCount 存储过程 (RF循环盘点)
CREATE OR REPLACE PROCEDURE nspRFCycleCount (
    p_cckey             IN CHAR,
    p_ccdetailkey       IN CHAR,
    p_storerkey         IN CHAR,
    p_sku               IN CHAR,
    p_lot               IN CHAR,
    p_id                IN CHAR,
    p_loc               IN CHAR,
    p_qtyactual         IN NUMBER,
    p_userkey           IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_qtyexpected NUMBER;
    v_variance NUMBER;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 验证循环盘点明细是否存在
    BEGIN
        SELECT QtyExpected INTO v_qtyexpected
        FROM CCDETAIL
        WHERE CCKey = p_cckey 
        AND CCDetailKey = p_ccdetailkey
        AND StorerKey = p_storerkey
        AND Sku = p_sku;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 77001;
            p_errmsg := 'NSQL77001: Cycle count detail not found';
            RETURN;
    END;
    
    -- 计算差异
    v_variance := p_qtyactual - v_qtyexpected;
    
    -- 更新循环盘点明细
    UPDATE CCDETAIL
    SET QtyActual = p_qtyactual,
        Variance = v_variance,
        CountUser = p_userkey,
        CountDate = v_currentdatetime,
        EditDate = v_currentdatetime,
        EditWho = v_currentuser
    WHERE CCKey = p_cckey AND CCDetailKey = p_ccdetailkey;
    
    -- 如果有差异，创建调整事务
    IF v_variance != 0 THEN
        nspItrnAddAdjustment(
            NULL, p_storerkey, p_sku, p_lot, p_loc, p_id, '',
            '', '', '', NULL, NULL,
            0, 0, v_variance, 0, 0, 0, 0,
            'STD', 'EA', 'nspRFCycleCount', p_cckey,
            p_success, p_err, p_errmsg
        );
    END IF;
    
    IF p_success = 1 THEN
        -- 记录RF日志
        INSERT INTO RFDB_LOG (
            adddate, addwho, rffunction, userkey, loc, sku, lot, id, qty,
            fromloc, toloc, fromid, toid, trantype, reference
        ) VALUES (
            v_currentdatetime, v_currentuser, 'CYCLECOUNT', p_userkey, 
            p_loc, p_sku, p_lot, p_id, p_qtyactual,
            p_loc, p_loc, p_id, p_id, 'CC', 'RF_CYCLECOUNT'
        );
        
        COMMIT;
    ELSE
        ROLLBACK;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspRFCycleCount');
        ROLLBACK;
END nspRFCycleCount;
/

-- =============================================
-- 任务管理存储过程
-- =============================================

-- nspTaskManagerGetNextTask 存储过程 (获取下一个任务)
CREATE OR REPLACE PROCEDURE nspTaskManagerGetNextTask (
    p_userkey           IN CHAR,
    p_tasktype          OUT CHAR,
    p_taskkey           OUT CHAR,
    p_priority          OUT CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_strategykey CHAR(10);
    v_ttmstrategykey CHAR(10);
    v_prioritytasktype CHAR(10);
    v_found NUMBER := 0;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_tasktype := ' ';
    p_taskkey := ' ';
    p_priority := ' ';

    -- 获取用户的任务管理配置
    BEGIN
        SELECT StrategyKey, TTMStrategyKey, PriorityTaskType
        INTO v_strategykey, v_ttmstrategykey, v_prioritytasktype
        FROM TaskManagerUser
        WHERE UserKey = p_userkey;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 78001;
            p_errmsg := 'NSQL78001: User not configured for task management';
            RETURN;
    END;

    -- 首先查找优先任务类型
    IF TRIM(v_prioritytasktype) IS NOT NULL THEN
        -- 查找拣选任务
        IF v_prioritytasktype = 'PICK' THEN
            BEGIN
                SELECT 'PICK', PickHeaderKey, Priority
                INTO p_tasktype, p_taskkey, p_priority
                FROM PICKHEADER
                WHERE Status = '0' -- 待处理
                AND AssignedUser IS NULL
                ORDER BY Priority DESC, AddDate
                FETCH FIRST 1 ROWS ONLY;

                v_found := 1;

                -- 分配任务给用户
                UPDATE PICKHEADER
                SET AssignedUser = p_userkey,
                    AssignedDate = SYSDATE,
                    Status = '1' -- 进行中
                WHERE PickHeaderKey = p_taskkey;

            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    v_found := 0;
            END;
        END IF;

        -- 查找补货任务
        IF v_found = 0 AND v_prioritytasktype = 'REPLENISHMENT' THEN
            BEGIN
                SELECT 'REPLENISHMENT', ReplenishmentKey, Priority
                INTO p_tasktype, p_taskkey, p_priority
                FROM REPLENISHMENT
                WHERE Status = '0' -- 待处理
                AND AssignedUser IS NULL
                ORDER BY Priority DESC, AddDate
                FETCH FIRST 1 ROWS ONLY;

                v_found := 1;

                -- 分配任务给用户
                UPDATE REPLENISHMENT
                SET AssignedUser = p_userkey,
                    AssignedDate = SYSDATE,
                    Status = '1' -- 进行中
                WHERE ReplenishmentKey = p_taskkey;

            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    v_found := 0;
            END;
        END IF;

        -- 查找上架任务
        IF v_found = 0 AND v_prioritytasktype = 'PUTAWAY' THEN
            BEGIN
                SELECT 'PUTAWAY', StorerKey || '|' || Sku || '|' || Lot || '|' || Id, '5'
                INTO p_tasktype, p_taskkey, p_priority
                FROM RFPUTAWAY
                WHERE Status = '0' -- 待处理
                AND UserKey IS NULL
                ORDER BY AddDate
                FETCH FIRST 1 ROWS ONLY;

                v_found := 1;

                -- 分配任务给用户
                UPDATE RFPUTAWAY
                SET UserKey = p_userkey,
                    Status = '1' -- 进行中
                WHERE StorerKey || '|' || Sku || '|' || Lot || '|' || Id = p_taskkey;

            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    v_found := 0;
            END;
        END IF;
    END IF;

    -- 如果没有找到优先任务，按策略查找其他任务
    IF v_found = 0 THEN
        -- 根据TTM策略查找任务 (简化版本)
        BEGIN
            SELECT 'PICK', PickHeaderKey, Priority
            INTO p_tasktype, p_taskkey, p_priority
            FROM PICKHEADER
            WHERE Status = '0'
            AND AssignedUser IS NULL
            ORDER BY Priority DESC, AddDate
            FETCH FIRST 1 ROWS ONLY;

            v_found := 1;

            -- 分配任务给用户
            UPDATE PICKHEADER
            SET AssignedUser = p_userkey,
                AssignedDate = SYSDATE,
                Status = '1'
            WHERE PickHeaderKey = p_taskkey;

        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                p_success := 0;
                p_err := 78002;
                p_errmsg := 'NSQL78002: No tasks available for user';
        END;
    END IF;

    IF p_success = 1 THEN
        COMMIT;
    ELSE
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspTaskManagerGetNextTask');
        ROLLBACK;
END nspTaskManagerGetNextTask;
/

-- nspTaskManagerCompleteTask 存储过程 (完成任务)
CREATE OR REPLACE PROCEDURE nspTaskManagerCompleteTask (
    p_userkey           IN CHAR,
    p_tasktype          IN CHAR,
    p_taskkey           IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 根据任务类型完成任务
    CASE p_tasktype
        WHEN 'PICK' THEN
            UPDATE PICKHEADER
            SET Status = '9', -- 完成
                CompletedDate = v_currentdatetime,
                CompletedUser = p_userkey,
                EditDate = v_currentdatetime,
                EditWho = v_currentuser
            WHERE PickHeaderKey = p_taskkey
            AND AssignedUser = p_userkey;

        WHEN 'REPLENISHMENT' THEN
            UPDATE REPLENISHMENT
            SET Status = '9', -- 完成
                CompletedDate = v_currentdatetime,
                CompletedUser = p_userkey,
                EditDate = v_currentdatetime,
                EditWho = v_currentuser
            WHERE ReplenishmentKey = p_taskkey
            AND AssignedUser = p_userkey;

        WHEN 'PUTAWAY' THEN
            UPDATE RFPUTAWAY
            SET Status = '9', -- 完成
                EditDate = v_currentdatetime,
                EditWho = v_currentuser
            WHERE StorerKey || '|' || Sku || '|' || Lot || '|' || Id = p_taskkey
            AND UserKey = p_userkey;

        ELSE
            p_success := 0;
            p_err := 78003;
            p_errmsg := 'NSQL78003: Unknown task type';
    END CASE;

    IF SQL%ROWCOUNT = 0 THEN
        p_success := 0;
        p_err := 78004;
        p_errmsg := 'NSQL78004: Task not found or not assigned to user';
    END IF;

    IF p_success = 1 THEN
        COMMIT;
    ELSE
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspTaskManagerCompleteTask');
        ROLLBACK;
END nspTaskManagerCompleteTask;
/

-- nspTaskManagerSkipTask 存储过程 (跳过任务)
CREATE OR REPLACE PROCEDURE nspTaskManagerSkipTask (
    p_userkey           IN CHAR,
    p_tasktype          IN CHAR,
    p_taskkey           IN CHAR,
    p_skipreason        IN CHAR,
    p_success           OUT NUMBER,
    p_err               OUT NUMBER,
    p_errmsg            OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentdatetime DATE := SYSDATE;
    v_currentuser CHAR(18) := USER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 记录跳过任务的原因
    INSERT INTO TaskManagerSkipTasks (
        UserKey, TaskType, SkipReason,
        AddDate, AddWho, EditDate, EditWho
    ) VALUES (
        p_userkey, p_tasktype, p_skipreason,
        v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
    );

    -- 根据任务类型释放任务
    CASE p_tasktype
        WHEN 'PICK' THEN
            UPDATE PICKHEADER
            SET AssignedUser = NULL,
                AssignedDate = NULL,
                Status = '0', -- 重新设为待处理
                EditDate = v_currentdatetime,
                EditWho = v_currentuser
            WHERE PickHeaderKey = p_taskkey
            AND AssignedUser = p_userkey;

        WHEN 'REPLENISHMENT' THEN
            UPDATE REPLENISHMENT
            SET AssignedUser = NULL,
                AssignedDate = NULL,
                Status = '0', -- 重新设为待处理
                EditDate = v_currentdatetime,
                EditWho = v_currentuser
            WHERE ReplenishmentKey = p_taskkey
            AND AssignedUser = p_userkey;

        WHEN 'PUTAWAY' THEN
            UPDATE RFPUTAWAY
            SET UserKey = NULL,
                Status = '0', -- 重新设为待处理
                EditDate = v_currentdatetime,
                EditWho = v_currentuser
            WHERE StorerKey || '|' || Sku || '|' || Lot || '|' || Id = p_taskkey
            AND UserKey = p_userkey;

        ELSE
            p_success := 0;
            p_err := 78005;
            p_errmsg := 'NSQL78005: Unknown task type';
    END CASE;

    IF SQL%ROWCOUNT = 0 THEN
        p_success := 0;
        p_err := 78006;
        p_errmsg := 'NSQL78006: Task not found or not assigned to user';
    END IF;

    IF p_success = 1 THEN
        COMMIT;
    ELSE
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspTaskManagerSkipTask');
        ROLLBACK;
END nspTaskManagerSkipTask;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle RF设备和任务管理存储过程脚本执行完成';
PROMPT '>>> 已创建完整的RF设备和任务管理存储过程集合 (6个)';
PROMPT '>>> 包含：RF接收、RF拣选、RF盘点、任务分配、任务完成、任务跳过等功能';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - RF设备和任务管理存储过程创建完成！';
PROMPT '总RF和任务存储过程数量：6个专业存储过程';
PROMPT '功能覆盖：';
PROMPT '- RF接收处理';
PROMPT '- RF拣选处理';
PROMPT '- RF循环盘点';
PROMPT '- 任务分配管理';
PROMPT '- 任务完成处理';
PROMPT '- 任务跳过管理';
PROMPT '=============================================';
