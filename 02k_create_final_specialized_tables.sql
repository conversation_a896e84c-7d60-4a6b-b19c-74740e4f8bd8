-- =============================================
-- 创建最终专业化表结构脚本 (第十一部分)
-- 功能：创建仓库管理系统中最终专业化表结构
-- 用途：RF设备、帮助系统、物料清单、提单管理等
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- RF设备和日志表
-- =============================================

-- RFDB_LOG 表 (RF数据库日志表)
CREATE TABLE dbo.RFDB_LOG (
adddate datetime NOT NULL CONSTRAINT DF_RFDB_LOG_adddate_1__15 DEFAULT (getdate()),
addwho char(18) NOT NULL CONSTRAINT DF_RFDB_LOG_addwho_2__15 DEFAULT (user),
rffunction char(30) NOT NULL CONSTRAINT DF_RFDB_LOG_rffunction_3__15 DEFAULT (''),
userkey char(18) NOT NULL CONSTRAINT DF_RFDB_LOG_userkey_4__15 DEFAULT (''),
loc char(10) NOT NULL CONSTRAINT DF_RFDB_LOG_loc_5__15 DEFAULT (''),
sku char(20) NOT NULL CONSTRAINT DF_RFDB_LOG_sku_6__15 DEFAULT (''),
lot char(10) NOT NULL CONSTRAINT DF_RFDB_LOG_lot_7__15 DEFAULT (''),
id char(18) NOT NULL CONSTRAINT DF_RFDB_LOG_id_8__15 DEFAULT (''),
qty int NOT NULL CONSTRAINT DF_RFDB_LOG_qty_9__15 DEFAULT (0),
fromloc char(10) NOT NULL CONSTRAINT DF_RFDB_LOG_fromloc_10__15 DEFAULT (''),
toloc char(10) NOT NULL CONSTRAINT DF_RFDB_LOG_toloc_11__15 DEFAULT (''),
fromid char(18) NOT NULL CONSTRAINT DF_RFDB_LOG_fromid_12__15 DEFAULT (''),
toid char(18) NOT NULL CONSTRAINT DF_RFDB_LOG_toid_13__15 DEFAULT (''),
trantype char(2) NOT NULL CONSTRAINT DF_RFDB_LOG_trantype_14__15 DEFAULT (''),
reference char(30) NOT NULL CONSTRAINT DF_RFDB_LOG_reference_15__15 DEFAULT ('')
)
GO

IF OBJECT_ID('RFDB_LOG') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE RFDB_LOG FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE RFDB_LOG>>>'
GRANT INSERT ON RFDB_LOG TO nsql
GRANT UPDATE ON RFDB_LOG TO nsql
GRANT DELETE ON RFDB_LOG TO nsql
GRANT SELECT ON RFDB_LOG TO nsql
END
GO

-- RFPUTAWAY 表 (RF上架表)
CREATE TABLE RFPUTAWAY (
StorerKey                char(15) NOT NULL,
Sku                      char(20) NOT NULL,
Lot                      char(10) NOT NULL,
Id                       char(18) NOT NULL,
FromLoc                  char(10) NOT NULL,
ToLoc                    char(10) NOT NULL,
Qty                      int NOT NULL
CONSTRAINT DF_RFPUTAWAY_Qty DEFAULT 0,
Status                   char(10) NOT NULL
CONSTRAINT DF_RFPUTAWAY_Status DEFAULT "0",
UserKey                  char(18) NOT NULL
CONSTRAINT DF_RFPUTAWAY_UserKey DEFAULT "",
AddDate                  datetime NOT NULL
CONSTRAINT DF_RFPUTAWAY_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_RFPUTAWAY_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_RFPUTAWAY_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_RFPUTAWAY_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('RFPUTAWAY') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE RFPUTAWAY FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE RFPUTAWAY>>>'
GRANT INSERT ON RFPUTAWAY TO nsql
GRANT UPDATE ON RFPUTAWAY TO nsql
GRANT DELETE ON RFPUTAWAY TO nsql
GRANT SELECT ON RFPUTAWAY TO nsql
END
GO

-- =============================================
-- 帮助和文档表
-- =============================================

-- help 表 (帮助表)
CREATE TABLE help
(topic            char(64)                NOT NULL
CONSTRAINT DF_help_topic DEFAULT "",
subtopic         char(64)                NOT NULL
CONSTRAINT DF_help_subtopic DEFAULT "",
info             text                    NULL,
AddDate          datetime                NOT NULL
CONSTRAINT DF_help_AddDate DEFAULT CURRENT_Timestamp,
AddWho           char(18)                NOT NULL
CONSTRAINT DF_help_AddWho DEFAULT USER ,
EditDate         datetime                NOT NULL
CONSTRAINT DF_help_EditDate DEFAULT CURRENT_Timestamp,
EditWho          char(18)                NOT NULL
CONSTRAINT DF_help_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('help') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE help FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE help>>>'
GRANT INSERT ON help TO nsql
GRANT UPDATE ON help TO nsql
GRANT DELETE ON help TO nsql
GRANT SELECT ON help TO nsql
END
GO

-- =============================================
-- 物料清单表
-- =============================================

-- BillOfMaterial 表 (物料清单表)
CREATE TABLE BillOfMaterial
(Storerkey     char(15)                NOT NULL
CONSTRAINT DF_BillOfMaterial_Storerkey DEFAULT "",
ParentSku      char(20)                NOT NULL
CONSTRAINT DF_BillOfMaterial_ParentSku DEFAULT "",
ComponentSku   char(20)                NOT NULL
CONSTRAINT DF_BillOfMaterial_ComponentSku DEFAULT "",
QtyRequired    int                     NOT NULL
CONSTRAINT DF_BillOfMaterial_QtyRequired DEFAULT 0,
UOM            char(10)                NOT NULL
CONSTRAINT DF_BillOfMaterial_UOM DEFAULT "",
EffectiveDate  datetime                NOT NULL
CONSTRAINT DF_BillOfMaterial_EffectiveDate DEFAULT CURRENT_Timestamp,
ExpirationDate datetime                NOT NULL
CONSTRAINT DF_BillOfMaterial_ExpirationDate DEFAULT 'JAN 01 2100',
AddDate        datetime                NOT NULL
CONSTRAINT DF_BillOfMaterial_AddDate DEFAULT CURRENT_Timestamp,
AddWho         char(18)                NOT NULL
CONSTRAINT DF_BillOfMaterial_AddWho DEFAULT USER ,
EditDate       datetime                NOT NULL
CONSTRAINT DF_BillOfMaterial_EditDate DEFAULT CURRENT_Timestamp,
EditWho        char(18)                NOT NULL
CONSTRAINT DF_BillOfMaterial_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('BillOfMaterial') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE BillOfMaterial FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE BillOfMaterial>>>'
GRANT INSERT ON BillOfMaterial TO nsql
GRANT UPDATE ON BillOfMaterial TO nsql
GRANT DELETE ON BillOfMaterial TO nsql
GRANT SELECT ON BillOfMaterial TO nsql
END
GO

-- =============================================
-- 投放区域管理表
-- =============================================

-- Dropid 表 (投放ID表)
CREATE TABLE Dropid
(Dropid        char(18)                NOT NULL
CONSTRAINT DF_Dropid_Dropid DEFAULT "",
Descr          char(60)                NOT NULL
CONSTRAINT DF_Dropid_Descr DEFAULT "",
DropZone       char(10)                NOT NULL
CONSTRAINT DF_Dropid_DropZone DEFAULT "",
Status         char(10)                NOT NULL
CONSTRAINT DF_Dropid_Status DEFAULT "0",
AddDate        datetime                NOT NULL
CONSTRAINT DF_Dropid_AddDate DEFAULT CURRENT_Timestamp,
AddWho         char(18)                NOT NULL
CONSTRAINT DF_Dropid_AddWho DEFAULT USER ,
EditDate       datetime                NOT NULL
CONSTRAINT DF_Dropid_EditDate DEFAULT CURRENT_Timestamp,
EditWho        char(18)                NOT NULL
CONSTRAINT DF_Dropid_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('Dropid') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE Dropid FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE Dropid>>>'
GRANT INSERT ON Dropid TO nsql
GRANT UPDATE ON Dropid TO nsql
GRANT DELETE ON Dropid TO nsql
GRANT SELECT ON Dropid TO nsql
END
GO

-- DropidDetail 表 (投放ID明细表)
CREATE TABLE DropidDetail
(Dropid        char(18)                NOT NULL,
DropidLineNumber char(5)               NOT NULL,
CaseId         char(10)                NOT NULL
CONSTRAINT DF_DropidDetail_CaseId DEFAULT "",
OrderKey       char(10)                NOT NULL
CONSTRAINT DF_DropidDetail_OrderKey DEFAULT "",
AddDate        datetime                NOT NULL
CONSTRAINT DF_DropidDetail_AddDate DEFAULT CURRENT_Timestamp,
AddWho         char(18)                NOT NULL
CONSTRAINT DF_DropidDetail_AddWho DEFAULT USER ,
EditDate       datetime                NOT NULL
CONSTRAINT DF_DropidDetail_EditDate DEFAULT CURRENT_Timestamp,
EditWho        char(18)                NOT NULL
CONSTRAINT DF_DropidDetail_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('DropidDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE DropidDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE DropidDetail>>>'
GRANT INSERT ON DropidDetail TO nsql
GRANT UPDATE ON DropidDetail TO nsql
GRANT DELETE ON DropidDetail TO nsql
GRANT SELECT ON DropidDetail TO nsql
END
GO

-- =============================================
-- 任务管理扩展表
-- =============================================

-- TaskManagerSkipTasks 表 (任务管理跳过任务表)
CREATE TABLE TaskManagerSkipTasks
(
UserKey        char(18)                NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_UserKey DEFAULT "",
TaskType       char(10)                NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_TaskType DEFAULT "",
SkipReason     char(60)                NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_SkipReason DEFAULT "",
AddDate        datetime                NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_AddDate DEFAULT CURRENT_Timestamp,
AddWho         char(18)                NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_AddWho DEFAULT USER ,
EditDate       datetime                NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_EditDate DEFAULT CURRENT_Timestamp,
EditWho        char(18)                NOT NULL
CONSTRAINT DF_TaskManagerSkipTasks_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('TaskManagerSkipTasks') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TaskManagerSkipTasks FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TaskManagerSkipTasks>>>'
GRANT INSERT ON TaskManagerSkipTasks TO nsql
GRANT UPDATE ON TaskManagerSkipTasks TO nsql
GRANT DELETE ON TaskManagerSkipTasks TO nsql
GRANT SELECT ON TaskManagerSkipTasks TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 提单管理表
-- =============================================

-- BOL 表 (提单表)
CREATE TABLE BOL
( BolKey                        char(10)  NOT NULL ,
Status                         char(10)
CONSTRAINT DF_BOL_Status DEFAULT "0"
CONSTRAINT CK_BOL_Status CHECK (Status IN ("0", "9")),
ExternBolKey                   char(30) NOT NULL
CONSTRAINT DF_BOL_XBolKey DEFAULT ""     ,
BolDate                        DateTime NOT NULL
CONSTRAINT DF_BOL_BolDate DEFAULT CURRENT_TIMESTAMP ,
CarrierKey                     char(10) NOT NULL
CONSTRAINT DF_BOL_CarrierKey DEFAULT " " ,
EffectiveDate                  datetime     NOT NULL
CONSTRAINT DF_BOL_EffDate DEFAULT CURRENT_TIMESTAMP ,
AddDate                        datetime NOT NULL
CONSTRAINT DF_BOL_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho                         char(18) NOT NULL
CONSTRAINT DF_BOL_AddWho DEFAULT USER ,
EditDate                       datetime NOT NULL
CONSTRAINT DF_BOL_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho                        char(18) NOT NULL
CONSTRAINT DF_BOL_EditWho DEFAULT USER ,
TrafficCop                     char(1)  NULL,
ArchiveCop                     char(1)  NULL,
TimeStamp                      char(18) NULL
)
GO

IF OBJECT_ID('BOL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE BOL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE BOL>>>'
GRANT INSERT ON BOL TO nsql
GRANT UPDATE ON BOL TO nsql
GRANT DELETE ON BOL TO nsql
GRANT SELECT ON BOL TO nsql
END
GO

-- BOLDETAIL 表 (提单明细表)
CREATE TABLE BOLDETAIL
( BolKey         char (10) NOT NULL ,
BolLineNumber  char (5) NOT NULL ,
OrderKey       char (10) NOT NULL
CONSTRAINT DF_BOLD_OrderKey DEFAULT " " ,
PalletKey      char (10) NOT NULL
CONSTRAINT DF_BOLD_PalletKey DEFAULT " " ,
Description    char (30) NOT NULL
CONSTRAINT DF_BOLD_Descript DEFAULT " " ,
AddDate        datetime NOT NULL
CONSTRAINT DF_BOLD_AddDate DEFAULT CURRENT_TIMESTAMP ,
AddWho         char(18) NOT NULL
CONSTRAINT DF_BOLD_AddWho DEFAULT USER ,
EditDate       datetime NOT NULL
CONSTRAINT DF_BOLD_EditDate DEFAULT CURRENT_TIMESTAMP ,
EditWho        char(18) NOT NULL
CONSTRAINT DF_BOLD_EditWho DEFAULT USER ,
TrafficCop     char(1)  NULL,
ArchiveCop     char(1)  NULL,
TimeStamp      char(18) NULL
)
GO

IF OBJECT_ID('BOLDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE BOLDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE BOLDETAIL>>>'
GRANT INSERT ON BOLDETAIL TO nsql
GRANT UPDATE ON BOLDETAIL TO nsql
GRANT DELETE ON BOLDETAIL TO nsql
GRANT SELECT ON BOLDETAIL TO nsql
END
GO

-- =============================================
-- 批次ID明细表
-- =============================================

-- LotxIdDetail 表 (批次ID明细表)
CREATE TABLE LotxIdDetail (
LotxIdDetailKey     char(10) NOT NULL ,
StorerKey           char(15) NOT NULL
CONSTRAINT DF_LotxIdDetail_StorerKey DEFAULT "",
Sku                 char(20) NOT NULL
CONSTRAINT DF_LotxIdDetail_Sku DEFAULT "",
Lot                 char(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_Lot DEFAULT "",
Id                  char(18) NOT NULL
CONSTRAINT DF_LotxIdDetail_Id DEFAULT "",
Loc                 char(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_Loc DEFAULT "",
Qty                 int NOT NULL
CONSTRAINT DF_LotxIdDetail_Qty DEFAULT 0,
QtyAllocated        int NOT NULL
CONSTRAINT DF_LotxIdDetail_QtyAllocated DEFAULT 0,
QtyPicked           int NOT NULL
CONSTRAINT DF_LotxIdDetail_QtyPicked DEFAULT 0,
PackKey             char(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_PackKey DEFAULT "STD",
UOM                 char(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_UOM DEFAULT "",
RotationRule        char(10) NOT NULL
CONSTRAINT DF_LotxIdDetail_RotationRule DEFAULT "",
MaxQty              int NOT NULL
CONSTRAINT DF_LotxIdDetail_MaxQty DEFAULT 0,
MaxWeight           float NOT NULL
CONSTRAINT DF_LotxIdDetail_MaxWeight DEFAULT 0,
MaxCube             float NOT NULL
CONSTRAINT DF_LotxIdDetail_MaxCube DEFAULT 0,
EffectiveDate       datetime NOT NULL
CONSTRAINT DF_LotxIdDetail_EffectiveDate DEFAULT CURRENT_TIMESTAMP ,
AddDate             datetime NOT NULL
CONSTRAINT DF_LotxIdDetail_AddDate DEFAULT CURRENT_Timestamp ,
AddWho              char(18) NOT NULL
CONSTRAINT DF_LotxIdDetail_AddWho DEFAULT USER ,
EditDate            datetime NOT NULL
CONSTRAINT DF_LotxIdDetail_EditDate DEFAULT CURRENT_Timestamp ,
EditWho             char(18) NOT NULL
CONSTRAINT DF_LotxIdDetail_EditWho DEFAULT USER ,
TrafficCop          char(1)  NULL,
ArchiveCop          char(1)  NULL
)
GO

IF OBJECT_ID('LotxIdDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE LotxIdDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE LotxIdDetail>>>'
GRANT INSERT ON LotxIdDetail TO nsql
GRANT UPDATE ON LotxIdDetail TO nsql
GRANT DELETE ON LotxIdDetail TO nsql
GRANT SELECT ON LotxIdDetail TO nsql
END
GO

-- =============================================
-- 补货锁定表
-- =============================================

-- REPLENISHMENT_LOCK 表 (补货锁定表)
CREATE TABLE REPLENISHMENT_LOCK
(
StorerKey                char(15) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_StorerKey DEFAULT "",
Sku                      char(20) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_Sku DEFAULT "",
FromLoc                  char(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_FromLoc DEFAULT "",
ToLoc                    char(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_ToLoc DEFAULT "",
LockType                 char(10) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_LockType DEFAULT "",
LockReason               char(60) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_LockReason DEFAULT "",
AddDate                  datetime NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_REPLENISHMENT_LOCK_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('REPLENISHMENT_LOCK') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE REPLENISHMENT_LOCK FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE REPLENISHMENT_LOCK>>>'
GRANT INSERT ON REPLENISHMENT_LOCK TO nsql
GRANT UPDATE ON REPLENISHMENT_LOCK TO nsql
GRANT DELETE ON REPLENISHMENT_LOCK TO nsql
GRANT SELECT ON REPLENISHMENT_LOCK TO nsql
END
GO

-- 注意：此脚本包含最终专业化表结构
-- 完整的表结构请参考原始 NEPISQL.sql 文件
