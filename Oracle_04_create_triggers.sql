-- =============================================
-- Oracle版本 - 创建触发器脚本
-- 功能：创建仓库管理系统的触发器 (Oracle版本)
-- 用途：数据完整性、审计跟踪、自动更新等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：为关键表创建触发器以确保数据一致性和业务规则
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 库存事务触发器
-- =============================================

-- ITRN表的审计触发器
CREATE OR REPLACE TRIGGER TRG_ITRN_AUDIT
BEFORE INSERT OR UPDATE ON ITRN
FOR EACH ROW
BEGIN
    -- 设置审计字段
    IF INSERTING THEN
        :NEW.AddDate := SYSDATE;
        :NEW.AddWho := USER;
        :NEW.EditDate := SYSDATE;
        :NEW.EditWho := USER;
        
        -- 生成事务键如果为空
        IF :NEW.ItrnKey IS NULL OR TRIM(:NEW.ItrnKey) = ' ' THEN
            SELECT 'IT' || LPAD(TO_CHAR(SEQ_ITRN.NEXTVAL), 8, '0') INTO :NEW.ItrnKey FROM DUAL;
        END IF;
    ELSIF UPDATING THEN
        :NEW.EditDate := SYSDATE;
        :NEW.EditWho := USER;
        -- 保持原始添加信息
        :NEW.AddDate := :OLD.AddDate;
        :NEW.AddWho := :OLD.AddWho;
    END IF;
END;
/

PROMPT '>>> 已创建触发器 TRG_ITRN_AUDIT (库存事务审计触发器)';

-- =============================================
-- 订单管理触发器
-- =============================================

-- ORDERS表的状态更新触发器
CREATE OR REPLACE TRIGGER TRG_ORDERS_STATUS_UPDATE
BEFORE UPDATE ON ORDERS
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 状态变更日志
    IF :OLD.Status != :NEW.Status THEN
        -- 记录状态变更到日志表
        INSERT INTO ALERT (
            AlertKey, ModuleName, AlertType, AlertMessage, 
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
            'ORDERS',
            'STATUS_CHANGE',
            'Order ' || :NEW.OrderKey || ' status changed from ' || :OLD.Status || ' to ' || :NEW.Status,
            SYSDATE, USER, SYSDATE, USER
        );
    END IF;
END;
/

PROMPT '>>> 已创建触发器 TRG_ORDERS_STATUS_UPDATE (订单状态更新触发器)';

-- ORDERDETAIL表的审计触发器
CREATE OR REPLACE TRIGGER TRG_ORDERDETAIL_AUDIT
BEFORE INSERT OR UPDATE ON ORDERDETAIL
FOR EACH ROW
BEGIN
    -- 设置审计字段
    IF INSERTING THEN
        :NEW.AddDate := SYSDATE;
        :NEW.AddWho := USER;
        :NEW.EditDate := SYSDATE;
        :NEW.EditWho := USER;
        
        -- 生成订单明细键如果为空
        IF :NEW.OrderDetailKey IS NULL OR TRIM(:NEW.OrderDetailKey) = ' ' THEN
            SELECT 'OD' || LPAD(TO_CHAR(SEQ_ORDERDETAIL.NEXTVAL), 8, '0') INTO :NEW.OrderDetailKey FROM DUAL;
        END IF;
    ELSIF UPDATING THEN
        :NEW.EditDate := SYSDATE;
        :NEW.EditWho := USER;
        -- 保持原始添加信息
        :NEW.AddDate := :OLD.AddDate;
        :NEW.AddWho := :OLD.AddWho;
    END IF;
END;
/

PROMPT '>>> 已创建触发器 TRG_ORDERDETAIL_AUDIT (订单明细审计触发器)';

-- =============================================
-- 库存管理触发器
-- =============================================

-- LOTxLOCxID表的库存变更触发器
CREATE OR REPLACE TRIGGER TRG_LOTXLOCXID_INVENTORY_CHECK
BEFORE UPDATE ON LOTxLOCxID
FOR EACH ROW
BEGIN
    -- 设置编辑审计字段
    :NEW.EditDate := SYSDATE;
    :NEW.EditWho := USER;
    
    -- 检查库存不能为负数
    IF :NEW.Qty < 0 THEN
        RAISE_APPLICATION_ERROR(-20001, 'Inventory quantity cannot be negative');
    END IF;
    
    -- 检查分配数量不能超过总数量
    IF :NEW.QtyAllocated > :NEW.Qty THEN
        RAISE_APPLICATION_ERROR(-20002, 'Allocated quantity cannot exceed total quantity');
    END IF;
    
    -- 检查拣选数量不能超过分配数量
    IF :NEW.QtyPicked > :NEW.QtyAllocated THEN
        RAISE_APPLICATION_ERROR(-20003, 'Picked quantity cannot exceed allocated quantity');
    END IF;
    
    -- 记录库存变更日志
    IF :OLD.Qty != :NEW.Qty THEN
        INSERT INTO ALERT (
            AlertKey, ModuleName, AlertType, AlertMessage, 
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            'AL' || LPAD(TO_CHAR(SEQ_ALERT.NEXTVAL), 16, '0'),
            'INVENTORY',
            'QTY_CHANGE',
            'Inventory changed for ' || :NEW.StorerKey || '/' || :NEW.Sku || 
            ' from ' || :OLD.Qty || ' to ' || :NEW.Qty,
            SYSDATE, USER, SYSDATE, USER
        );
    END IF;
END;
/

PROMPT '>>> 已创建触发器 TRG_LOTXLOCXID_INVENTORY_CHECK (库存变更检查触发器)';

-- =============================================
-- 拣选管理触发器
-- =============================================

-- PICKDETAIL表的拣选完成触发器
CREATE OR REPLACE TRIGGER TRG_PICKDETAIL_COMPLETION
AFTER UPDATE ON PICKDETAIL
FOR EACH ROW
WHEN (NEW.Status = '9' AND OLD.Status != '9')
BEGIN
    -- 检查拣选头是否所有明细都完成
    DECLARE
        v_pending_count NUMBER;
        v_pickheaderkey CHAR(18);
    BEGIN
        v_pickheaderkey := :NEW.PickHeaderKey;
        
        SELECT COUNT(*) INTO v_pending_count
        FROM PICKDETAIL
        WHERE PickHeaderKey = v_pickheaderkey
        AND Status != '9';
        
        -- 如果所有明细都完成，更新拣选头状态
        IF v_pending_count = 0 THEN
            UPDATE PICKHEADER
            SET Status = '9',
                EditDate = SYSDATE,
                EditWho = USER
            WHERE PickHeaderKey = v_pickheaderkey;
        END IF;
    END;
END;
/

PROMPT '>>> 已创建触发器 TRG_PICKDETAIL_COMPLETION (拣选完成触发器)';

-- =============================================
-- 收货管理触发器
-- =============================================

-- RECEIPTDETAIL表的收货完成触发器
CREATE OR REPLACE TRIGGER TRG_RECEIPTDETAIL_COMPLETION
AFTER UPDATE ON RECEIPTDETAIL
FOR EACH ROW
WHEN (NEW.QtyReceived >= NEW.Qty AND OLD.QtyReceived < OLD.Qty)
BEGIN
    -- 检查收货头是否所有明细都完成
    DECLARE
        v_pending_count NUMBER;
        v_receiptkey CHAR(10);
    BEGIN
        v_receiptkey := :NEW.ReceiptKey;
        
        SELECT COUNT(*) INTO v_pending_count
        FROM RECEIPTDETAIL
        WHERE ReceiptKey = v_receiptkey
        AND QtyReceived < Qty;
        
        -- 如果所有明细都完成，更新收货头状态
        IF v_pending_count = 0 THEN
            UPDATE RECEIPT
            SET Status = '9',
                EditDate = SYSDATE,
                EditWho = USER
            WHERE ReceiptKey = v_receiptkey;
        END IF;
    END;
END;
/

PROMPT '>>> 已创建触发器 TRG_RECEIPTDETAIL_COMPLETION (收货完成触发器)';

-- =============================================
-- SKU管理触发器
-- =============================================

-- SKU表的审计触发器
CREATE OR REPLACE TRIGGER TRG_SKU_AUDIT
BEFORE INSERT OR UPDATE ON SKU
FOR EACH ROW
BEGIN
    -- 设置审计字段
    IF INSERTING THEN
        :NEW.AddDate := SYSDATE;
        :NEW.AddWho := USER;
        :NEW.EditDate := SYSDATE;
        :NEW.EditWho := USER;
    ELSIF UPDATING THEN
        :NEW.EditDate := SYSDATE;
        :NEW.EditWho := USER;
        -- 保持原始添加信息
        :NEW.AddDate := :OLD.AddDate;
        :NEW.AddWho := :OLD.AddWho;
    END IF;
END;
/

PROMPT '>>> 已创建触发器 TRG_SKU_AUDIT (SKU审计触发器)';

-- =============================================
-- 位置管理触发器
-- =============================================

-- LOC表的审计触发器
CREATE OR REPLACE TRIGGER TRG_LOC_AUDIT
BEFORE INSERT OR UPDATE ON LOC
FOR EACH ROW
BEGIN
    -- 设置审计字段
    IF INSERTING THEN
        :NEW.AddDate := SYSDATE;
        :NEW.AddWho := USER;
        :NEW.EditDate := SYSDATE;
        :NEW.EditWho := USER;
    ELSIF UPDATING THEN
        :NEW.EditDate := SYSDATE;
        :NEW.EditWho := USER;
        -- 保持原始添加信息
        :NEW.AddDate := :OLD.AddDate;
        :NEW.AddWho := :OLD.AddWho;
    END IF;
END;
/

PROMPT '>>> 已创建触发器 TRG_LOC_AUDIT (位置审计触发器)';

-- =============================================
-- 创建必要的序列
-- =============================================

-- 创建序列（如果不存在）
DECLARE
    v_count NUMBER;
BEGIN
    -- 检查并创建ITRN序列
    SELECT COUNT(*) INTO v_count FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_ITRN';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_ITRN START WITH 1 INCREMENT BY 1';
    END IF;
    
    -- 检查并创建ALERT序列
    SELECT COUNT(*) INTO v_count FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_ALERT';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_ALERT START WITH 1 INCREMENT BY 1';
    END IF;
    
    -- 检查并创建ORDERDETAIL序列
    SELECT COUNT(*) INTO v_count FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_ORDERDETAIL';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_ORDERDETAIL START WITH 1 INCREMENT BY 1';
    END IF;
END;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle触发器脚本执行完成';
PROMPT '>>> 已创建8个触发器和3个序列';
PROMPT '>>> 功能覆盖：';
PROMPT '>>> - 库存事务审计';
PROMPT '>>> - 订单状态管理';
PROMPT '>>> - 库存变更检查';
PROMPT '>>> - 拣选完成处理';
PROMPT '>>> - 收货完成处理';
PROMPT '>>> - SKU和位置审计';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 触发器创建完成！';
PROMPT '总触发器数量：8个专业触发器';
PROMPT '功能覆盖：';
PROMPT '- 数据完整性检查';
PROMPT '- 审计跟踪';
PROMPT '- 状态自动更新';
PROMPT '- 业务规则强制执行';
PROMPT '=============================================';
