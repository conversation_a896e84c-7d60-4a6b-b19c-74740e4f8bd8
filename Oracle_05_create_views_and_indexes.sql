-- =============================================
-- Oracle版本 - 创建视图和索引脚本
-- 功能：创建仓库管理系统的视图和索引 (Oracle版本)
-- 用途：查询优化、数据展示、性能提升等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：为关键表创建索引和业务视图以提升查询性能
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 创建业务视图
-- =============================================

-- 库存汇总视图
CREATE OR REPLACE VIEW V_INVENTORY_SUMMARY AS
SELECT 
    lli.StorerKey,
    lli.Sku,
    s.DESCR AS SkuDescription,
    SUM(lli.Qty) AS TotalQty,
    SUM(lli.QtyAllocated) AS TotalAllocated,
    SUM(lli.QtyPicked) AS TotalPicked,
    SUM(lli.Qty - lli.QtyAllocated - lli.QtyPicked) AS AvailableQty,
    COUNT(DISTINCT lli.Loc) AS LocationCount,
    COUNT(DISTINCT lli.Lot) AS LotCount
FROM LOTxLOCxID lli
JOIN SKU s ON lli.StorerKey = s.StorerKey AND lli.Sku = s.Sku
WHERE lli.Qty > 0
GROUP BY lli.StorerKey, lli.Sku, s.DESCR;

-- 授予权限
GRANT SELECT ON V_INVENTORY_SUMMARY TO nsql;

PROMPT '>>> 已创建视图 V_INVENTORY_SUMMARY (库存汇总视图)';

-- 订单状态视图
CREATE OR REPLACE VIEW V_ORDER_STATUS AS
SELECT 
    o.OrderKey,
    o.ExternOrderKey,
    o.StorerKey,
    o.ConsigneeKey,
    o.OrderDate,
    o.RequestedShipDate,
    o.Status,
    CASE o.Status
        WHEN '0' THEN 'Open'
        WHEN '1' THEN 'Allocated'
        WHEN '2' THEN 'Picking'
        WHEN '9' THEN 'Shipped'
        WHEN 'C' THEN 'Cancelled'
        ELSE 'Unknown'
    END AS StatusDescription,
    COUNT(od.OrderDetailKey) AS TotalLines,
    SUM(od.Qty) AS TotalQty,
    SUM(od.ShippedQty) AS TotalShippedQty,
    ROUND((SUM(od.ShippedQty) / NULLIF(SUM(od.Qty), 0)) * 100, 2) AS ShipPercentage
FROM ORDERS o
LEFT JOIN ORDERDETAIL od ON o.OrderKey = od.OrderKey
GROUP BY o.OrderKey, o.ExternOrderKey, o.StorerKey, o.ConsigneeKey, 
         o.OrderDate, o.RequestedShipDate, o.Status;

-- 授予权限
GRANT SELECT ON V_ORDER_STATUS TO nsql;

PROMPT '>>> 已创建视图 V_ORDER_STATUS (订单状态视图)';

-- 拣选工作量视图
CREATE OR REPLACE VIEW V_PICKING_WORKLOAD AS
SELECT 
    ph.WaveKey,
    ph.PickHeaderKey,
    ph.OrderKey,
    ph.Status,
    CASE ph.Status
        WHEN '0' THEN 'Released'
        WHEN '1' THEN 'Picking'
        WHEN '9' THEN 'Complete'
        WHEN 'C' THEN 'Cancelled'
        ELSE 'Unknown'
    END AS StatusDescription,
    COUNT(pd.PickDetailKey) AS TotalPicks,
    SUM(pd.Qty) AS TotalQty,
    SUM(pd.QtyPicked) AS TotalPickedQty,
    COUNT(DISTINCT pd.Loc) AS LocationCount,
    ph.AssignedUser,
    ph.Priority
FROM PICKHEADER ph
LEFT JOIN PICKDETAIL pd ON ph.PickHeaderKey = pd.PickHeaderKey
GROUP BY ph.WaveKey, ph.PickHeaderKey, ph.OrderKey, ph.Status, 
         ph.AssignedUser, ph.Priority;

-- 授予权限
GRANT SELECT ON V_PICKING_WORKLOAD TO nsql;

PROMPT '>>> 已创建视图 V_PICKING_WORKLOAD (拣选工作量视图)';

-- 收货进度视图
CREATE OR REPLACE VIEW V_RECEIVING_PROGRESS AS
SELECT 
    r.ReceiptKey,
    r.ExternReceiptKey,
    r.StorerKey,
    r.ReceiptDate,
    r.Status,
    CASE r.Status
        WHEN '0' THEN 'Open'
        WHEN '1' THEN 'Receiving'
        WHEN '9' THEN 'Complete'
        WHEN 'C' THEN 'Cancelled'
        ELSE 'Unknown'
    END AS StatusDescription,
    COUNT(rd.ReceiptDetailKey) AS TotalLines,
    SUM(rd.Qty) AS TotalQty,
    SUM(rd.QtyReceived) AS TotalReceivedQty,
    ROUND((SUM(rd.QtyReceived) / NULLIF(SUM(rd.Qty), 0)) * 100, 2) AS ReceivePercentage
FROM RECEIPT r
LEFT JOIN RECEIPTDETAIL rd ON r.ReceiptKey = rd.ReceiptKey
GROUP BY r.ReceiptKey, r.ExternReceiptKey, r.StorerKey, 
         r.ReceiptDate, r.Status;

-- 授予权限
GRANT SELECT ON V_RECEIVING_PROGRESS TO nsql;

PROMPT '>>> 已创建视图 V_RECEIVING_PROGRESS (收货进度视图)';

-- 位置利用率视图
CREATE OR REPLACE VIEW V_LOCATION_UTILIZATION AS
SELECT 
    l.Loc,
    l.LocationType,
    l.PutawayZone,
    l.Status,
    NVL(inv.SKUCount, 0) AS SKUCount,
    NVL(inv.TotalQty, 0) AS TotalQty,
    NVL(inv.TotalCube, 0) AS TotalCube,
    NVL(inv.TotalWeight, 0) AS TotalWeight,
    CASE 
        WHEN l.MaxQty > 0 THEN ROUND((NVL(inv.TotalQty, 0) / l.MaxQty) * 100, 2)
        ELSE 0
    END AS QtyUtilization,
    CASE 
        WHEN l.MaxCube > 0 THEN ROUND((NVL(inv.TotalCube, 0) / l.MaxCube) * 100, 2)
        ELSE 0
    END AS CubeUtilization
FROM LOC l
LEFT JOIN (
    SELECT 
        lli.Loc,
        COUNT(DISTINCT lli.Sku) AS SKUCount,
        SUM(lli.Qty) AS TotalQty,
        SUM(lli.Qty * NVL(s.Cube, 0)) AS TotalCube,
        SUM(lli.Qty * NVL(s.GrossWeight, 0)) AS TotalWeight
    FROM LOTxLOCxID lli
    JOIN SKU s ON lli.StorerKey = s.StorerKey AND lli.Sku = s.Sku
    WHERE lli.Qty > 0
    GROUP BY lli.Loc
) inv ON l.Loc = inv.Loc;

-- 授予权限
GRANT SELECT ON V_LOCATION_UTILIZATION TO nsql;

PROMPT '>>> 已创建视图 V_LOCATION_UTILIZATION (位置利用率视图)';

-- =============================================
-- 创建性能索引
-- =============================================

-- 库存表索引
CREATE INDEX IDX_LOTXLOCXID_STORERKEY_SKU ON LOTxLOCxID (StorerKey, Sku);
CREATE INDEX IDX_LOTXLOCXID_LOC ON LOTxLOCxID (Loc);
CREATE INDEX IDX_LOTXLOCXID_LOT ON LOTxLOCxID (Lot);
CREATE INDEX IDX_LOTXLOCXID_QTY ON LOTxLOCxID (Qty) WHERE Qty > 0;

PROMPT '>>> 已创建LOTxLOCxID表索引 (4个)';

-- 订单表索引
CREATE INDEX IDX_ORDERS_STORERKEY ON ORDERS (StorerKey);
CREATE INDEX IDX_ORDERS_ORDERDATE ON ORDERS (OrderDate);
CREATE INDEX IDX_ORDERS_STATUS ON ORDERS (Status);
CREATE INDEX IDX_ORDERS_EXTERNORDERKEY ON ORDERS (ExternOrderKey);

PROMPT '>>> 已创建ORDERS表索引 (4个)';

-- 订单明细表索引
CREATE INDEX IDX_ORDERDETAIL_ORDERKEY ON ORDERDETAIL (OrderKey);
CREATE INDEX IDX_ORDERDETAIL_SKU ON ORDERDETAIL (StorerKey, Sku);
CREATE INDEX IDX_ORDERDETAIL_STATUS ON ORDERDETAIL (Status);

PROMPT '>>> 已创建ORDERDETAIL表索引 (3个)';

-- 拣选表索引
CREATE INDEX IDX_PICKHEADER_ORDERKEY ON PICKHEADER (OrderKey);
CREATE INDEX IDX_PICKHEADER_WAVEKEY ON PICKHEADER (WaveKey);
CREATE INDEX IDX_PICKHEADER_STATUS ON PICKHEADER (Status);
CREATE INDEX IDX_PICKHEADER_ASSIGNEDUSER ON PICKHEADER (AssignedUser);

PROMPT '>>> 已创建PICKHEADER表索引 (4个)';

-- 拣选明细表索引
CREATE INDEX IDX_PICKDETAIL_PICKHEADERKEY ON PICKDETAIL (PickHeaderKey);
CREATE INDEX IDX_PICKDETAIL_LOC ON PICKDETAIL (Loc);
CREATE INDEX IDX_PICKDETAIL_SKU ON PICKDETAIL (StorerKey, Sku);
CREATE INDEX IDX_PICKDETAIL_STATUS ON PICKDETAIL (Status);

PROMPT '>>> 已创建PICKDETAIL表索引 (4个)';

-- 收货表索引
CREATE INDEX IDX_RECEIPT_STORERKEY ON RECEIPT (StorerKey);
CREATE INDEX IDX_RECEIPT_RECEIPTDATE ON RECEIPT (ReceiptDate);
CREATE INDEX IDX_RECEIPT_STATUS ON RECEIPT (Status);
CREATE INDEX IDX_RECEIPT_EXTERNRECEIPTKEY ON RECEIPT (ExternReceiptKey);

PROMPT '>>> 已创建RECEIPT表索引 (4个)';

-- 收货明细表索引
CREATE INDEX IDX_RECEIPTDETAIL_RECEIPTKEY ON RECEIPTDETAIL (ReceiptKey);
CREATE INDEX IDX_RECEIPTDETAIL_SKU ON RECEIPTDETAIL (StorerKey, Sku);
CREATE INDEX IDX_RECEIPTDETAIL_STATUS ON RECEIPTDETAIL (Status);

PROMPT '>>> 已创建RECEIPTDETAIL表索引 (3个)';

-- 库存事务表索引
CREATE INDEX IDX_ITRN_STORERKEY_SKU ON ITRN (StorerKey, Sku);
CREATE INDEX IDX_ITRN_EFFECTIVEDATE ON ITRN (EffectiveDate);
CREATE INDEX IDX_ITRN_TRANTYPE ON ITRN (TranType);
CREATE INDEX IDX_ITRN_FROMLOC ON ITRN (FromLoc);
CREATE INDEX IDX_ITRN_TOLOC ON ITRN (ToLoc);

PROMPT '>>> 已创建ITRN表索引 (5个)';

-- SKU表索引
CREATE INDEX IDX_SKU_STORERKEY ON SKU (StorerKey);
CREATE INDEX IDX_SKU_DESCR ON SKU (DESCR);
CREATE INDEX IDX_SKU_PACKKEY ON SKU (PackKey);

PROMPT '>>> 已创建SKU表索引 (3个)';

-- 位置表索引
CREATE INDEX IDX_LOC_LOCATIONTYPE ON LOC (LocationType);
CREATE INDEX IDX_LOC_PUTAWAYZONE ON LOC (PutawayZone);
CREATE INDEX IDX_LOC_STATUS ON LOC (Status);

PROMPT '>>> 已创建LOC表索引 (3个)';

-- 提交事务
COMMIT;

PROMPT '>>> Oracle视图和索引脚本执行完成';
PROMPT '>>> 已创建5个业务视图和37个性能索引';
PROMPT '>>> 功能覆盖：';
PROMPT '>>> - 库存汇总和分析';
PROMPT '>>> - 订单状态跟踪';
PROMPT '>>> - 拣选工作量管理';
PROMPT '>>> - 收货进度监控';
PROMPT '>>> - 位置利用率分析';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 视图和索引创建完成！';
PROMPT '总视图数量：5个业务视图';
PROMPT '总索引数量：37个性能索引';
PROMPT '功能覆盖：';
PROMPT '- 查询性能优化';
PROMPT '- 业务数据展示';
PROMPT '- 报表支持';
PROMPT '- 分析功能增强';
PROMPT '=============================================';
