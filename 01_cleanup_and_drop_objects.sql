-- =============================================
-- 清理和删除相关对象脚本
-- 功能：删除外键约束、主键约束、索引、视图和表
-- 用途：数据库重构前的清理工作
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 第一部分：删除外键约束
-- =============================================

-- 删除 CODELKUP 表的外键约束
IF NOT OBJECT_ID('FK_CODELKUP_LISTNAME_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_CODELKUP_LISTNAME_01 From Table CODELKUP>>>'
ALTER TABLE CODELKUP
DROP CONSTRAINT FK_CODELKUP_LISTNAME_01
END
GO

-- 删除 PALLETDETAIL 表的外键约束
IF NOT OBJECT_ID('FK_PALLETDETAIL_LOC_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_PALLETDETAIL_LOC_01 From Table PALLETDETAIL>>>'
ALTER TABLE PALLETDETAIL
DROP CONSTRAINT FK_PALLETDETAIL_LOC_01
END
GO

-- 删除 LOT 表的外键约束
IF NOT OBJECT_ID('FK_LOT_STORER_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_LOT_STORER_01 From Table LOT>>>'
ALTER TABLE LOT
DROP CONSTRAINT FK_LOT_STORER_01
END
GO

IF NOT OBJECT_ID('FK_LOT_SKU_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_LOT_SKU_01 From Table LOT>>>'
ALTER TABLE LOT
DROP CONSTRAINT FK_LOT_SKU_01
END
GO

IF NOT OBJECT_ID('FK_LOT_LOTATTRIBUTE_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_LOT_LOTATTRIBUTE_01 From Table LOT>>>'
ALTER TABLE LOT
DROP CONSTRAINT FK_LOT_LOTATTRIBUTE_01
END
GO

-- 删除 LOTATTRIBUTE 表的外键约束
IF NOT OBJECT_ID('FK_LOTATTRIBUTE_STORER_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_LOTATTRIBUTE_STORER_01 From Table LOTATTRIBUTE>>>'
ALTER TABLE LOTATTRIBUTE
DROP CONSTRAINT FK_LOTATTRIBUTE_STORER_01
END
GO

IF NOT OBJECT_ID('FK_LTATTR_SKU_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_LTATTR_SKU_01 From Table LOTATTRIBUTE>>>'
ALTER TABLE LOTATTRIBUTE
DROP CONSTRAINT FK_LTATTR_SKU_01
END
GO

-- 删除 LOTxLOCxID 表的外键约束
IF NOT OBJECT_ID('FK_LOTxLOCxID_LOT_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_LOTxLOCxID_LOT_01 From Table LOTxLOCxID>>>'
ALTER TABLE LOTxLOCxID
DROP CONSTRAINT FK_LOTxLOCxID_LOT_01
END
GO

IF NOT OBJECT_ID('FK_LOTxLOCxID_LOC_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_LOTxLOCxID_LOC_01 From Table LOTxLOCxID>>>'
ALTER TABLE LOTxLOCxID
DROP CONSTRAINT FK_LOTxLOCxID_LOC_01
END
GO

IF NOT OBJECT_ID('FK_LOTxLOCxID_ID_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_LOTxLOCxID_ID_01 From Table LOTxLOCxID>>>'
ALTER TABLE LOTxLOCxID
DROP CONSTRAINT FK_LOTxLOCxID_ID_01
END
GO

IF NOT OBJECT_ID('FK_LOTxLOCxID_SKU_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_LOTxLOCxID_SKU_01 From Table LOTxLOCxID>>>'
ALTER TABLE LOTxLOCxID
DROP CONSTRAINT FK_LOTxLOCxID_SKU_01
END
GO

-- 删除 SKU 表的外键约束
IF NOT OBJECT_ID('FK_SKU_STORER_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_SKU_STORER_01 From Table SKU>>>'
ALTER TABLE SKU
DROP CONSTRAINT FK_SKU_STORER_01
END
GO

-- 删除 SKUxLOC 表的外键约束
IF NOT OBJECT_ID('FK_SKUxLOC_SKU_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreing Key FK_SKUxLOC_SKU_01 From Table SKUxLOC>>>'
ALTER TABLE SKUxLOC
DROP CONSTRAINT FK_SKUxLOC_SKU_01
END
GO

IF NOT OBJECT_ID('FK_SKUxLOC_LOC_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_SKUxLOC_LOC_01 From Table SKUxLOC>>>'
ALTER TABLE SKUxLOC
DROP CONSTRAINT FK_SKUxLOC_LOC_01
END
GO

-- 删除 PICKDETAIL 表的外键约束
IF NOT OBJECT_ID('FK_PICKDETAIL_SKU_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_PICKDETAIL_SKU_01 From Table PICKDETAIL>>>'
ALTER TABLE PICKDETAIL
DROP CONSTRAINT FK_PICKDETAIL_SKU_01
END
GO

IF NOT OBJECT_ID('FK_PICKDETAIL_LOTLOCID_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_PICKDETAIL_LOTLOCID_01 From Table PICKDETAIL>>>'
ALTER TABLE PICKDETAIL
DROP CONSTRAINT FK_PICKDETAIL_LOTLOCID_01
END
GO

-- 删除 ORDERDETAIL 表的外键约束
IF NOT OBJECT_ID('FK_ORDERDETAIL_SKU_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_ORDERDETAIL_SKU_01 From Table ORDERDETAIL>>>'
ALTER TABLE ORDERDETAIL
DROP CONSTRAINT FK_ORDERDETAIL_SKU_01
END
GO

-- 删除 ORDERS 表的外键约束
IF NOT OBJECT_ID('FK_ORDERS_STORER_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_ORDERS_STORER_01 From Table ORDERS>>>'
ALTER TABLE ORDERS
DROP CONSTRAINT FK_ORDERS_STORER_01
END
GO

-- 删除 RECEIPTDETAIL 表的外键约束
IF NOT OBJECT_ID('FK_RECEIPTDETAIL_SKU_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_RECEIPTDETAIL_SKU_01 From Table RECEIPTDETAIL>>>'
ALTER TABLE RECEIPTDETAIL
DROP CONSTRAINT FK_RECEIPTDETAIL_SKU_01
END
GO

-- 删除 PODETAIL 表的外键约束
IF NOT OBJECT_ID('FK_PODETAIL_SKU_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_PODETAIL_SKU_01 From Table PODETAIL>>>'
ALTER TABLE PODETAIL
DROP CONSTRAINT FK_PODETAIL_SKU_01
END
GO

-- 删除 ADJUSTMENTDETAIL 表的外键约束
IF NOT OBJECT_ID('FK_ADJUSTMENTDETAIL_SKU_01') IS NULL
BEGIN
PRINT '<<<Dropping Foreign Key FK_ADJUSTMENTDETAIL_SKU_01 From Table ADJUSTMENTDETAIL>>>'
ALTER TABLE ADJUSTMENTDETAIL
DROP CONSTRAINT FK_ADJUSTMENTDETAIL_SKU_01
END
GO

-- 删除 CONTAINERDETAIL 表的外键约束
IF NOT OBJECT_ID('FK_CONTAINERDETAIL_PALLET_01') IS NULL
BEGIN
PRINT '<<<Dropping Foriegn Key FK_CONTAINERDETAIL_PALLET_01 From Table CONTAINERDETAIL>>>'
ALTER TABLE CONTAINERDETAIL
DROP CONSTRAINT FK_CONTAINERDETAIL_PALLET_01
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 第二部分：删除主键约束
-- =============================================

-- 删除 ALERT 表的主键约束
IF NOT OBJECT_ID('PKALERT') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKALETY From Table ALERT>>>'
ALTER TABLE ALERT
DROP CONSTRAINT PKALert
END
GO

-- 删除 CODELIST 表的主键约束
IF NOT OBJECT_ID('PKCODELIST') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKCODELIST From Table CODELIST>>>'
ALTER TABLE CODELIST
DROP CONSTRAINT PKCodeList
END
GO

-- 删除 CODELKUP 表的主键约束
IF NOT OBJECT_ID('PKCODELKUP') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKCODELKUP From Table CODELKUP>>>'
ALTER TABLE CODELKUP
DROP CONSTRAINT PKCodelkup
END
GO

-- 删除 NSQLCONFIG 表的主键约束
IF NOT OBJECT_ID('PKNSQLCONFIG') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKNSQLCONFIG From Table NSQLCONFIG>>>'
ALTER TABLE NSQLCONFIG
DROP CONSTRAINT PKNSQLConfig
END
GO

-- 删除 ID 表的主键约束
IF NOT OBJECT_ID('PKID') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKID From Table ID>>>'
ALTER TABLE ID
DROP CONSTRAINT PKID
END
GO

-- 删除 IDSTACK 表的主键约束
IF NOT OBJECT_ID('PKIDSTACK') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKIDSTACK From Table IDSTACK>>>'
ALTER TABLE IDSTACK
DROP CONSTRAINT PKIDSTACK
END
GO

-- 删除 ITRN 表的主键约束
IF NOT OBJECT_ID('PKITRN') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKITRN From Table ITRN>>>'
ALTER TABLE ITRN
DROP CONSTRAINT PKItrn
END
GO

-- 删除 LOC 表的主键约束和索引
IF NOT OBJECT_ID('PKLOC') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKLOC From Table LOC>>>'
ALTER TABLE LOC
DROP CONSTRAINT PKLOC
END

IF NOT OBJECT_ID('IDX_LOC_PUTAWAYZONE') IS NULL
BEGIN
PRINT '<<<Dropping Key IDX_LOC_PUTAWAYZONE From Table LOC>>>'
DROP INDEX LOC.IDX_LOC_PUTAWAYZONE
END

IF NOT OBJECT_ID('IDX_LOC_LOGICALLOCATION') IS NULL
BEGIN
PRINT '<<<Dropping Key IDX_LOC_LOGICALLOCATION From Table LOC>>>'
DROP INDEX LOC.IDX_LOC_LOGICALLOCATION
END
GO

-- 删除 LOT 表的主键约束
IF NOT OBJECT_ID('PKLOT') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKLOT From Table LOT>>>'
ALTER TABLE LOT
DROP CONSTRAINT PKLot
END
GO

-- 删除 LOTATTRIBUTE 表的主键约束
IF NOT OBJECT_ID('PKLOTATTRIBUTE') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKLOTATTRIBUTE From Table LOTATTRIBUTE>>>'
ALTER TABLE LOTATTRIBUTE
DROP CONSTRAINT PKLOTAttribute
END
GO

IF NOT OBJECT_ID('AK_LOTATTRIBUTE_01') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key AK_LOTATTRIBUTE_01 From Table LOTATTRIBUTE>>>'
ALTER TABLE LOTATTRIBUTE
DROP CONSTRAINT AK_LOTATTRIBUTE_01
END
GO

-- 删除 LOTxLOCxID 表的主键约束和索引
IF NOT OBJECT_ID('PKLOTxLOCxID') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKLOTxLOCxID From Table LOTxLOCxID>>>'
ALTER TABLE LOTxLOCxID
DROP CONSTRAINT PKLOTxLOCxID
END
GO

IF NOT OBJECT_ID('IDX_LOTxLOCxID_LOC') IS NULL
BEGIN
PRINT '<<<Dropping Index IDX_LOTxLOCxID_LOC From Table LOTxLOCxID>>>'
DROP INDEX LOTxLOCxID.IDX_LOTxLOCxID_LOC
END
GO

IF NOT OBJECT_ID('IDX_LOTxLOCxID_SKU') IS NULL
BEGIN
PRINT '<<<Dropping Index IDX_LOTxLOCxID_SKU From Table LOTxLOCxID>>>'
DROP INDEX LOTxLOCxID.IDX_LOTxLOCxID_SKU
END

IF NOT OBJECT_ID('IDX_LOTxLOCxID_ID') IS NULL
BEGIN
PRINT '<<<Dropping Index IDX_LOTxLOCxID_ID From Table LOTxLOCxID>>>'
DROP INDEX LOTxLOCxID.IDX_LOTxLOCxID_ID
END
GO

-- 删除 SKU 表的主键约束和索引
IF NOT OBJECT_ID('PKSKU') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKSKU From Table SKU>>>'
ALTER TABLE SKU
DROP CONSTRAINT PKSKU
END
GO

IF NOT OBJECT_ID('IDX_SKU_SKU') IS NULL
BEGIN
PRINT '<<<Dropping Index IDX_SKU_SKU From Table SKU>>>'
DROP INDEX SKU.IDX_SKU_SKU
END
GO

-- 删除 SKUxLOC 表的主键约束和索引
IF NOT OBJECT_ID('PKSKUxLOC') IS NULL
BEGIN
PRINT '<<<Dropping Primary Key PKSKUxLOC From Table SKUxLOC>>>'
ALTER TABLE SKUxLOC
DROP CONSTRAINT PKSKUxLOC
END
GO

IF NOT OBJECT_ID('IDX_SKUxLOC_SKU') IS NULL
BEGIN
PRINT '<<<Dropping Index IDX_SKUxLOC_SKU From Table SKUxLOC>>>'
DROP INDEX SKUxLOC.IDX_SKUxLOC_SKU
END
GO

IF NOT OBJECT_ID('IDX_SKUxLOC_LOC') IS NULL
BEGIN
PRINT '<<<Dropping Index IDX_SKUxLOC_LOC From Table SKUxLOC>>>'
DROP INDEX SKUxLOC.IDX_SKUxLOC_LOC
END

IF NOT OBJECT_ID('IDX_SKUxLOC_LOCTYPE') IS NULL
BEGIN
PRINT '<<<Dropping Index IDX_SKUxLOC_LOCTYPE From Table SKUxLOC>>>'
DROP INDEX SKUxLOC.IDX_SKUxLOC_LOCTYPE
END

IF NOT OBJECT_ID('IDX_SKUxLOC_REPSEV') IS NULL
BEGIN
PRINT '<<<Dropping Index IDX_SKUxLOC_REPSEV From Table SKUxLOC>>>'
DROP INDEX SKUxLOC.IDX_SKUxLOC_REPSEV
END
GO

-- 注意：此脚本包含主要的约束和索引删除
-- 更多约束删除请参考原始文件
