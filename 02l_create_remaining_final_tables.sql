-- =============================================
-- 创建剩余最终表结构脚本 (第十二部分)
-- 功能：创建仓库管理系统中剩余的最终表结构
-- 用途：追踪管理、区域设备、任务原因、分区管理等
-- =============================================

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- =============================================
-- 追踪管理表
-- =============================================

-- PTRACEDETAIL 表 (追踪明细表)
CREATE TABLE PTRACEDETAIL
(PTRACETYPE                   char(30) NOT NULL,
PTRACEKEY                    char(30) NOT NULL,
PTRACELINENUM                char(5) NOT NULL,
PTRACEDATA                   char(255) NOT NULL
CONSTRAINT DF_PTRACEDETAIL_PTRACEDATA DEFAULT "",
AddDate                      datetime NOT NULL
CONSTRAINT DF_PTRACEDETAIL_AddDate DEFAULT CURRENT_Timestamp,
AddWho                       char(18) NOT NULL
CONSTRAINT DF_PTRACEDETAIL_AddWho DEFAULT USER ,
EditDate                     datetime NOT NULL
CONSTRAINT DF_PTRACEDETAIL_EditDate DEFAULT CURRENT_Timestamp,
EditWho                      char(18) NOT NULL
CONSTRAINT DF_PTRACEDETAIL_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('PTRACEDETAIL') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PTRACEDETAIL FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PTRACEDETAIL>>>'
GRANT INSERT ON PTRACEDETAIL TO nsql
GRANT UPDATE ON PTRACEDETAIL TO nsql
GRANT DELETE ON PTRACEDETAIL TO nsql
GRANT SELECT ON PTRACEDETAIL TO nsql
END
GO

-- PTRACEHEAD 表 (追踪头表)
CREATE TABLE PTRACEHEAD
(PTRACETYPE               char(30) NOT NULL,
PTRACEKEY                char(30) NOT NULL,
PTRACESTATUS             char(10) NOT NULL
CONSTRAINT DF_PTRACEHEAD_PTRACESTATUS DEFAULT "0",
PTRACEDATE               datetime NOT NULL
CONSTRAINT DF_PTRACEHEAD_PTRACEDATE DEFAULT CURRENT_Timestamp,
PTRACEDESCR              char(60) NOT NULL
CONSTRAINT DF_PTRACEHEAD_PTRACEDESCR DEFAULT "",
AddDate                  datetime NOT NULL
CONSTRAINT DF_PTRACEHEAD_AddDate DEFAULT CURRENT_Timestamp,
AddWho                   char(18) NOT NULL
CONSTRAINT DF_PTRACEHEAD_AddWho DEFAULT USER ,
EditDate                 datetime NOT NULL
CONSTRAINT DF_PTRACEHEAD_EditDate DEFAULT CURRENT_Timestamp,
EditWho                  char(18) NOT NULL
CONSTRAINT DF_PTRACEHEAD_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('PTRACEHEAD') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PTRACEHEAD FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PTRACEHEAD>>>'
GRANT INSERT ON PTRACEHEAD TO nsql
GRANT UPDATE ON PTRACEHEAD TO nsql
GRANT DELETE ON PTRACEHEAD TO nsql
GRANT SELECT ON PTRACEHEAD TO nsql
END
GO

-- =============================================
-- 区域设备管理表
-- =============================================

-- PAZoneEquipmentExcludeDetail 表 (上架区域设备排除明细表)
CREATE TABLE PAZoneEquipmentExcludeDetail
(PutawayZone              char(10)     NOT NULL
CONSTRAINT DF_PAZEED_PutawayZone DEFAULT "" ,
EquipmentProfileKey      char(10)     NOT NULL
CONSTRAINT DF_PAZEED_EquipmentProfileKey DEFAULT "" ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_PAZEED_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_PAZEED_AddWho      DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_PAZEED_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_PAZEED_EditWho     DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('PAZoneEquipmentExcludeDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE PAZoneEquipmentExcludeDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE PAZoneEquipmentExcludeDetail>>>'
GRANT INSERT ON PAZoneEquipmentExcludeDetail TO nsql
GRANT UPDATE ON PAZoneEquipmentExcludeDetail TO nsql
GRANT DELETE ON PAZoneEquipmentExcludeDetail TO nsql
GRANT SELECT ON PAZoneEquipmentExcludeDetail TO nsql
END
GO

-- =============================================
-- 任务管理原因表
-- =============================================

-- TaskManagerReason 表 (任务管理原因表)
CREATE TABLE TaskManagerReason
(TaskManagerReasonKey     char(10)     NOT NULL
CONSTRAINT DF_TMR_Key         DEFAULT "" ,
TaskType                 char(10)     NOT NULL
CONSTRAINT DF_TMR_TaskType    DEFAULT "" ,
ReasonCode               char(10)     NOT NULL
CONSTRAINT DF_TMR_ReasonCode  DEFAULT "" ,
ReasonDescription        char(60)     NOT NULL
CONSTRAINT DF_TMR_ReasonDescription DEFAULT "" ,
Active                   char(1)      NOT NULL
CONSTRAINT DF_TMR_Active      DEFAULT "Y" ,
AddDate                  datetime     NOT NULL
CONSTRAINT DF_TMR_AddDate     DEFAULT CURRENT_Timestamp ,
AddWho                   char(18)     NOT NULL
CONSTRAINT DF_TMR_AddWho      DEFAULT USER ,
EditDate                 datetime     NOT NULL
CONSTRAINT DF_TMR_EditDate    DEFAULT CURRENT_Timestamp ,
EditWho                  char(18)     NOT NULL
CONSTRAINT DF_TMR_EditWho     DEFAULT USER ,
TrafficCop               char(1)      NULL,
ArchiveCop               char(1)      NULL
)
GO

IF OBJECT_ID('TaskManagerReason') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE TaskManagerReason FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE TaskManagerReason>>>'
GRANT INSERT ON TaskManagerReason TO nsql
GRANT UPDATE ON TaskManagerReason TO nsql
GRANT DELETE ON TaskManagerReason TO nsql
GRANT SELECT ON TaskManagerReason TO nsql
END
GO

-- =============================================
-- 分区管理表
-- =============================================

-- Section 表 (分区表)
CREATE TABLE Section
(SectionKey    char(10)                NOT NULL
CONSTRAINT DF_Section_SectionKey DEFAULT "",
SectionName    char(30)                NOT NULL
CONSTRAINT DF_Section_SectionName DEFAULT "",
SectionType    char(10)                NOT NULL
CONSTRAINT DF_Section_SectionType DEFAULT "",
Description    char(60)                NOT NULL
CONSTRAINT DF_Section_Description DEFAULT "",
Active         char(1)                 NOT NULL
CONSTRAINT DF_Section_Active DEFAULT "Y",
AddDate        datetime                NOT NULL
CONSTRAINT DF_Section_AddDate DEFAULT CURRENT_Timestamp,
AddWho         char(18)                NOT NULL
CONSTRAINT DF_Section_AddWho DEFAULT USER ,
EditDate       datetime                NOT NULL
CONSTRAINT DF_Section_EditDate DEFAULT CURRENT_Timestamp,
EditWho        char(18)                NOT NULL
CONSTRAINT DF_Section_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('Section') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE Section FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE Section>>>'
GRANT INSERT ON Section TO nsql
GRANT UPDATE ON Section TO nsql
GRANT DELETE ON Section TO nsql
GRANT SELECT ON Section TO nsql
END
GO

-- =============================================
-- 附加服务明细表
-- =============================================

-- AccessorialDetail 表 (附加服务明细表)
CREATE TABLE AccessorialDetail
(Accessorialkey              char(10)  NOT NULL,
AccessorialLineNumber       char(5)   NOT NULL,
ServiceKey                  char(10)  NOT NULL
CONSTRAINT DF_AccessorialDetail_ServiceKey DEFAULT "",
Descrip                     char(30)  NOT NULL
CONSTRAINT DF_AccessorialDetail_Descrip DEFAULT "",
Rate                        decimal(12,6) NOT NULL
CONSTRAINT DF_AccessorialDetail_Rate DEFAULT 0.0,
UOM                         char(10)  NOT NULL
CONSTRAINT DF_AccessorialDetail_UOM DEFAULT "",
MinimumCharge               decimal(12,6) NOT NULL
CONSTRAINT DF_AccessorialDetail_MinimumCharge DEFAULT 0.0,
MaximumCharge               decimal(12,6) NOT NULL
CONSTRAINT DF_AccessorialDetail_MaximumCharge DEFAULT 999999.99,
EffectiveDate               datetime  NOT NULL
CONSTRAINT DF_AccessorialDetail_EffectiveDate DEFAULT CURRENT_Timestamp,
ExpirationDate              datetime  NOT NULL
CONSTRAINT DF_AccessorialDetail_ExpirationDate DEFAULT 'JAN 01 2100',
AddDate                     datetime  NOT NULL
CONSTRAINT DF_AccessorialDetail_AddDate DEFAULT CURRENT_Timestamp,
AddWho                      char(18)  NOT NULL
CONSTRAINT DF_AccessorialDetail_AddWho DEFAULT USER ,
EditDate                    datetime  NOT NULL
CONSTRAINT DF_AccessorialDetail_EditDate DEFAULT CURRENT_Timestamp,
EditWho                     char(18)  NOT NULL
CONSTRAINT DF_AccessorialDetail_EditWho DEFAULT USER
)
GO

IF OBJECT_ID('AccessorialDetail') IS NULL
BEGIN
PRINT '<<<CREATION OF TABLE AccessorialDetail FAILED>>>'
END
ELSE
BEGIN
PRINT '<<<CREATED TABLE AccessorialDetail>>>'
GRANT INSERT ON AccessorialDetail TO nsql
GRANT UPDATE ON AccessorialDetail TO nsql
GRANT DELETE ON AccessorialDetail TO nsql
GRANT SELECT ON AccessorialDetail TO nsql
END
GO

-- 清理事务日志
DECLARE @c_command char(254)
SELECT @c_command = "DUMP TRANSACTION " + DB_NAME() + " WITH NO_LOG"
EXECUTE (@c_command)
GO

-- 注意：此脚本包含剩余的最终表结构
-- 这是最后一个表结构脚本，完成了所有145张表的创建
