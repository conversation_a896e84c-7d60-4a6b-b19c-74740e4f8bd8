-- =============================================
-- Oracle版本 - 创建最终遗漏表结构脚本 (第十部分)
-- 功能：创建仓库管理系统中最终遗漏的重要表结构 (Oracle版本)
-- 用途：消息管理、交叉转运、库存控制、RF设备等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 消息管理表
-- =============================================

-- MESSAGE_ID 表 (消息ID表)
CREATE TABLE MESSAGE_ID
(MsgId                   CHAR(40) NOT NULL
CONSTRAINT DF_MESSAGE_ID_MsgId DEFAULT ' ',
MsgType                  CHAR(10) NOT NULL
CONSTRAINT DF_MESSAGE_ID_MsgType DEFAULT ' ',
MsgStatus                CHAR(10) NOT NULL
CONSTRAINT DF_MESSAGE_ID_MsgStatus DEFAULT '0',
AddDate                  DATE NOT NULL
CONSTRAINT DF_MESSAGE_ID_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_MESSAGE_ID_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_MESSAGE_ID_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_MESSAGE_ID_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE MESSAGE_ID ADD CONSTRAINT PK_MESSAGE_ID PRIMARY KEY (MsgId);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON MESSAGE_ID TO nsql;

PROMPT '>>> 已创建表 MESSAGE_ID (消息ID表)';

-- MESSAGE_TEXT 表 (消息文本表)
CREATE TABLE MESSAGE_TEXT
(MsgId                   CHAR(40) NOT NULL,
LineNumber               NUMBER(10) NOT NULL,
MessageText              VARCHAR2(255) NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_MessageText DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_MESSAGE_TEXT_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE MESSAGE_TEXT ADD CONSTRAINT PK_MESSAGE_TEXT PRIMARY KEY (MsgId, LineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON MESSAGE_TEXT TO nsql;

PROMPT '>>> 已创建表 MESSAGE_TEXT (消息文本表)';

-- =============================================
-- 计费汇总表
-- =============================================

-- BILL_ACCUMULATEDCHARGES 表 (计费累计费用表)
CREATE TABLE BILL_ACCUMULATEDCHARGES (
Ident                    NUMBER(10) NOT NULL,
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_StorerKey DEFAULT ' ',
TariffKey                CHAR(10) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_TariffKey DEFAULT ' ',
ChargeType               CHAR(10) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_ChargeType DEFAULT ' ',
ChargeAmount             NUMBER(12,6) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_ChargeAmount DEFAULT 0.0,
BillingPeriod            CHAR(10) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_BillingPeriod DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_BILL_ACCUMULATEDCHARGES_EditWho DEFAULT USER
);

-- 创建序列
CREATE SEQUENCE SEQ_BILL_ACCUMULATEDCHARGES START WITH 1 INCREMENT BY 1;

-- 添加主键
ALTER TABLE BILL_ACCUMULATEDCHARGES ADD CONSTRAINT PK_BILL_ACCUMULATEDCHARGES PRIMARY KEY (Ident);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BILL_ACCUMULATEDCHARGES TO nsql;

PROMPT '>>> 已创建表 BILL_ACCUMULATEDCHARGES (计费累计费用表)';

-- BILLING_DETAIL_CUT 表 (计费明细截止表)
CREATE TABLE BILLING_DETAIL_CUT (
StorerKey CHAR(15) NOT NULL,
BillThruDate DATE NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_BillThruDate DEFAULT SYSDATE,
AddDate                  DATE NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_BILLING_DETAIL_CUT_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE BILLING_DETAIL_CUT ADD CONSTRAINT PK_BILLING_DETAIL_CUT PRIMARY KEY (StorerKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BILLING_DETAIL_CUT TO nsql;

PROMPT '>>> 已创建表 BILLING_DETAIL_CUT (计费明细截止表)';

-- BILLING_SUMMARY_CUT 表 (计费汇总截止表)
CREATE TABLE BILLING_SUMMARY_CUT(
StorerKey CHAR(15) NOT NULL,
BillThruDate DATE NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_BillThruDate DEFAULT SYSDATE,
AddDate                  DATE NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_BILLING_SUMMARY_CUT_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE BILLING_SUMMARY_CUT ADD CONSTRAINT PK_BILLING_SUMMARY_CUT PRIMARY KEY (StorerKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON BILLING_SUMMARY_CUT TO nsql;

PROMPT '>>> 已创建表 BILLING_SUMMARY_CUT (计费汇总截止表)';

-- =============================================
-- 任务调度表
-- =============================================

-- TRIDENTSCHEDULER 表 (三叉戟调度器表)
CREATE TABLE TRIDENTSCHEDULER
(
SchedulerKey             CHAR(10) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_SchedulerKey DEFAULT ' ',
TaskType                 CHAR(20) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_TaskType DEFAULT ' ',
TaskStatus               CHAR(10) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_TaskStatus DEFAULT '0',
ScheduledTime            DATE NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_ScheduledTime DEFAULT SYSDATE,
ExecutedTime             DATE NULL,
TaskParameters           CLOB NULL,
AddDate                  DATE NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TRIDENTSCHEDULER_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE TRIDENTSCHEDULER ADD CONSTRAINT PK_TRIDENTSCHEDULER PRIMARY KEY (SchedulerKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TRIDENTSCHEDULER TO nsql;

PROMPT '>>> 已创建表 TRIDENTSCHEDULER (三叉戟调度器表)';

-- =============================================
-- 预分配拣选表
-- =============================================

-- PreAllocatePickDetail 表 (预分配拣选明细表)
CREATE TABLE PreAllocatePickDetail
(PreAllocatePickDetailKey CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_PreAllocatePickDetailKey DEFAULT ' ',
OrderKey                     CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_OrderKey DEFAULT ' ',
OrderLineNumber              CHAR(5) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_OrderLineNumber DEFAULT ' ',
Sku                          CHAR(20) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_Sku DEFAULT ' ',
StorerKey                    CHAR(15) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_StorerKey DEFAULT ' ',
Lot                          CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_Lot DEFAULT ' ',
Loc                          CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_Loc DEFAULT ' ',
ID                           CHAR(18) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_ID DEFAULT ' ',
Qty                          NUMBER(10) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_Qty DEFAULT 0,
Status                       CHAR(10) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_Status DEFAULT '0',
AddDate                      DATE NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_AddDate DEFAULT SYSDATE,
AddWho                       CHAR(18) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_AddWho DEFAULT USER,
EditDate                     DATE NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_EditDate DEFAULT SYSDATE,
EditWho                      CHAR(18) NOT NULL
CONSTRAINT DF_PreAllocatePickDetail_EditWho DEFAULT USER,
TrafficCop                   CHAR(1) NULL,
ArchiveCop                   CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PreAllocatePickDetail ADD CONSTRAINT PK_PreAllocatePickDetail 
PRIMARY KEY (PreAllocatePickDetailKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PreAllocatePickDetail TO nsql;

PROMPT '>>> 已创建表 PreAllocatePickDetail (预分配拣选明细表)';

-- =============================================
-- 交叉转运表
-- =============================================

-- XDOCK 表 (交叉转运表)
CREATE TABLE XDOCK
(XDOCKKEY           CHAR(10) NOT NULL,
Status             CHAR(10) NOT NULL
CONSTRAINT DF_XDOCK_Status DEFAULT '0',
ExternXDOCKKey     CHAR(30) NOT NULL
CONSTRAINT DF_XDOCK_ExternXDOCKKey DEFAULT ' ',
XDOCKDate          DATE NOT NULL
CONSTRAINT DF_XDOCK_XDOCKDate DEFAULT SYSDATE,
Type               CHAR(10) NOT NULL
CONSTRAINT DF_XDOCK_Type DEFAULT '0',
Notes              CLOB NULL,
EffectiveDate      DATE NOT NULL
CONSTRAINT DF_XDOCK_EffectiveDate DEFAULT SYSDATE,
AddDate            DATE NOT NULL
CONSTRAINT DF_XDOCK_AddDate DEFAULT SYSDATE,
AddWho             CHAR(18) NOT NULL
CONSTRAINT DF_XDOCK_AddWho DEFAULT USER,
EditDate           DATE NOT NULL
CONSTRAINT DF_XDOCK_EditDate DEFAULT SYSDATE,
EditWho            CHAR(18) NOT NULL
CONSTRAINT DF_XDOCK_EditWho DEFAULT USER,
TrafficCop         CHAR(1) NULL,
ArchiveCop         CHAR(1) NULL
);

-- 添加主键
ALTER TABLE XDOCK ADD CONSTRAINT PK_XDOCK PRIMARY KEY (XDOCKKEY);

-- 添加检查约束
ALTER TABLE XDOCK ADD CONSTRAINT CK_XDOCK_Status
CHECK (Status IN ('0', '1', '2', '3', '4', '5', '9'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON XDOCK TO nsql;

PROMPT '>>> 已创建表 XDOCK (交叉转运表)';

-- XDOCKDETAIL 表 (交叉转运明细表)
CREATE TABLE XDOCKDETAIL
(XDOCKKEY            CHAR(10) NOT NULL,
XDOCKLineNumber     CHAR(5) NOT NULL,
XDOCKDetailSysId    NUMBER(10) NULL,
ExternXDOCKKey      CHAR(30) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_ExternXDOCKKey DEFAULT ' ',
ExternLineNo        CHAR(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_ExternLineNo DEFAULT ' ',
Sku                 CHAR(20) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_Sku DEFAULT ' ',
StorerKey           CHAR(15) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_StorerKey DEFAULT ' ',
Qty                 NUMBER(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_Qty DEFAULT 0,
UOM                 CHAR(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_UOM DEFAULT ' ',
PackKey             CHAR(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_PackKey DEFAULT 'STD',
Lot                 CHAR(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_Lot DEFAULT ' ',
ID                  CHAR(18) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_ID DEFAULT ' ',
FromLoc             CHAR(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_FromLoc DEFAULT 'UNKNOWN',
ToLoc               CHAR(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_ToLoc DEFAULT 'UNKNOWN',
Status              CHAR(10) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_Status DEFAULT '0',
EffectiveDate       DATE NOT NULL
CONSTRAINT DF_XDOCKDETAIL_EffectiveDate DEFAULT SYSDATE,
AddDate             DATE NOT NULL
CONSTRAINT DF_XDOCKDETAIL_AddDate DEFAULT SYSDATE,
AddWho              CHAR(18) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_AddWho DEFAULT USER,
EditDate            DATE NOT NULL
CONSTRAINT DF_XDOCKDETAIL_EditDate DEFAULT SYSDATE,
EditWho             CHAR(18) NOT NULL
CONSTRAINT DF_XDOCKDETAIL_EditWho DEFAULT USER,
TrafficCop          CHAR(1) NULL,
ArchiveCop          CHAR(1) NULL
);

-- 添加主键
ALTER TABLE XDOCKDETAIL ADD CONSTRAINT PK_XDOCKDETAIL PRIMARY KEY (XDOCKKEY, XDOCKLineNumber);

-- 添加检查约束
ALTER TABLE XDOCKDETAIL ADD CONSTRAINT CK_XDOCKDETAIL_Status
CHECK (Status IN ('0', '1', '2', '3', '4', '5', '9'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON XDOCKDETAIL TO nsql;

PROMPT '>>> 已创建表 XDOCKDETAIL (交叉转运明细表)';

-- =============================================
-- 库存控制表
-- =============================================

-- INVENTORYHOLD 表 (库存冻结表)
CREATE TABLE INVENTORYHOLD
(InventoryHoldKey         CHAR(10) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_InventoryHoldKey DEFAULT ' ',
StorerKey                CHAR(15) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_StorerKey DEFAULT ' ',
Sku                      CHAR(20) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_Sku DEFAULT ' ',
Lot                      CHAR(10) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_Lot DEFAULT ' ',
Loc                      CHAR(10) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_Loc DEFAULT ' ',
ID                       CHAR(18) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_ID DEFAULT ' ',
HoldType                 CHAR(10) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_HoldType DEFAULT ' ',
HoldReason               CHAR(60) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_HoldReason DEFAULT ' ',
QtyHeld                  NUMBER(10) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_QtyHeld DEFAULT 0,
Status                   CHAR(10) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_Status DEFAULT '0',
AddDate                  DATE NOT NULL
CONSTRAINT DF_INVENTORYHOLD_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_INVENTORYHOLD_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_INVENTORYHOLD_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE INVENTORYHOLD ADD CONSTRAINT PK_INVENTORYHOLD PRIMARY KEY (InventoryHoldKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON INVENTORYHOLD TO nsql;

PROMPT '>>> 已创建表 INVENTORYHOLD (库存冻结表)';

-- 提交事务
COMMIT;

PROMPT '>>> 最终遗漏表结构脚本执行完成 (8张表)';
PROMPT '>>> 请继续执行后续脚本以创建更多表结构';
