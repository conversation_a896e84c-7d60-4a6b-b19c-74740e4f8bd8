-- =============================================
-- Oracle版本 - 创建剩余最终表结构脚本 (第十二部分)
-- 功能：创建仓库管理系统中剩余的最终表结构 (Oracle版本)
-- 用途：追踪管理、区域设备、任务原因、分区管理等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 追踪管理表
-- =============================================

-- PTRACEDETAIL 表 (追踪明细表)
CREATE TABLE PTRACEDETAIL
(PTRACETYPE                   CHAR(30) NOT NULL,
PTRACEKEY                    CHAR(30) NOT NULL,
PTRACELINENUM                CHAR(5) NOT NULL,
PTRACEDATA                   CHAR(255) NOT NULL
CONSTRAINT DF_PTRACEDETAIL_PTRACEDATA DEFAULT ' ',
AddDate                      DATE NOT NULL
CONSTRAINT DF_PTRACEDETAIL_AddDate DEFAULT SYSDATE,
AddWho                       CHAR(18) NOT NULL
CONSTRAINT DF_PTRACEDETAIL_AddWho DEFAULT USER,
EditDate                     DATE NOT NULL
CONSTRAINT DF_PTRACEDETAIL_EditDate DEFAULT SYSDATE,
EditWho                      CHAR(18) NOT NULL
CONSTRAINT DF_PTRACEDETAIL_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE PTRACEDETAIL ADD CONSTRAINT PK_PTRACEDETAIL 
PRIMARY KEY (PTRACETYPE, PTRACEKEY, PTRACELINENUM);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PTRACEDETAIL TO nsql;

PROMPT '>>> 已创建表 PTRACEDETAIL (追踪明细表)';

-- PTRACEHEAD 表 (追踪头表)
CREATE TABLE PTRACEHEAD
(PTRACETYPE               CHAR(30) NOT NULL,
PTRACEKEY                CHAR(30) NOT NULL,
PTRACESTATUS             CHAR(10) NOT NULL
CONSTRAINT DF_PTRACEHEAD_PTRACESTATUS DEFAULT '0',
PTRACEDATE               DATE NOT NULL
CONSTRAINT DF_PTRACEHEAD_PTRACEDATE DEFAULT SYSDATE,
PTRACEDESCR              CHAR(60) NOT NULL
CONSTRAINT DF_PTRACEHEAD_PTRACEDESCR DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_PTRACEHEAD_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PTRACEHEAD_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PTRACEHEAD_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PTRACEHEAD_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE PTRACEHEAD ADD CONSTRAINT PK_PTRACEHEAD PRIMARY KEY (PTRACETYPE, PTRACEKEY);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PTRACEHEAD TO nsql;

PROMPT '>>> 已创建表 PTRACEHEAD (追踪头表)';

-- =============================================
-- 区域设备管理表
-- =============================================

-- PAZoneEquipmentExcludeDetail 表 (上架区域设备排除明细表)
CREATE TABLE PAZoneEquipmentExcludeDetail
(PutawayZone              CHAR(10) NOT NULL
CONSTRAINT DF_PAZoneEquipmentExcludeDetail_PutawayZone DEFAULT ' ',
EquipmentProfileKey      CHAR(10) NOT NULL
CONSTRAINT DF_PAZoneEquipmentExcludeDetail_EquipmentProfileKey DEFAULT ' ',
AddDate                  DATE NOT NULL
CONSTRAINT DF_PAZoneEquipmentExcludeDetail_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_PAZoneEquipmentExcludeDetail_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_PAZoneEquipmentExcludeDetail_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_PAZoneEquipmentExcludeDetail_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE PAZoneEquipmentExcludeDetail ADD CONSTRAINT PK_PAZoneEquipmentExcludeDetail 
PRIMARY KEY (PutawayZone, EquipmentProfileKey);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON PAZoneEquipmentExcludeDetail TO nsql;

PROMPT '>>> 已创建表 PAZoneEquipmentExcludeDetail (上架区域设备排除明细表)';

-- =============================================
-- 任务管理原因表
-- =============================================

-- TaskManagerReason 表 (任务管理原因表)
CREATE TABLE TaskManagerReason
(TaskManagerReasonKey     CHAR(10) NOT NULL
CONSTRAINT DF_TaskManagerReason_TaskManagerReasonKey DEFAULT ' ',
TaskType                 CHAR(10) NOT NULL
CONSTRAINT DF_TaskManagerReason_TaskType DEFAULT ' ',
ReasonCode               CHAR(10) NOT NULL
CONSTRAINT DF_TaskManagerReason_ReasonCode DEFAULT ' ',
ReasonDescription        CHAR(60) NOT NULL
CONSTRAINT DF_TaskManagerReason_ReasonDescription DEFAULT ' ',
Active                   CHAR(1) NOT NULL
CONSTRAINT DF_TaskManagerReason_Active DEFAULT 'Y',
AddDate                  DATE NOT NULL
CONSTRAINT DF_TaskManagerReason_AddDate DEFAULT SYSDATE,
AddWho                   CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerReason_AddWho DEFAULT USER,
EditDate                 DATE NOT NULL
CONSTRAINT DF_TaskManagerReason_EditDate DEFAULT SYSDATE,
EditWho                  CHAR(18) NOT NULL
CONSTRAINT DF_TaskManagerReason_EditWho DEFAULT USER,
TrafficCop               CHAR(1) NULL,
ArchiveCop               CHAR(1) NULL
);

-- 添加主键
ALTER TABLE TaskManagerReason ADD CONSTRAINT PK_TaskManagerReason PRIMARY KEY (TaskManagerReasonKey);

-- 添加检查约束
ALTER TABLE TaskManagerReason ADD CONSTRAINT CK_TaskManagerReason_Active 
CHECK (Active IN ('Y', 'N'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON TaskManagerReason TO nsql;

PROMPT '>>> 已创建表 TaskManagerReason (任务管理原因表)';

-- =============================================
-- 分区管理表
-- =============================================

-- Section 表 (分区表)
CREATE TABLE Section
(SectionKey    CHAR(10) NOT NULL
CONSTRAINT DF_Section_SectionKey DEFAULT ' ',
SectionName    CHAR(30) NOT NULL
CONSTRAINT DF_Section_SectionName DEFAULT ' ',
SectionType    CHAR(10) NOT NULL
CONSTRAINT DF_Section_SectionType DEFAULT ' ',
Description    CHAR(60) NOT NULL
CONSTRAINT DF_Section_Description DEFAULT ' ',
Active         CHAR(1) NOT NULL
CONSTRAINT DF_Section_Active DEFAULT 'Y',
AddDate        DATE NOT NULL
CONSTRAINT DF_Section_AddDate DEFAULT SYSDATE,
AddWho         CHAR(18) NOT NULL
CONSTRAINT DF_Section_AddWho DEFAULT USER,
EditDate       DATE NOT NULL
CONSTRAINT DF_Section_EditDate DEFAULT SYSDATE,
EditWho        CHAR(18) NOT NULL
CONSTRAINT DF_Section_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE Section ADD CONSTRAINT PK_Section PRIMARY KEY (SectionKey);

-- 添加检查约束
ALTER TABLE Section ADD CONSTRAINT CK_Section_Active CHECK (Active IN ('Y', 'N'));

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON Section TO nsql;

PROMPT '>>> 已创建表 Section (分区表)';

-- =============================================
-- 附加服务明细表
-- =============================================

-- AccessorialDetail 表 (附加服务明细表)
CREATE TABLE AccessorialDetail
(Accessorialkey              CHAR(10) NOT NULL,
AccessorialLineNumber       CHAR(5) NOT NULL,
ServiceKey                  CHAR(10) NOT NULL
CONSTRAINT DF_AccessorialDetail_ServiceKey DEFAULT ' ',
Descrip                     CHAR(30) NOT NULL
CONSTRAINT DF_AccessorialDetail_Descrip DEFAULT ' ',
Rate                        NUMBER(12,6) NOT NULL
CONSTRAINT DF_AccessorialDetail_Rate DEFAULT 0.0,
UOM                         CHAR(10) NOT NULL
CONSTRAINT DF_AccessorialDetail_UOM DEFAULT ' ',
MinimumCharge               NUMBER(12,6) NOT NULL
CONSTRAINT DF_AccessorialDetail_MinimumCharge DEFAULT 0.0,
MaximumCharge               NUMBER(12,6) NOT NULL
CONSTRAINT DF_AccessorialDetail_MaximumCharge DEFAULT 999999.99,
EffectiveDate               DATE NOT NULL
CONSTRAINT DF_AccessorialDetail_EffectiveDate DEFAULT SYSDATE,
ExpirationDate              DATE NOT NULL
CONSTRAINT DF_AccessorialDetail_ExpirationDate DEFAULT TO_DATE('2100-01-01', 'YYYY-MM-DD'),
AddDate                     DATE NOT NULL
CONSTRAINT DF_AccessorialDetail_AddDate DEFAULT SYSDATE,
AddWho                      CHAR(18) NOT NULL
CONSTRAINT DF_AccessorialDetail_AddWho DEFAULT USER,
EditDate                    DATE NOT NULL
CONSTRAINT DF_AccessorialDetail_EditDate DEFAULT SYSDATE,
EditWho                     CHAR(18) NOT NULL
CONSTRAINT DF_AccessorialDetail_EditWho DEFAULT USER
);

-- 添加主键
ALTER TABLE AccessorialDetail ADD CONSTRAINT PK_AccessorialDetail 
PRIMARY KEY (Accessorialkey, AccessorialLineNumber);

-- 授予权限
GRANT INSERT, UPDATE, DELETE, SELECT ON AccessorialDetail TO nsql;

PROMPT '>>> 已创建表 AccessorialDetail (附加服务明细表)';

-- 提交事务
COMMIT;

PROMPT '>>> 剩余最终表结构脚本执行完成 (6张表)';
PROMPT '>>> 这是最后一个表结构脚本，完成了所有145张表的创建';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 所有表结构创建完成！';
PROMPT '总表数量：145张表';
PROMPT '脚本文件：12个表结构脚本';
PROMPT '功能覆盖：企业级完整WMS系统';
PROMPT '=============================================';

-- 重置环境参数
SET SERVEROUTPUT OFF;
