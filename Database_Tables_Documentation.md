# 仓库管理系统数据库表结构文档

## 概述

本文档详细说明了从 `NEPISQL.sql` 文件中提取的仓库管理系统 (WMS) 数据库表结构。该系统包含完整的仓库管理功能，涵盖库存管理、订单处理、收货发货、计费等核心业务流程。

## 数据库对象统计

根据对原始 SQL 文件的分析，系统包含以下主要对象：

### 核心业务表 (35+ 张)

| 类别 | 表数量 | 说明 |
|------|--------|------|
| 库存管理 | 8 | SKU、LOT、LOC、LOTATTRIBUTE 等 |
| 订单管理 | 6 | ORDERS、ORDERDETAIL、PICKHEADER 等 |
| 收货管理 | 4 | RECEIPT、RECEIPTDETAIL、PO 等 |
| 发货管理 | 4 | WAVE、CARTONIZATION、PALLET 等 |
| 系统管理 | 8 | ALERT、ERRLOG、NSQLCONFIG 等 |
| 计费管理 | 5+ | 各种计费相关表 |

### 其他对象

- **视图 (Views)**: 10+ 个业务视图
- **存储过程 (Stored Procedures)**: 50+ 个业务逻辑处理过程
- **触发器 (Triggers)**: 20+ 个数据完整性触发器
- **索引 (Indexes)**: 100+ 个性能优化索引
- **约束 (Constraints)**: 200+ 个数据约束

## 核心表结构详解

### 1. 库存管理模块

#### SKU 表 (库存单位表)
- **主键**: StorerKey + Sku
- **功能**: 存储商品基本信息
- **关键字段**: 
  - SKU编码、描述、包装信息
  - 重量、体积、分类信息
  - 拣选策略、上架策略
  - 批次标签配置

#### LOT 表 (批次表)
- **主键**: Lot + StorerKey + Sku
- **功能**: 管理库存批次信息
- **关键字段**:
  - 批次号、数量信息
  - 预分配、已分配、已拣选数量
  - 状态、归档信息

#### LOC 表 (位置表)
- **主键**: Loc
- **功能**: 管理仓库位置信息
- **关键字段**:
  - 位置编码、类型、标志
  - 容量限制、坐标信息
  - 拣选区域、上架区域

#### LOTxLOCxID 表 (批次位置标识关联表)
- **主键**: Lot + Loc + Id + StorerKey + Sku
- **功能**: 管理具体的库存分布
- **关键字段**:
  - 实际数量、预期数量
  - 分配数量、拣选数量
  - 移动状态

### 2. 订单管理模块

#### ORDERS 表 (订单表)
- **主键**: OrderKey
- **功能**: 存储订单头信息
- **关键字段**:
  - 订单号、外部订单号
  - 客户信息、承运商信息
  - 优先级、状态、路线信息

#### ORDERDETAIL 表 (订单明细表)
- **主键**: OrderKey + OrderLineNumber
- **功能**: 存储订单行项目信息
- **关键字段**:
  - SKU信息、数量信息
  - 分配状态、拣选状态
  - 价格信息、批次要求

#### PICKHEADER 表 (拣选头表)
- **主键**: PickHeaderKey
- **功能**: 管理拣选任务
- **关键字段**:
  - 波次号、订单号
  - 拣选类型、状态
  - 优先级、区域

#### PICKDETAIL 表 (拣选明细表)
- **主键**: PickDetailKey
- **功能**: 管理具体拣选任务
- **关键字段**:
  - 箱号、位置、批次
  - 拣选数量、移动数量
  - 拣选方法、状态

### 3. 收货管理模块

#### RECEIPT 表 (收货表)
- **主键**: ReceiptKey
- **功能**: 管理收货单信息
- **关键字段**:
  - 收货单号、PO号
  - 承运商信息、容器信息
  - 收货日期、状态

#### RECEIPTDETAIL 表 (收货明细表)
- **主键**: ReceiptKey + ReceiptLineNumber
- **功能**: 管理收货明细
- **关键字段**:
  - SKU信息、数量信息
  - 目标位置、批次信息
  - 质量状态、价格信息

#### PO 表 (采购订单表)
- **主键**: POKey
- **功能**: 管理采购订单
- **关键字段**:
  - PO号、供应商信息
  - 运输信息、贸易条款
  - 状态、备注

### 4. 发货管理模块

#### WAVE 表 (波次表)
- **主键**: WaveKey
- **功能**: 管理拣选波次
- **关键字段**:
  - 波次类型、描述
  - 分派方法配置
  - 状态信息

#### CARTONIZATION 表 (装箱表)
- **主键**: CartonizationKey
- **功能**: 管理装箱策略
- **关键字段**:
  - 箱型信息、容量限制
  - 使用顺序、重量限制

#### PALLET 表 (托盘表)
- **主键**: PalletKey
- **功能**: 管理托盘信息
- **关键字段**:
  - 托盘号、状态
  - 存储商信息

### 5. 系统管理模块

#### ALERT 表 (警报表)
- **主键**: AlertKey
- **功能**: 系统警报管理
- **关键字段**:
  - 模块名、警报消息
  - 严重级别、状态
  - 解决方案

#### ERRLOG 表 (错误日志表)
- **功能**: 系统错误日志
- **关键字段**:
  - 错误ID、模块名
  - 错误文本、系统状态

#### NSQLCONFIG 表 (系统配置表)
- **主键**: ConfigKey
- **功能**: 系统参数配置
- **关键字段**:
  - 配置键、当前值
  - 默认值、描述

## 数据关系

### 主要外键关系

1. **SKU → STORER**: 商品属于存储商
2. **LOT → SKU**: 批次属于特定商品
3. **LOTxLOCxID → LOT, LOC, ID**: 库存分布关系
4. **ORDERDETAIL → ORDERS**: 订单明细属于订单
5. **PICKDETAIL → ORDERDETAIL**: 拣选任务来源于订单
6. **RECEIPTDETAIL → RECEIPT**: 收货明细属于收货单

### 业务流程关系

```
收货流程: PO → RECEIPT → RECEIPTDETAIL → LOTxLOCxID
发货流程: ORDERS → ORDERDETAIL → WAVE → PICKDETAIL → 出库
库存管理: SKU → LOT → LOTxLOCxID (实时库存)
```

## 性能优化

### 关键索引

1. **LOTxLOCxID**: 按位置、SKU、ID建立索引
2. **PICKDETAIL**: 按状态、波次、订单建立索引
3. **ITRN**: 按事务类型、日期建立索引
4. **SKU**: 按SKU编码建立索引

### 分区策略建议

1. **ITRN**: 按月份分区（历史事务数据）
2. **PICKDETAIL**: 按状态分区（活跃/历史数据）
3. **RECEIPTDETAIL**: 按年份分区

## 数据完整性

### 约束类型

1. **主键约束**: 确保记录唯一性
2. **外键约束**: 维护数据关系完整性
3. **检查约束**: 验证数据有效性（如数量 >= 0）
4. **默认约束**: 提供字段默认值

### 触发器功能

1. **数据同步**: 自动更新相关表数据
2. **业务规则**: 强制执行复杂业务逻辑
3. **审计跟踪**: 记录数据变更历史
4. **数据验证**: 跨表数据一致性检查

## 扩展性考虑

### 水平扩展

1. **多仓库支持**: 通过 Facility 字段区分
2. **多租户支持**: 通过 StorerKey 实现
3. **历史数据归档**: 通过 ArchiveCop 字段管理

### 垂直扩展

1. **自定义字段**: SUSR1-5, BUSR1-5 等用户字段
2. **批次属性**: LOTTABLE01-05 灵活配置
3. **扩展表**: 支持业务特定需求

---

**文档版本**: 1.0  
**最后更新**: 2025-06-18  
**维护者**: 数据库管理团队
