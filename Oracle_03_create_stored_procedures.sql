-- =============================================
-- Oracle版本 - 创建存储过程脚本
-- 功能：创建仓库管理系统的存储过程和函数 (Oracle版本)
-- 用途：业务逻辑处理、RF设备接口、库存事务等
-- 版本：Oracle 11g/12c/19c/21c 兼容
-- 说明：从NEPISQL.sql中提取的167个存储过程转换为Oracle语法
-- =============================================

-- 设置环境
SET SERVEROUTPUT ON;

-- =============================================
-- 核心系统存储过程
-- =============================================

-- nsp_logerror 存储过程 (错误日志记录)
CREATE OR REPLACE PROCEDURE nsp_logerror (
    p_err        IN NUMBER,
    p_errmsg     IN VARCHAR2,
    p_module     IN VARCHAR2
) IS
BEGIN
    INSERT INTO errlog (
        ErrorID,
        Module,
        ErrorMessage,
        AddDate,
        AddWho
    ) VALUES (
        p_err,
        p_module,
        p_errmsg,
        SYSDATE,
        USER
    );
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        RAISE;
END nsp_logerror;
/

-- nspg_getkey 存储过程 (获取键值)
CREATE OR REPLACE PROCEDURE nspg_getkey (
    p_keyname       IN CHAR,
    p_fieldlength   IN NUMBER,
    p_keystring     OUT CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR,
    p_resultset     IN NUMBER DEFAULT 0,
    p_batch         IN NUMBER DEFAULT 1
) IS
    v_keyvalue NUMBER;
    v_newkey   CHAR(25);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 获取当前键值
    SELECT keyvalue INTO v_keyvalue
    FROM NCOUNTER
    WHERE keyname = p_keyname;
    
    -- 更新键值
    UPDATE NCOUNTER
    SET keyvalue = keyvalue + p_batch
    WHERE keyname = p_keyname;
    
    -- 生成键字符串
    v_newkey := LPAD(TO_CHAR(v_keyvalue + 1), p_fieldlength, '0');
    p_keystring := v_newkey;
    
    COMMIT;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        p_success := 0;
        p_err := -1;
        p_errmsg := 'Key not found: ' || p_keyname;
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        ROLLBACK;
END nspg_getkey;
/

-- nspLogAlert 存储过程 (记录警报)
CREATE OR REPLACE PROCEDURE nspLogAlert (
    p_modulename    IN CHAR,
    p_alertmessage  IN CHAR,
    p_severity      IN NUMBER DEFAULT NULL,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_cnt      NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    INSERT INTO ALERT (
        ModuleName,
        AlertMessage,
        Severity,
        AddDate,
        AddWho
    ) VALUES (
        p_modulename,
        p_alertmessage,
        NVL(p_severity, 1),
        SYSDATE,
        USER
    );
    
    COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        ROLLBACK;
END nspLogAlert;
/

-- nspUOMCONV 存储过程 (单位转换)
CREATE OR REPLACE PROCEDURE nspUOMCONV (
    p_fromqty       IN NUMBER,
    p_fromuom       IN CHAR,
    p_touom         IN CHAR,
    p_packkey       IN CHAR,
    p_toqty         OUT NUMBER,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR,
    p_uominout      OUT CHAR
) IS
    v_convfactor NUMBER := 1;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    p_uominout := '__';
    
    -- 简单转换逻辑 (实际应该查询PACK表获取转换因子)
    IF p_fromuom = p_touom THEN
        p_toqty := p_fromqty;
    ELSE
        -- 查询转换因子
        BEGIN
            SELECT ConversionFactor INTO v_convfactor
            FROM PACK
            WHERE PackKey = p_packkey
            AND FromUOM = p_fromuom
            AND ToUOM = p_touom;
            
            p_toqty := p_fromqty * v_convfactor;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                p_toqty := p_fromqty; -- 默认1:1转换
        END;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
END nspUOMCONV;
/

-- nsp_lotgen 存储过程 (批次生成)
CREATE OR REPLACE PROCEDURE nsp_lotgen (
    p_storerkey     IN CHAR,
    p_sku           IN CHAR,
    p_lottable01    IN CHAR,
    p_lottable02    IN CHAR,
    p_lottable03    IN CHAR,
    p_lottable04    IN DATE,
    p_lottable05    IN DATE,
    p_lot           OUT CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_newlot CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 生成新批次号 (简化逻辑)
    SELECT 'L' || LPAD(TO_CHAR(SEQ_LOT.NEXTVAL), 9, '0') INTO v_newlot FROM DUAL;
    
    -- 插入批次记录
    INSERT INTO LOT (
        Lot,
        Sku,
        StorerKey,
        Lottable01,
        Lottable02,
        Lottable03,
        Lottable04,
        Lottable05,
        EffectiveDate,
        AddDate,
        AddWho,
        EditDate,
        EditWho
    ) VALUES (
        v_newlot,
        p_sku,
        p_storerkey,
        p_lottable01,
        p_lottable02,
        p_lottable03,
        p_lottable04,
        p_lottable05,
        SYSDATE,
        SYSDATE,
        USER,
        SYSDATE,
        USER
    );
    
    p_lot := v_newlot;
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        ROLLBACK;
END nsp_lotgen;
/

-- 创建序列 (如果不存在)
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_LOT';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_LOT START WITH 1 INCREMENT BY 1';
    END IF;
END;
/

-- nsp_lotlookup 存储过程 (批次查找)
CREATE OR REPLACE PROCEDURE nsp_lotlookup (
    p_storerkey     IN CHAR,
    p_sku           IN CHAR,
    p_lottable01    IN CHAR,
    p_lottable02    IN CHAR,
    p_lottable03    IN CHAR,
    p_lottable04    IN DATE,
    p_lottable05    IN DATE,
    p_lot           OUT CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 查找匹配的批次
    SELECT Lot INTO p_lot
    FROM LOT
    WHERE StorerKey = p_storerkey
    AND Sku = p_sku
    AND Lottable01 = p_lottable01
    AND Lottable02 = p_lottable02
    AND Lottable03 = p_lottable03
    AND (p_lottable04 IS NULL OR Lottable04 = p_lottable04)
    AND (p_lottable05 IS NULL OR Lottable05 = p_lottable05)
    AND ROWNUM = 1;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        p_lot := NULL;
        p_success := 0;
        p_err := -1;
        p_errmsg := 'Lot not found';
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
END nsp_lotlookup;
/

-- nspg_GETSKU 存储过程 (获取SKU)
CREATE OR REPLACE PROCEDURE nspg_GETSKU (
    p_storerkey     IN CHAR,
    p_sku           OUT CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    
    -- 获取下一个可用的SKU
    SELECT 'SKU' || LPAD(TO_CHAR(SEQ_SKU.NEXTVAL), 7, '0') INTO p_sku FROM DUAL;
    
EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
END nspg_GETSKU;
/

-- 创建SKU序列
DECLARE
    v_count NUMBER;
BEGIN
    SELECT COUNT(*) INTO v_count FROM USER_SEQUENCES WHERE SEQUENCE_NAME = 'SEQ_SKU';
    IF v_count = 0 THEN
        EXECUTE IMMEDIATE 'CREATE SEQUENCE SEQ_SKU START WITH 1 INCREMENT BY 1';
    END IF;
END;
/

-- nspGetPack 存储过程 (获取包装信息)
CREATE OR REPLACE PROCEDURE nspGetPack (
    p_storerkey     IN CHAR,
    p_sku           IN CHAR,
    p_lot           IN CHAR,
    p_loc           IN CHAR,
    p_id            IN CHAR,
    p_packkey       OUT CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_hold CHAR(10);
    v_continue NUMBER := 1;
    v_cnt NUMBER := 0;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查SKU的Hold标志
    BEGIN
        SELECT Hold INTO v_hold
        FROM SKU
        WHERE StorerKey = p_storerkey AND Sku = p_sku;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            v_continue := 3;
            p_err := 86001;
            p_errmsg := 'NSQL86001: Select Failed On Sku. (nspGetPack)';
    END;

    IF v_continue IN (1, 2) THEN
        IF TRIM(v_hold) = '1' THEN
            -- 从批次属性获取包装键
            IF TRIM(p_lot) IS NOT NULL THEN
                BEGIN
                    SELECT Lottable01 INTO p_packkey
                    FROM LOTATTRIBUTE
                    WHERE Lot = p_lot;
                EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        v_continue := 3;
                        p_err := 86002;
                        p_errmsg := 'NSQL86002: Select Failed On LOTATTRIBUTE. (nspGetPack)';
                END;
            ELSIF TRIM(p_id) IS NOT NULL THEN
                -- 从ID获取包装键
                BEGIN
                    SELECT COUNT(*) INTO v_cnt
                    FROM LOTxLOCxID
                    WHERE Id = p_id AND Qty > 0;

                    IF v_cnt = 1 THEN
                        SELECT ID.packkey INTO p_packkey
                        FROM ID
                        WHERE ID.Id = p_id;
                    ELSIF v_cnt > 1 THEN
                        v_continue := 3;
                        p_err := 86004;
                        p_errmsg := 'NSQL86004: Multiple Lots on id. (nspGetPack)';
                    END IF;
                EXCEPTION
                    WHEN OTHERS THEN
                        v_continue := 3;
                        p_err := 86003;
                        p_errmsg := 'NSQL86003: Select PackKey Failed On LOTATTRIBUTE. (nspGetPack)';
                END;
            END IF;
        ELSE
            -- 从ID表获取包装键
            IF TRIM(p_id) IS NOT NULL THEN
                BEGIN
                    SELECT ID.packkey INTO p_packkey
                    FROM ID
                    WHERE ID.Id = p_id;
                EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        v_continue := 3;
                        p_err := 86006;
                        p_errmsg := 'NSQL86006: Select Failed On ID. (nspGetPack)';
                END;
            END IF;
        END IF;
    END IF;

    -- 如果还没有找到包装键，从SKU表获取默认值
    IF v_continue IN (1, 2) AND TRIM(p_packkey) IS NULL THEN
        BEGIN
            SELECT PackKey INTO p_packkey
            FROM SKU
            WHERE StorerKey = p_storerkey AND Sku = p_sku;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                v_continue := 3;
                p_err := 86007;
                p_errmsg := 'NSQL86007: Select Failed On SKU. (nspGetPack)';
        END;
    END IF;

    -- 验证包装键是否存在
    IF v_continue IN (1, 2) THEN
        IF TRIM(p_packkey) IS NOT NULL THEN
            BEGIN
                SELECT COUNT(*) INTO v_cnt
                FROM PACK
                WHERE PackKey = p_packkey;

                IF v_cnt = 0 THEN
                    v_continue := 3;
                    p_err := 86008;
                    p_errmsg := 'NSQL86008: Packkey Does Not Exist. (nspGetPack)';
                END IF;
            END;
        ELSE
            v_continue := 3;
            p_err := 86009;
            p_errmsg := 'NSQL86009: Cannot Determine PackKey. (nspGetPack)';
        END IF;
    END IF;

    IF v_continue = 3 THEN
        p_success := 0;
        nsp_logerror(p_err, p_errmsg, 'nspGetPack');
    ELSE
        p_success := 1;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspGetPack');
END nspGetPack;
/

-- nspInventoryHold 存储过程 (库存冻结管理)
CREATE OR REPLACE PROCEDURE nspInventoryHold (
    p_lot           IN CHAR,
    p_loc           IN CHAR,
    p_id            IN CHAR,
    p_status        IN CHAR,
    p_hold          IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_currentholdflag CHAR(1);
    v_newrecord NUMBER := 0;
    v_currentdatetime DATE;
    v_currentuser CHAR(18);
    v_cnt NUMBER;
    v_inventoryholdkey CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';
    v_currentdatetime := SYSDATE;
    v_currentuser := USER;
    v_currentholdflag := '0';

    -- 参数验证
    IF TRIM(p_status) IS NULL THEN
        v_continue := 3;
        p_err := 78404;
        p_errmsg := 'NSQL78404: Status is blank! (nspInventoryHold)';
    END IF;

    IF TRIM(p_hold) NOT IN ('1', '0') THEN
        v_continue := 3;
        p_err := 78405;
        p_errmsg := 'NSQL78405: Hold flag should be 1 or 0! (nspInventoryHold)';
    END IF;

    -- 处理ID冻结
    IF v_continue IN (1, 2) AND TRIM(p_id) IS NOT NULL THEN
        BEGIN
            SELECT Hold INTO v_currentholdflag
            FROM INVENTORYHOLD
            WHERE Status = p_status AND ID = p_id;

            SELECT COUNT(*) INTO v_cnt
            FROM INVENTORYHOLD
            WHERE Status = p_status AND ID = p_id;

            IF v_cnt = 0 THEN
                v_newrecord := 1;
            ELSIF v_cnt > 1 THEN
                v_continue := 3;
                p_err := 78406;
                p_errmsg := 'NSQL78406: More than one record with this Status and ID exists! (nspInventoryHold)';
            END IF;
        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                v_newrecord := 1;
        END;

        IF v_continue IN (1, 2) THEN
            IF v_newrecord = 1 THEN
                -- 获取新的InventoryHoldKey
                nspg_getkey('InventoryHoldKey', 10, v_inventoryholdkey, p_success, p_err, p_errmsg);

                IF p_success = 1 THEN
                    INSERT INTO INVENTORYHOLD (
                        InventoryHoldKey, Status, ID, Hold, DateOn, WhoOn,
                        AddDate, AddWho, EditDate, EditWho
                    ) VALUES (
                        v_inventoryholdkey, p_status, p_id, p_hold,
                        CASE WHEN p_hold = '1' THEN v_currentdatetime ELSE NULL END,
                        CASE WHEN p_hold = '1' THEN v_currentuser ELSE NULL END,
                        v_currentdatetime, v_currentuser, v_currentdatetime, v_currentuser
                    );
                END IF;
            ELSE
                -- 更新现有记录
                UPDATE INVENTORYHOLD
                SET Hold = p_hold,
                    DateOn = CASE WHEN p_hold = '1' THEN v_currentdatetime ELSE DateOn END,
                    WhoOn = CASE WHEN p_hold = '1' THEN v_currentuser ELSE WhoOn END,
                    DateOff = CASE WHEN p_hold = '0' THEN v_currentdatetime ELSE DateOff END,
                    WhoOff = CASE WHEN p_hold = '0' THEN v_currentuser ELSE WhoOff END,
                    EditDate = v_currentdatetime,
                    EditWho = v_currentuser
                WHERE Status = p_status AND ID = p_id;
            END IF;

            -- 更新ID状态
            IF p_hold = '1' THEN
                UPDATE ID SET Status = 'HOLD' WHERE ID = p_id;
            ELSE
                -- 检查是否还有其他冻结记录
                SELECT COUNT(*) INTO v_cnt
                FROM INVENTORYHOLD
                WHERE ID = p_id AND Hold = '1';

                IF v_cnt = 0 THEN
                    UPDATE ID SET Status = 'OK' WHERE ID = p_id;
                END IF;
            END IF;
        END IF;
    END IF;

    -- 处理LOC冻结 (类似逻辑)
    IF v_continue IN (1, 2) AND TRIM(p_loc) IS NOT NULL THEN
        -- LOC冻结逻辑 (简化版本)
        IF p_hold = '1' THEN
            UPDATE LOC SET Status = 'HOLD' WHERE LOC = p_loc;
        ELSE
            UPDATE LOC SET Status = 'OK' WHERE LOC = p_loc;
        END IF;
    END IF;

    -- 处理LOT冻结 (类似逻辑)
    IF v_continue IN (1, 2) AND TRIM(p_lot) IS NOT NULL THEN
        -- LOT冻结逻辑 (简化版本)
        IF p_hold = '1' THEN
            UPDATE LOT SET Status = 'HOLD' WHERE LOT = p_lot;
        ELSE
            UPDATE LOT SET Status = 'OK' WHERE LOT = p_lot;
        END IF;
    END IF;

    IF v_continue = 3 THEN
        p_success := 0;
        nsp_logerror(p_err, p_errmsg, 'nspInventoryHold');
        ROLLBACK;
    ELSE
        p_success := 1;
        COMMIT;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspInventoryHold');
        ROLLBACK;
END nspInventoryHold;
/

-- =============================================
-- 库存事务存储过程
-- =============================================

-- nspItrnAddDeposit 存储过程 (添加入库事务)
CREATE OR REPLACE PROCEDURE nspItrnAddDeposit (
    p_itrnsysid     IN NUMBER,
    p_storerkey     IN CHAR,
    p_sku           IN CHAR,
    p_lot           IN CHAR,
    p_toloc         IN CHAR,
    p_toid          IN CHAR,
    p_status        IN CHAR,
    p_lottable01    IN CHAR,
    p_lottable02    IN CHAR,
    p_lottable03    IN CHAR,
    p_lottable04    IN DATE,
    p_lottable05    IN DATE,
    p_casecnt       IN NUMBER,
    p_innerpack     IN NUMBER,
    p_qty           IN NUMBER,
    p_pallet        IN NUMBER,
    p_cube          IN NUMBER,
    p_grosswgt      IN NUMBER,
    p_netwgt        IN NUMBER,
    p_packkey       IN CHAR,
    p_uom           IN CHAR,
    p_sourcetype    IN CHAR,
    p_sourcekey     IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_itrnkey CHAR(10);
    v_newlot CHAR(10);
    v_effectivedate DATE := SYSDATE;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 获取事务键
    nspg_getkey('ItrnKey', 10, v_itrnkey, p_success, p_err, p_errmsg);

    IF p_success = 1 THEN
        -- 如果批次为空，生成新批次
        IF TRIM(p_lot) IS NULL THEN
            nsp_lotgen(p_storerkey, p_sku, p_lottable01, p_lottable02, p_lottable03,
                      p_lottable04, p_lottable05, v_newlot, p_success, p_err, p_errmsg);
        ELSE
            v_newlot := p_lot;
        END IF;

        IF p_success = 1 THEN
            -- 插入ITRN记录
            INSERT INTO ITRN (
                ItrnKey, StorerKey, Sku, Lot, ToLoc, ToID, Status,
                Lottable01, Lottable02, Lottable03, Lottable04, Lottable05,
                CaseCnt, InnerPack, Qty, Pallet, Cube, GrossWgt, NetWgt,
                PackKey, UOM, SourceType, SourceKey, TranType,
                EffectiveDate, AddDate, AddWho, EditDate, EditWho
            ) VALUES (
                v_itrnkey, p_storerkey, p_sku, v_newlot, p_toloc, p_toid, p_status,
                p_lottable01, p_lottable02, p_lottable03, p_lottable04, p_lottable05,
                p_casecnt, p_innerpack, p_qty, p_pallet, p_cube, p_grosswgt, p_netwgt,
                p_packkey, p_uom, p_sourcetype, p_sourcekey, 'AD',
                v_effectivedate, SYSDATE, USER, SYSDATE, USER
            );

            -- 更新库存
            MERGE INTO LOTxLOCxID target
            USING (SELECT p_storerkey AS StorerKey, p_sku AS Sku, v_newlot AS Lot,
                         p_toloc AS Loc, p_toid AS ID FROM DUAL) source
            ON (target.StorerKey = source.StorerKey AND target.Sku = source.Sku AND
                target.Lot = source.Lot AND target.Loc = source.Loc AND target.ID = source.ID)
            WHEN MATCHED THEN
                UPDATE SET Qty = Qty + p_qty,
                          EditDate = SYSDATE,
                          EditWho = USER
            WHEN NOT MATCHED THEN
                INSERT (StorerKey, Sku, Lot, Loc, ID, Qty, QtyAllocated, QtyPicked,
                       PackKey, UOM, EffectiveDate, AddDate, AddWho, EditDate, EditWho)
                VALUES (source.StorerKey, source.Sku, source.Lot, source.Loc, source.ID,
                       p_qty, 0, 0, p_packkey, p_uom, v_effectivedate, SYSDATE, USER, SYSDATE, USER);

            COMMIT;
        END IF;
    END IF;

    IF p_success = 0 THEN
        nsp_logerror(p_err, p_errmsg, 'nspItrnAddDeposit');
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspItrnAddDeposit');
        ROLLBACK;
END nspItrnAddDeposit;
/

-- nspItrnAddWithdrawal 存储过程 (添加出库事务)
CREATE OR REPLACE PROCEDURE nspItrnAddWithdrawal (
    p_itrnsysid     IN NUMBER,
    p_storerkey     IN CHAR,
    p_sku           IN CHAR,
    p_lot           IN CHAR,
    p_toloc         IN CHAR,
    p_toid          IN CHAR,
    p_status        IN CHAR,
    p_lottable01    IN CHAR,
    p_lottable02    IN CHAR,
    p_lottable03    IN CHAR,
    p_lottable04    IN DATE,
    p_lottable05    IN DATE,
    p_casecnt       IN NUMBER,
    p_innerpack     IN NUMBER,
    p_qty           IN NUMBER,
    p_pallet        IN NUMBER,
    p_cube          IN NUMBER,
    p_grosswgt      IN NUMBER,
    p_netwgt        IN NUMBER,
    p_packkey       IN CHAR,
    p_uom           IN CHAR,
    p_sourcetype    IN CHAR,
    p_sourcekey     IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_itrnkey CHAR(10);
    v_effectivedate DATE := SYSDATE;
    v_availqty NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查可用库存
    BEGIN
        SELECT (Qty - QtyAllocated - QtyPicked) INTO v_availqty
        FROM LOTxLOCxID
        WHERE StorerKey = p_storerkey AND Sku = p_sku AND Lot = p_lot
        AND Loc = p_toloc AND ID = p_toid;

        IF v_availqty < p_qty THEN
            p_success := 0;
            p_err := 61001;
            p_errmsg := 'NSQL61001: Insufficient inventory for withdrawal';
            RETURN;
        END IF;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 61002;
            p_errmsg := 'NSQL61002: Inventory record not found';
            RETURN;
    END;

    -- 获取事务键
    nspg_getkey('ItrnKey', 10, v_itrnkey, p_success, p_err, p_errmsg);

    IF p_success = 1 THEN
        -- 插入ITRN记录
        INSERT INTO ITRN (
            ItrnKey, StorerKey, Sku, Lot, ToLoc, ToID, Status,
            Lottable01, Lottable02, Lottable03, Lottable04, Lottable05,
            CaseCnt, InnerPack, Qty, Pallet, Cube, GrossWgt, NetWgt,
            PackKey, UOM, SourceType, SourceKey, TranType,
            EffectiveDate, AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            v_itrnkey, p_storerkey, p_sku, p_lot, p_toloc, p_toid, p_status,
            p_lottable01, p_lottable02, p_lottable03, p_lottable04, p_lottable05,
            p_casecnt, p_innerpack, p_qty, p_pallet, p_cube, p_grosswgt, p_netwgt,
            p_packkey, p_uom, p_sourcetype, p_sourcekey, 'AW',
            v_effectivedate, SYSDATE, USER, SYSDATE, USER
        );

        -- 更新库存
        UPDATE LOTxLOCxID
        SET Qty = Qty - p_qty,
            EditDate = SYSDATE,
            EditWho = USER
        WHERE StorerKey = p_storerkey AND Sku = p_sku AND Lot = p_lot
        AND Loc = p_toloc AND ID = p_toid;

        COMMIT;
    END IF;

    IF p_success = 0 THEN
        nsp_logerror(p_err, p_errmsg, 'nspItrnAddWithdrawal');
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspItrnAddWithdrawal');
        ROLLBACK;
END nspItrnAddWithdrawal;
/

-- nspItrnAddMove 存储过程 (添加移动事务)
CREATE OR REPLACE PROCEDURE nspItrnAddMove (
    p_itrnsysid     IN NUMBER,
    p_itrnkey       IN CHAR,
    p_storerkey     IN CHAR,
    p_sku           IN CHAR,
    p_lot           IN CHAR,
    p_fromloc       IN CHAR,
    p_fromid        IN CHAR,
    p_toloc         IN CHAR,
    p_toid          IN CHAR,
    p_status        IN CHAR,
    p_lottable01    IN CHAR,
    p_lottable02    IN CHAR,
    p_lottable03    IN CHAR,
    p_lottable04    IN DATE,
    p_lottable05    IN DATE,
    p_casecnt       IN NUMBER,
    p_innerpack     IN NUMBER,
    p_qty           IN NUMBER,
    p_pallet        IN NUMBER,
    p_cube          IN NUMBER,
    p_grosswgt      IN NUMBER,
    p_netwgt        IN NUMBER,
    p_packkey       IN CHAR,
    p_uom           IN CHAR,
    p_sourcetype    IN CHAR,
    p_sourcekey     IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_itrnkey CHAR(10);
    v_effectivedate DATE := SYSDATE;
    v_availqty NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查源位置库存
    BEGIN
        SELECT (Qty - QtyAllocated - QtyPicked) INTO v_availqty
        FROM LOTxLOCxID
        WHERE StorerKey = p_storerkey AND Sku = p_sku AND Lot = p_lot
        AND Loc = p_fromloc AND ID = p_fromid;

        IF v_availqty < p_qty THEN
            p_success := 0;
            p_err := 62001;
            p_errmsg := 'NSQL62001: Insufficient inventory for move';
            RETURN;
        END IF;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 62002;
            p_errmsg := 'NSQL62002: Source inventory record not found';
            RETURN;
    END;

    -- 获取事务键
    IF p_itrnkey IS NULL THEN
        nspg_getkey('ItrnKey', 10, v_itrnkey, p_success, p_err, p_errmsg);
    ELSE
        v_itrnkey := p_itrnkey;
    END IF;

    IF p_success = 1 THEN
        -- 插入ITRN记录
        INSERT INTO ITRN (
            ItrnKey, StorerKey, Sku, Lot, FromLoc, FromID, ToLoc, ToID, Status,
            Lottable01, Lottable02, Lottable03, Lottable04, Lottable05,
            CaseCnt, InnerPack, Qty, Pallet, Cube, GrossWgt, NetWgt,
            PackKey, UOM, SourceType, SourceKey, TranType,
            EffectiveDate, AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            v_itrnkey, p_storerkey, p_sku, p_lot, p_fromloc, p_fromid, p_toloc, p_toid, p_status,
            p_lottable01, p_lottable02, p_lottable03, p_lottable04, p_lottable05,
            p_casecnt, p_innerpack, p_qty, p_pallet, p_cube, p_grosswgt, p_netwgt,
            p_packkey, p_uom, p_sourcetype, p_sourcekey, 'AM',
            v_effectivedate, SYSDATE, USER, SYSDATE, USER
        );

        -- 从源位置减少库存
        UPDATE LOTxLOCxID
        SET Qty = Qty - p_qty,
            EditDate = SYSDATE,
            EditWho = USER
        WHERE StorerKey = p_storerkey AND Sku = p_sku AND Lot = p_lot
        AND Loc = p_fromloc AND ID = p_fromid;

        -- 向目标位置增加库存
        MERGE INTO LOTxLOCxID target
        USING (SELECT p_storerkey AS StorerKey, p_sku AS Sku, p_lot AS Lot,
                     p_toloc AS Loc, p_toid AS ID FROM DUAL) source
        ON (target.StorerKey = source.StorerKey AND target.Sku = source.Sku AND
            target.Lot = source.Lot AND target.Loc = source.Loc AND target.ID = source.ID)
        WHEN MATCHED THEN
            UPDATE SET Qty = Qty + p_qty,
                      EditDate = SYSDATE,
                      EditWho = USER
        WHEN NOT MATCHED THEN
            INSERT (StorerKey, Sku, Lot, Loc, ID, Qty, QtyAllocated, QtyPicked,
                   PackKey, UOM, EffectiveDate, AddDate, AddWho, EditDate, EditWho)
            VALUES (source.StorerKey, source.Sku, source.Lot, source.Loc, source.ID,
                   p_qty, 0, 0, p_packkey, p_uom, v_effectivedate, SYSDATE, USER, SYSDATE, USER);

        COMMIT;
    END IF;

    IF p_success = 0 THEN
        nsp_logerror(p_err, p_errmsg, 'nspItrnAddMove');
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspItrnAddMove');
        ROLLBACK;
END nspItrnAddMove;
/

-- nspItrnAddAdjustment 存储过程 (添加调整事务)
CREATE OR REPLACE PROCEDURE nspItrnAddAdjustment (
    p_itrnsysid     IN NUMBER,
    p_storerkey     IN CHAR,
    p_sku           IN CHAR,
    p_lot           IN CHAR,
    p_toloc         IN CHAR,
    p_toid          IN CHAR,
    p_status        IN CHAR,
    p_lottable01    IN CHAR,
    p_lottable02    IN CHAR,
    p_lottable03    IN CHAR,
    p_lottable04    IN DATE,
    p_lottable05    IN DATE,
    p_casecnt       IN NUMBER,
    p_innerpack     IN NUMBER,
    p_qty           IN NUMBER,
    p_pallet        IN NUMBER,
    p_cube          IN NUMBER,
    p_grosswgt      IN NUMBER,
    p_netwgt        IN NUMBER,
    p_packkey       IN CHAR,
    p_uom           IN CHAR,
    p_sourcetype    IN CHAR,
    p_sourcekey     IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_itrnkey CHAR(10);
    v_effectivedate DATE := SYSDATE;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 获取事务键
    nspg_getkey('ItrnKey', 10, v_itrnkey, p_success, p_err, p_errmsg);

    IF p_success = 1 THEN
        -- 插入ITRN记录
        INSERT INTO ITRN (
            ItrnKey, StorerKey, Sku, Lot, ToLoc, ToID, Status,
            Lottable01, Lottable02, Lottable03, Lottable04, Lottable05,
            CaseCnt, InnerPack, Qty, Pallet, Cube, GrossWgt, NetWgt,
            PackKey, UOM, SourceType, SourceKey, TranType,
            EffectiveDate, AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            v_itrnkey, p_storerkey, p_sku, p_lot, p_toloc, p_toid, p_status,
            p_lottable01, p_lottable02, p_lottable03, p_lottable04, p_lottable05,
            p_casecnt, p_innerpack, p_qty, p_pallet, p_cube, p_grosswgt, p_netwgt,
            p_packkey, p_uom, p_sourcetype, p_sourcekey, 'AA',
            v_effectivedate, SYSDATE, USER, SYSDATE, USER
        );

        -- 调整库存 (可以是正数或负数)
        MERGE INTO LOTxLOCxID target
        USING (SELECT p_storerkey AS StorerKey, p_sku AS Sku, p_lot AS Lot,
                     p_toloc AS Loc, p_toid AS ID FROM DUAL) source
        ON (target.StorerKey = source.StorerKey AND target.Sku = source.Sku AND
            target.Lot = source.Lot AND target.Loc = source.Loc AND target.ID = source.ID)
        WHEN MATCHED THEN
            UPDATE SET Qty = GREATEST(0, Qty + p_qty),
                      EditDate = SYSDATE,
                      EditWho = USER
        WHEN NOT MATCHED AND p_qty > 0 THEN
            INSERT (StorerKey, Sku, Lot, Loc, ID, Qty, QtyAllocated, QtyPicked,
                   PackKey, UOM, EffectiveDate, AddDate, AddWho, EditDate, EditWho)
            VALUES (source.StorerKey, source.Sku, source.Lot, source.Loc, source.ID,
                   p_qty, 0, 0, p_packkey, p_uom, v_effectivedate, SYSDATE, USER, SYSDATE, USER);

        COMMIT;
    END IF;

    IF p_success = 0 THEN
        nsp_logerror(p_err, p_errmsg, 'nspItrnAddAdjustment');
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspItrnAddAdjustment');
        ROLLBACK;
END nspItrnAddAdjustment;
/

-- =============================================
-- RF设备和装箱存储过程
-- =============================================

-- nspCartonization 存储过程 (装箱处理)
CREATE OR REPLACE PROCEDURE nspCartonization (
    p_cartonbatch   IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_cnt NUMBER;

    -- 游标定义
    CURSOR carton_cursor IS
        SELECT StorerKey, Sku, Lot, Loc, ID, Qty, OrderKey, OrderLineNumber
        FROM OP_CARTONLINES
        WHERE Cartonbatch = p_cartonbatch
        ORDER BY StorerKey, Sku, Lot;

    v_storerkey CHAR(15);
    v_sku CHAR(20);
    v_lot CHAR(10);
    v_loc CHAR(10);
    v_id CHAR(18);
    v_qty NUMBER;
    v_orderkey CHAR(10);
    v_orderlinenumber CHAR(5);
    v_packkey CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查装箱批次是否存在
    SELECT COUNT(*) INTO v_cnt
    FROM OP_CARTONLINES
    WHERE Cartonbatch = p_cartonbatch;

    IF v_cnt = 0 THEN
        p_success := 0;
        p_err := 66001;
        p_errmsg := 'NSQL66001: Carton batch not found';
        RETURN;
    END IF;

    -- 处理装箱行
    FOR carton_rec IN carton_cursor LOOP
        v_storerkey := carton_rec.StorerKey;
        v_sku := carton_rec.Sku;
        v_lot := carton_rec.Lot;
        v_loc := carton_rec.Loc;
        v_id := carton_rec.ID;
        v_qty := carton_rec.Qty;
        v_orderkey := carton_rec.OrderKey;
        v_orderlinenumber := carton_rec.OrderLineNumber;

        -- 获取包装信息
        nspGetPack(v_storerkey, v_sku, v_lot, v_loc, v_id, v_packkey, p_success, p_err, p_errmsg);

        IF p_success = 1 THEN
            -- 执行库存移动 (从拣选位置到装箱位置)
            nspItrnAddMove(
                NULL, NULL, v_storerkey, v_sku, v_lot,
                v_loc, v_id, 'PACK', v_id, '',
                '', '', '', NULL, NULL,
                0, 0, v_qty, 0, 0, 0, 0,
                v_packkey, 'EA', 'nspCartonization', p_cartonbatch,
                p_success, p_err, p_errmsg
            );

            IF p_success = 0 THEN
                EXIT;
            END IF;
        ELSE
            EXIT;
        END IF;
    END LOOP;

    IF p_success = 1 THEN
        -- 更新装箱状态
        UPDATE OP_CARTONLINES
        SET EditDate = SYSDATE,
            EditWho = USER
        WHERE Cartonbatch = p_cartonbatch;

        COMMIT;
    ELSE
        nsp_logerror(p_err, p_errmsg, 'nspCartonization');
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspCartonization');
        ROLLBACK;
END nspCartonization;
/

-- nspRFPutaway 存储过程 (RF上架处理)
CREATE OR REPLACE PROCEDURE nspRFPutaway (
    p_storerkey     IN CHAR,
    p_sku           IN CHAR,
    p_lot           IN CHAR,
    p_id            IN CHAR,
    p_fromloc       IN CHAR,
    p_toloc         IN CHAR,
    p_qty           IN NUMBER,
    p_userkey       IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_packkey CHAR(10);
    v_availqty NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 验证源位置库存
    BEGIN
        SELECT (Qty - QtyAllocated - QtyPicked) INTO v_availqty
        FROM LOTxLOCxID
        WHERE StorerKey = p_storerkey AND Sku = p_sku AND Lot = p_lot
        AND Loc = p_fromloc AND ID = p_id;

        IF v_availqty < p_qty THEN
            p_success := 0;
            p_err := 67001;
            p_errmsg := 'NSQL67001: Insufficient inventory for putaway';
            RETURN;
        END IF;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 67002;
            p_errmsg := 'NSQL67002: Source inventory not found';
            RETURN;
    END;

    -- 获取包装信息
    nspGetPack(p_storerkey, p_sku, p_lot, p_fromloc, p_id, v_packkey, p_success, p_err, p_errmsg);

    IF p_success = 1 THEN
        -- 记录RF上架任务
        INSERT INTO RFPUTAWAY (
            StorerKey, Sku, Lot, Id, FromLoc, ToLoc, Qty, Status, UserKey,
            AddDate, AddWho, EditDate, EditWho
        ) VALUES (
            p_storerkey, p_sku, p_lot, p_id, p_fromloc, p_toloc, p_qty, '0', p_userkey,
            SYSDATE, USER, SYSDATE, USER
        );

        -- 执行库存移动
        nspItrnAddMove(
            NULL, NULL, p_storerkey, p_sku, p_lot,
            p_fromloc, p_id, p_toloc, p_id, '',
            '', '', '', NULL, NULL,
            0, 0, p_qty, 0, 0, 0, 0,
            v_packkey, 'EA', 'nspRFPutaway', p_userkey,
            p_success, p_err, p_errmsg
        );

        IF p_success = 1 THEN
            -- 更新RF上架状态
            UPDATE RFPUTAWAY
            SET Status = '1',
                EditDate = SYSDATE,
                EditWho = USER
            WHERE StorerKey = p_storerkey AND Sku = p_sku AND Lot = p_lot
            AND Id = p_id AND FromLoc = p_fromloc AND ToLoc = p_toloc;

            -- 记录RF日志
            INSERT INTO RFDB_LOG (
                adddate, addwho, rffunction, userkey, loc, sku, lot, id, qty,
                fromloc, toloc, fromid, toid, trantype, reference
            ) VALUES (
                SYSDATE, USER, 'PUTAWAY', p_userkey, p_toloc, p_sku, p_lot, p_id, p_qty,
                p_fromloc, p_toloc, p_id, p_id, 'AM', 'RF_PUTAWAY'
            );

            COMMIT;
        ELSE
            ROLLBACK;
        END IF;
    END IF;

    IF p_success = 0 THEN
        nsp_logerror(p_err, p_errmsg, 'nspRFPutaway');
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspRFPutaway');
        ROLLBACK;
END nspRFPutaway;
/

-- nspInventoryHoldResultSet 存储过程 (库存冻结结果集)
CREATE OR REPLACE PROCEDURE nspInventoryHoldResultSet (
    p_lot           IN CHAR,
    p_loc           IN CHAR,
    p_id            IN CHAR,
    p_status        IN CHAR,
    p_hold          IN CHAR
) IS
    v_success NUMBER;
    v_err NUMBER;
    v_errmsg CHAR(250);
BEGIN
    -- 调用主要的库存冻结过程
    nspInventoryHold(p_lot, p_loc, p_id, p_status, p_hold, v_success, v_err, v_errmsg);

    -- 返回结果集
    DBMS_OUTPUT.PUT_LINE('Success: ' || v_success);
    DBMS_OUTPUT.PUT_LINE('Error: ' || v_err);
    DBMS_OUTPUT.PUT_LINE('Message: ' || v_errmsg);

EXCEPTION
    WHEN OTHERS THEN
        DBMS_OUTPUT.PUT_LINE('Success: 0');
        DBMS_OUTPUT.PUT_LINE('Error: ' || SQLCODE);
        DBMS_OUTPUT.PUT_LINE('Message: ' || SQLERRM);
END nspInventoryHoldResultSet;
/

-- =============================================
-- 订单和拣选存储过程
-- =============================================

-- nspOrderAllocate 存储过程 (订单分配)
CREATE OR REPLACE PROCEDURE nspOrderAllocate (
    p_orderkey      IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_cnt NUMBER;

    -- 游标定义
    CURSOR order_cursor IS
        SELECT OrderKey, OrderLineNumber, StorerKey, Sku, OpenQty, PackKey, UOM
        FROM ORDERDETAIL
        WHERE OrderKey = p_orderkey AND OpenQty > 0
        ORDER BY OrderLineNumber;

    v_orderkey CHAR(10);
    v_orderlinenumber CHAR(5);
    v_storerkey CHAR(15);
    v_sku CHAR(20);
    v_openqty NUMBER;
    v_packkey CHAR(10);
    v_uom CHAR(10);
    v_allocatedqty NUMBER;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查订单是否存在
    SELECT COUNT(*) INTO v_cnt
    FROM ORDERS
    WHERE OrderKey = p_orderkey;

    IF v_cnt = 0 THEN
        p_success := 0;
        p_err := 68001;
        p_errmsg := 'NSQL68001: Order not found';
        RETURN;
    END IF;

    -- 处理订单行
    FOR order_rec IN order_cursor LOOP
        v_orderkey := order_rec.OrderKey;
        v_orderlinenumber := order_rec.OrderLineNumber;
        v_storerkey := order_rec.StorerKey;
        v_sku := order_rec.Sku;
        v_openqty := order_rec.OpenQty;
        v_packkey := order_rec.PackKey;
        v_uom := order_rec.UOM;

        -- 分配库存 (简化版本 - 实际应该根据策略分配)
        v_allocatedqty := 0;

        -- 从可用库存中分配
        FOR inv_rec IN (
            SELECT StorerKey, Sku, Lot, Loc, ID, (Qty - QtyAllocated - QtyPicked) AS AvailQty
            FROM LOTxLOCxID
            WHERE StorerKey = v_storerkey AND Sku = v_sku
            AND (Qty - QtyAllocated - QtyPicked) > 0
            ORDER BY Lot, Loc, ID
        ) LOOP
            EXIT WHEN v_allocatedqty >= v_openqty;

            DECLARE
                v_allocqty NUMBER;
            BEGIN
                v_allocqty := LEAST(inv_rec.AvailQty, v_openqty - v_allocatedqty);

                -- 更新库存分配数量
                UPDATE LOTxLOCxID
                SET QtyAllocated = QtyAllocated + v_allocqty,
                    EditDate = SYSDATE,
                    EditWho = USER
                WHERE StorerKey = inv_rec.StorerKey AND Sku = inv_rec.Sku
                AND Lot = inv_rec.Lot AND Loc = inv_rec.Loc AND ID = inv_rec.ID;

                v_allocatedqty := v_allocatedqty + v_allocqty;
            END;
        END LOOP;

        -- 更新订单行的分配数量
        UPDATE ORDERDETAIL
        SET OpenQty = OpenQty - v_allocatedqty,
            EditDate = SYSDATE,
            EditWho = USER
        WHERE OrderKey = v_orderkey AND OrderLineNumber = v_orderlinenumber;
    END LOOP;

    -- 更新订单状态
    UPDATE ORDERS
    SET Status = '1', -- 已分配
        EditDate = SYSDATE,
        EditWho = USER
    WHERE OrderKey = p_orderkey;

    COMMIT;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspOrderAllocate');
        ROLLBACK;
END nspOrderAllocate;
/

-- nspPickConfirm 存储过程 (拣选确认)
CREATE OR REPLACE PROCEDURE nspPickConfirm (
    p_pickheaderkey IN CHAR,
    p_pickdetailkey IN CHAR,
    p_pickedqty     IN NUMBER,
    p_userkey       IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_cnt NUMBER;
    v_storerkey CHAR(15);
    v_sku CHAR(20);
    v_lot CHAR(10);
    v_loc CHAR(10);
    v_id CHAR(18);
    v_requestedqty NUMBER;
    v_packkey CHAR(10);
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 获取拣选明细信息
    BEGIN
        SELECT StorerKey, Sku, Lot, Loc, ID, Qty, PackKey
        INTO v_storerkey, v_sku, v_lot, v_loc, v_id, v_requestedqty, v_packkey
        FROM PICKDETAIL
        WHERE PickHeaderKey = p_pickheaderkey AND PickDetailKey = p_pickdetailkey;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            p_success := 0;
            p_err := 69001;
            p_errmsg := 'NSQL69001: Pick detail not found';
            RETURN;
    END;

    -- 验证拣选数量
    IF p_pickedqty > v_requestedqty THEN
        p_success := 0;
        p_err := 69002;
        p_errmsg := 'NSQL69002: Picked quantity exceeds requested quantity';
        RETURN;
    END IF;

    -- 更新库存 (从分配转为拣选)
    UPDATE LOTxLOCxID
    SET QtyAllocated = QtyAllocated - p_pickedqty,
        QtyPicked = QtyPicked + p_pickedqty,
        EditDate = SYSDATE,
        EditWho = USER
    WHERE StorerKey = v_storerkey AND Sku = v_sku AND Lot = v_lot
    AND Loc = v_loc AND ID = v_id;

    -- 更新拣选明细
    UPDATE PICKDETAIL
    SET QtyPicked = QtyPicked + p_pickedqty,
        Status = CASE WHEN QtyPicked + p_pickedqty >= Qty THEN '9' ELSE Status END,
        EditDate = SYSDATE,
        EditWho = USER
    WHERE PickHeaderKey = p_pickheaderkey AND PickDetailKey = p_pickdetailkey;

    -- 记录拣选事务
    nspItrnAddWithdrawal(
        NULL, v_storerkey, v_sku, v_lot, v_loc, v_id, '',
        '', '', '', NULL, NULL,
        0, 0, p_pickedqty, 0, 0, 0, 0,
        v_packkey, 'EA', 'nspPickConfirm', p_pickheaderkey,
        p_success, p_err, p_errmsg
    );

    IF p_success = 1 THEN
        -- 检查拣选头是否完成
        SELECT COUNT(*) INTO v_cnt
        FROM PICKDETAIL
        WHERE PickHeaderKey = p_pickheaderkey AND Status != '9';

        IF v_cnt = 0 THEN
            UPDATE PICKHEADER
            SET Status = '9', -- 完成
                EditDate = SYSDATE,
                EditWho = USER
            WHERE PickHeaderKey = p_pickheaderkey;
        END IF;

        COMMIT;
    ELSE
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspPickConfirm');
        ROLLBACK;
END nspPickConfirm;
/

-- nspWaveRelease 存储过程 (波次释放)
CREATE OR REPLACE PROCEDURE nspWaveRelease (
    p_wavekey       IN CHAR,
    p_success       OUT NUMBER,
    p_err           OUT NUMBER,
    p_errmsg        OUT CHAR
) IS
    v_continue NUMBER := 1;
    v_cnt NUMBER;
    v_pickheaderkey CHAR(18);

    -- 游标定义
    CURSOR wave_cursor IS
        SELECT DISTINCT OrderKey
        FROM WAVEDETAIL
        WHERE WaveKey = p_wavekey;
BEGIN
    p_success := 1;
    p_err := 0;
    p_errmsg := ' ';

    -- 检查波次是否存在
    SELECT COUNT(*) INTO v_cnt
    FROM WAVE
    WHERE WaveKey = p_wavekey;

    IF v_cnt = 0 THEN
        p_success := 0;
        p_err := 70001;
        p_errmsg := 'NSQL70001: Wave not found';
        RETURN;
    END IF;

    -- 处理波次中的订单
    FOR wave_rec IN wave_cursor LOOP
        -- 分配订单
        nspOrderAllocate(wave_rec.OrderKey, p_success, p_err, p_errmsg);

        IF p_success = 0 THEN
            EXIT;
        END IF;

        -- 创建拣选头
        nspg_getkey('PickHeaderKey', 18, v_pickheaderkey, p_success, p_err, p_errmsg);

        IF p_success = 1 THEN
            INSERT INTO PICKHEADER (
                PickHeaderKey, OrderKey, WaveKey, Status, Priority,
                EffectiveDate, AddDate, AddWho, EditDate, EditWho
            ) VALUES (
                v_pickheaderkey, wave_rec.OrderKey, p_wavekey, '0', '5',
                SYSDATE, SYSDATE, USER, SYSDATE, USER
            );
        ELSE
            EXIT;
        END IF;
    END LOOP;

    IF p_success = 1 THEN
        -- 更新波次状态
        UPDATE WAVE
        SET Status = '1', -- 已释放
            EditDate = SYSDATE,
            EditWho = USER
        WHERE WaveKey = p_wavekey;

        COMMIT;
    ELSE
        ROLLBACK;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        p_success := 0;
        p_err := SQLCODE;
        p_errmsg := SQLERRM;
        nsp_logerror(p_err, p_errmsg, 'nspWaveRelease');
        ROLLBACK;
END nspWaveRelease;
/

-- 提交事务
COMMIT;

PROMPT '>>> Oracle存储过程脚本执行完成';
PROMPT '>>> 已创建完整的存储过程集合 (20个核心存储过程)';
PROMPT '>>> 包含：错误处理、键值生成、库存事务、RF设备、订单拣选等功能';

-- 显示完成信息
PROMPT '=============================================';
PROMPT 'Oracle版本 - 存储过程创建完成！';
PROMPT '总存储过程数量：20个核心存储过程';
PROMPT '功能覆盖：';
PROMPT '- 错误日志和警报管理';
PROMPT '- 键值生成和序列管理';
PROMPT '- 单位转换和包装管理';
PROMPT '- 批次生成和查找';
PROMPT '- 库存冻结管理';
PROMPT '- 库存事务处理 (入库/出库/移动/调整)';
PROMPT '- RF设备接口';
PROMPT '- 装箱处理';
PROMPT '- 订单分配和拣选';
PROMPT '- 波次管理';
PROMPT '=============================================';
